package controllers

import (
	"GoShip/models"
	"GoShip/services"
	"encoding/json"
	"fmt"
	"os/exec"
	"path/filepath"
	"strings"

	beego "github.com/beego/beego/v2/server/web"
)

// CondaController Conda环境管理控制器
type CondaController struct {
	beego.Controller
}

// EnvironmentCheckRequest 环境检测请求
type EnvironmentCheckRequest struct {
	EnvName    string `json:"env_name"`
	DeployType string `json:"deploy_type"` // "local" 或 "remote"
	HostID     *int   `json:"host_id"`     // 远程主机ID，本地部署时为nil
}

// EnvironmentCheckResponse 环境检测响应
type EnvironmentCheckResponse struct {
	Exists         bool   `json:"exists"`
	PythonVersion  string `json:"python_version,omitempty"`
	PackagesCount  int    `json:"packages_count,omitempty"`
	Location       string `json:"location,omitempty"`
	DeployType     string `json:"deploy_type"`     // 部署类型
	HostInfo       string `json:"host_info,omitempty"` // 主机信息（远程时显示）
}

// EnvironmentInfo 环境详细信息
type EnvironmentInfo struct {
	Name          string   `json:"name"`
	PythonVersion string   `json:"python_version"`
	Location      string   `json:"location"`
	Packages      []string `json:"packages"`
}

// CondaEnvList conda环境列表结构
type CondaEnvList struct {
	Envs []string `json:"envs"`
}

// CondaPackage conda包信息结构
type CondaPackage struct {
	Name    string `json:"name"`
	Version string `json:"version"`
}

// CheckEnvironment 检测conda环境是否存在
func (c *CondaController) CheckEnvironment() {
	var req EnvironmentCheckRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		c.Data["json"] = map[string]interface{}{
			"error": "Invalid request format",
		}
		c.Ctx.Output.SetStatus(400)
		c.ServeJSON()
		return
	}

	if req.EnvName == "" {
		c.Data["json"] = map[string]interface{}{
			"error": "Environment name is required",
		}
		c.Ctx.Output.SetStatus(400)
		c.ServeJSON()
		return
	}

	if req.DeployType == "" {
		req.DeployType = "local" // 默认本地部署
	}

	// 验证远程部署时必须提供主机ID
	if req.DeployType == "remote" && req.HostID == nil {
		c.Data["json"] = map[string]interface{}{
			"error": "Host ID is required for remote deployment",
		}
		c.Ctx.Output.SetStatus(400)
		c.ServeJSON()
		return
	}

	var exists bool
	var location string
	var pythonVersion string
	var packagesCount int
	var hostInfo string
	var err error

	if req.DeployType == "local" {
		// 本地环境检测
		exists, location, err = c.checkCondaEnvironmentExists(req.EnvName)
		if err != nil {
			c.Data["json"] = map[string]interface{}{
				"error": fmt.Sprintf("Failed to check local environment: %v", err),
			}
			c.Ctx.Output.SetStatus(500)
			c.ServeJSON()
			return
		}

		if exists {
			pythonVersion, _ = c.getCondaEnvironmentPythonVersion(req.EnvName)
			packagesCount, _ = c.getCondaEnvironmentPackagesCount(req.EnvName)
		}
		hostInfo = "本地主机"
	} else {
		// 远程环境检测
		exists, location, pythonVersion, packagesCount, hostInfo, err = c.checkRemoteCondaEnvironment(req.EnvName, *req.HostID)
		if err != nil {
			c.Data["json"] = map[string]interface{}{
				"error": fmt.Sprintf("Failed to check remote environment: %v", err),
			}
			c.Ctx.Output.SetStatus(500)
			c.ServeJSON()
			return
		}
	}

	response := EnvironmentCheckResponse{
		Exists:        exists,
		Location:      location,
		PythonVersion: pythonVersion,
		PackagesCount: packagesCount,
		DeployType:    req.DeployType,
		HostInfo:      hostInfo,
	}

	c.Data["json"] = response
	c.ServeJSON()
}

// ListEnvironments 获取所有conda环境列表
func (c *CondaController) ListEnvironments() {
	cmd := exec.Command("conda", "env", "list", "--json")
	output, err := cmd.Output()
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"error": fmt.Sprintf("Failed to list environments: %v", err),
		}
		c.Ctx.Output.SetStatus(500)
		c.ServeJSON()
		return
	}

	var envList CondaEnvList
	if err := json.Unmarshal(output, &envList); err != nil {
		c.Data["json"] = map[string]interface{}{
			"error": fmt.Sprintf("Failed to parse environment list: %v", err),
		}
		c.Ctx.Output.SetStatus(500)
		c.ServeJSON()
		return
	}

	// 提取环境名称
	var envNames []string
	for _, envPath := range envList.Envs {
		envName := filepath.Base(envPath)
		if envName != "" && envName != "base" {
			envNames = append(envNames, envName)
		}
	}

	c.Data["json"] = envNames
	c.ServeJSON()
}

// GetEnvironmentInfo 获取指定环境的详细信息
func (c *CondaController) GetEnvironmentInfo() {
	envName := c.Ctx.Input.Param(":env_name")
	if envName == "" {
		c.Data["json"] = map[string]interface{}{
			"error": "Environment name is required",
		}
		c.Ctx.Output.SetStatus(400)
		c.ServeJSON()
		return
	}

	// 检查环境是否存在
	exists, location, err := c.checkCondaEnvironmentExists(envName)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"error": fmt.Sprintf("Failed to check environment: %v", err),
		}
		c.Ctx.Output.SetStatus(500)
		c.ServeJSON()
		return
	}

	if !exists {
		c.Data["json"] = map[string]interface{}{
			"error": fmt.Sprintf("Environment '%s' not found", envName),
		}
		c.Ctx.Output.SetStatus(404)
		c.ServeJSON()
		return
	}

	// 获取环境详细信息
	pythonVersion, _ := c.getCondaEnvironmentPythonVersion(envName)
	packages, _ := c.getCondaEnvironmentPackages(envName)

	info := EnvironmentInfo{
		Name:          envName,
		PythonVersion: pythonVersion,
		Location:      location,
		Packages:      packages,
	}

	c.Data["json"] = info
	c.ServeJSON()
}

// checkCondaEnvironmentExists 检查conda环境是否存在
func (c *CondaController) checkCondaEnvironmentExists(envName string) (bool, string, error) {
	cmd := exec.Command("conda", "env", "list", "--json")
	output, err := cmd.Output()
	if err != nil {
		return false, "", err
	}

	var envList CondaEnvList
	if err := json.Unmarshal(output, &envList); err != nil {
		// 如果JSON解析失败，回退到文本解析
		return strings.Contains(string(output), envName), "", nil
	}

	// 检查环境是否存在
	for _, envPath := range envList.Envs {
		if filepath.Base(envPath) == envName {
			return true, envPath, nil
		}
	}

	return false, "", nil
}

// getCondaEnvironmentPythonVersion 获取conda环境中的Python版本
func (c *CondaController) getCondaEnvironmentPythonVersion(envName string) (string, error) {
	cmd := exec.Command("conda", "list", "-n", envName, "python", "--json")
	output, err := cmd.Output()
	if err != nil {
		return "", err
	}

	var packages []CondaPackage
	if err := json.Unmarshal(output, &packages); err != nil {
		return "", err
	}

	for _, pkg := range packages {
		if pkg.Name == "python" {
			return pkg.Version, nil
		}
	}

	return "", fmt.Errorf("Python package not found in environment")
}

// getCondaEnvironmentPackagesCount 获取conda环境中的包数量
func (c *CondaController) getCondaEnvironmentPackagesCount(envName string) (int, error) {
	cmd := exec.Command("conda", "list", "-n", envName, "--json")
	output, err := cmd.Output()
	if err != nil {
		return 0, err
	}

	var packages []CondaPackage
	if err := json.Unmarshal(output, &packages); err != nil {
		return 0, err
	}

	return len(packages), nil
}

// getCondaEnvironmentPackages 获取conda环境中的所有包
func (c *CondaController) getCondaEnvironmentPackages(envName string) ([]string, error) {
	cmd := exec.Command("conda", "list", "-n", envName, "--json")
	output, err := cmd.Output()
	if err != nil {
		return nil, err
	}

	var packages []CondaPackage
	if err := json.Unmarshal(output, &packages); err != nil {
		return nil, err
	}

	var packageList []string
	for _, pkg := range packages {
		packageList = append(packageList, fmt.Sprintf("%s==%s", pkg.Name, pkg.Version))
	}

	return packageList, nil
}

// checkRemoteCondaEnvironment 检测远程主机上的conda环境
func (c *CondaController) checkRemoteCondaEnvironment(envName string, hostID int) (bool, string, string, int, string, error) {
	// 获取主机信息
	host, err := models.GetHostByID(int64(hostID))
	if err != nil {
		return false, "", "", 0, "", fmt.Errorf("获取主机信息失败: %v", err)
	}

	if host == nil {
		return false, "", "", 0, "", fmt.Errorf("主机不存在")
	}

	// 创建SSH客户端
	client, err := services.CreateSSHClient(host)
	if err != nil {
		return false, "", "", 0, "", fmt.Errorf("创建SSH连接失败: %v", err)
	}
	defer client.Close()

	hostInfo := fmt.Sprintf("%s (%s:%d)", host.Name, host.IP, host.Port)

	// 检查conda环境是否存在
	checkCmd := "conda env list --json"
	output, err := services.ExecuteSSHCommand(client, checkCmd)
	if err != nil {
		return false, "", "", 0, hostInfo, fmt.Errorf("执行conda命令失败: %v", err)
	}

	var envList CondaEnvList
	if err := json.Unmarshal([]byte(output), &envList); err != nil {
		// 如果JSON解析失败，回退到文本解析
		exists := strings.Contains(output, envName)
		return exists, "", "", 0, hostInfo, nil
	}

	// 检查环境是否存在
	var envPath string
	exists := false
	for _, path := range envList.Envs {
		if filepath.Base(path) == envName {
			exists = true
			envPath = path
			break
		}
	}

	if !exists {
		return false, "", "", 0, hostInfo, nil
	}

	// 获取Python版本
	pythonVersion := ""
	pythonCmd := fmt.Sprintf("conda list -n %s python --json", envName)
	if pythonOutput, err := services.ExecuteSSHCommand(client, pythonCmd); err == nil {
		var packages []CondaPackage
		if err := json.Unmarshal([]byte(pythonOutput), &packages); err == nil {
			for _, pkg := range packages {
				if pkg.Name == "python" {
					pythonVersion = pkg.Version
					break
				}
			}
		}
	}

	// 获取包数量
	packagesCount := 0
	packagesCmd := fmt.Sprintf("conda list -n %s --json", envName)
	if packagesOutput, err := services.ExecuteSSHCommand(client, packagesCmd); err == nil {
		var packages []CondaPackage
		if err := json.Unmarshal([]byte(packagesOutput), &packages); err == nil {
			packagesCount = len(packages)
		}
	}

	return true, envPath, pythonVersion, packagesCount, hostInfo, nil
}
