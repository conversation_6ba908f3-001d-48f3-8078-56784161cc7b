package controllers

import (
	"GoShip/config"
	"GoShip/services"
	"fmt"
	"net/http"
	"strconv"
	"time"

	beego "github.com/beego/beego/v2/server/web"
	"github.com/gorilla/websocket"
)

type WebSocketController struct {
	beego.Controller
}

// DeployLogs WebSocket 连接处理
func (c *WebSocketController) DeployLogs() {
	// 禁用模板渲染
	c.EnableRender = false

	// 获取项目ID
	projectID, err := strconv.ParseInt(c.Ctx.Input.Param(":id"), 10, 64)
	if err != nil {
		c.Ctx.Output.SetStatus(400)
		c.Ctx.Output.Body([]byte("无效的项目ID"))
		return
	}

	// 获取WebSocket配置
	wsConfig := config.DefaultWebSocketConfig

	// 升级 HTTP 连接为 WebSocket
	upgrader := websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
		// 使用配置中的握手超时时间
		HandshakeTimeout: wsConfig.HandshakeTimeout,
		// 设置读写缓冲区大小
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
	}

	ws, err := upgrader.Upgrade(c.Ctx.ResponseWriter, c.Ctx.Request, nil)
	if err != nil {
		fmt.Printf("升级WebSocket连接失败: %v\n", err)
		return
	}
	defer ws.Close()

	// 使用配置中的超时时间
	readTimeout := wsConfig.ReadTimeout
	writeTimeout := wsConfig.WriteTimeout

	ws.SetReadDeadline(time.Now().Add(readTimeout))
	ws.SetWriteDeadline(time.Now().Add(writeTimeout))

	// 设置 Pong 处理器 - 当收到客户端的pong响应时重置读取超时
	ws.SetPongHandler(func(string) error {
		fmt.Printf("收到项目 %d 的 pong 响应\n", projectID)
		ws.SetReadDeadline(time.Now().Add(readTimeout))
		return nil
	})

	// 添加连接到管理器
	services.WSManager.AddConnection(projectID, ws)
	defer services.WSManager.RemoveConnection(projectID, ws)

	// 启动心跳 goroutine - 使用配置中的心跳间隔
	heartbeatTicker := time.NewTicker(wsConfig.HeartbeatInterval)
	defer heartbeatTicker.Stop()

	go func() {
		for {
			select {
			case <-heartbeatTicker.C:
				ws.SetWriteDeadline(time.Now().Add(writeTimeout))
				if err := ws.WriteMessage(websocket.PingMessage, []byte("heartbeat")); err != nil {
					fmt.Printf("发送心跳失败，项目ID: %d, 错误: %v\n", projectID, err)
					return
				}
				fmt.Printf("发送心跳到项目 %d\n", projectID)
			}
		}
	}()

	fmt.Printf("WebSocket连接已建立，项目ID: %d\n", projectID)

	// 保持连接活跃 - 处理客户端消息
	for {
		messageType, message, err := ws.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure, websocket.CloseNormalClosure) {
				fmt.Printf("WebSocket连接异常关闭，项目ID: %d, 错误: %v\n", projectID, err)
			} else {
				fmt.Printf("WebSocket连接正常关闭，项目ID: %d\n", projectID)
			}
			break
		}

		// 重置读取超时
		ws.SetReadDeadline(time.Now().Add(readTimeout))

		// 处理客户端消息（如果需要）
		if messageType == websocket.TextMessage {
			fmt.Printf("收到项目 %d 的消息: %s\n", projectID, string(message))
		}
	}
}
