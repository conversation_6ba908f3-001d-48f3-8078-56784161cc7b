package controllers

import (
	"GoShip/models"
	"GoShip/services"
	"encoding/json"
	"fmt"
	"os/exec"
	"strings"

	beego "github.com/beego/beego/v2/server/web"
	"golang.org/x/crypto/ssh"
)

// PythonController Python相关控制器
type PythonController struct {
	beego.Controller
}

// CheckCondaEnvironmentRequest 检查Conda环境请求
type CheckCondaEnvironmentRequest struct {
	CondaEnvName string `json:"conda_env_name"`
	HostID       *int64 `json:"host_id"`
	DeployType   string `json:"deploy_type"`
}

// CheckCondaEnvironmentResponse 检查Conda环境响应
type CheckCondaEnvironmentResponse struct {
	Success       bool   `json:"success"`
	Exists        bool   `json:"exists"`
	PythonVersion string `json:"python_version,omitempty"`
	Message       string `json:"message,omitempty"`
}

// CheckCondaEnvironment 检查Conda环境是否存在
func (c *PythonController) CheckCondaEnvironment() {
	var req CheckCondaEnvironmentRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "参数错误: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	// 验证环境名
	if strings.TrimSpace(req.CondaEnvName) == "" {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "环境名不能为空",
		}
		c.ServeJSON()
		return
	}

	var response CheckCondaEnvironmentResponse

	if req.DeployType == "remote" && req.HostID != nil {
		// 远程检测
		response = checkRemoteCondaEnvironment(req.CondaEnvName, *req.HostID)
	} else {
		// 本地检测
		response = checkLocalCondaEnvironment(req.CondaEnvName)
	}

	c.Data["json"] = response
	c.ServeJSON()
}

// checkLocalCondaEnvironment 检查本地Conda环境
func checkLocalCondaEnvironment(envName string) CheckCondaEnvironmentResponse {
	// 执行conda env list命令
	cmd := exec.Command("conda", "env", "list")
	output, err := cmd.Output()
	if err != nil {
		return CheckCondaEnvironmentResponse{
			Success: false,
			Exists:  false,
			Message: "无法执行conda命令，请确保conda已安装",
		}
	}

	// 检查环境是否存在
	lines := strings.Split(string(output), "\n")
	exists := false
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, envName+" ") || strings.HasPrefix(line, envName+"\t") {
			exists = true
			break
		}
	}

	response := CheckCondaEnvironmentResponse{
		Success: true,
		Exists:  exists,
	}

	// 如果环境存在，获取Python版本
	if exists {
		pythonVersion := getLocalPythonVersion(envName)
		response.PythonVersion = pythonVersion
	}

	return response
}

// checkRemoteCondaEnvironment 检查远程Conda环境
func checkRemoteCondaEnvironment(envName string, hostID int64) CheckCondaEnvironmentResponse {
	// 获取主机信息
	host, err := models.GetHostByID(hostID)
	if err != nil {
		return CheckCondaEnvironmentResponse{
			Success: false,
			Exists:  false,
			Message: "主机不存在",
		}
	}

	// 建立SSH连接
	client, err := services.CreateSSHClient(host)
	if err != nil {
		return CheckCondaEnvironmentResponse{
			Success: false,
			Exists:  false,
			Message: "SSH连接失败: " + err.Error(),
		}
	}
	defer client.Close()

	// 执行远程conda env list命令
	output, err := services.ExecuteSSHCommand(client, "conda env list")
	if err != nil {
		return CheckCondaEnvironmentResponse{
			Success: false,
			Exists:  false,
			Message: "远程conda命令执行失败，请确保conda已安装",
		}
	}

	// 检查环境是否存在
	lines := strings.Split(output, "\n")
	exists := false
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, envName+" ") || strings.HasPrefix(line, envName+"\t") {
			exists = true
			break
		}
	}

	response := CheckCondaEnvironmentResponse{
		Success: true,
		Exists:  exists,
	}

	// 如果环境存在，获取Python版本
	if exists {
		pythonVersion := getRemotePythonVersion(client, envName)
		response.PythonVersion = pythonVersion
	}

	return response
}

// getLocalPythonVersion 获取本地Python版本
func getLocalPythonVersion(envName string) string {
	// 使用conda run命令在指定环境中获取Python版本
	cmd := exec.Command("conda", "run", "-n", envName, "python", "--version")
	output, err := cmd.Output()
	if err != nil {
		return ""
	}

	// 解析版本信息 (输出格式: "Python 3.11.5")
	versionStr := strings.TrimSpace(string(output))
	if strings.HasPrefix(versionStr, "Python ") {
		return strings.TrimPrefix(versionStr, "Python ")
	}

	return ""
}

// getRemotePythonVersion 获取远程Python版本
func getRemotePythonVersion(client *ssh.Client, envName string) string {
	// 使用conda run命令在指定环境中获取Python版本
	cmd := fmt.Sprintf("conda run -n %s python --version", envName)
	output, err := services.ExecuteSSHCommand(client, cmd)
	if err != nil {
		return ""
	}

	// 解析版本信息 (输出格式: "Python 3.11.5")
	versionStr := strings.TrimSpace(output)
	if strings.HasPrefix(versionStr, "Python ") {
		return strings.TrimPrefix(versionStr, "Python ")
	}

	return ""
}

// GetCondaEnvironments 获取Conda环境列表
func (c *PythonController) GetCondaEnvironments() {
	c.Data["json"] = map[string]interface{}{
		"success":      true,
		"environments": []string{},
		"message":      "功能开发中",
	}
	c.ServeJSON()
}

// GetPythonVersion 获取Python版本
func (c *PythonController) GetPythonVersion() {
	c.Data["json"] = map[string]interface{}{
		"success": true,
		"version": "",
		"message": "功能开发中",
	}
	c.ServeJSON()
}
