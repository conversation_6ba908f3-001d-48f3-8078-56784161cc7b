package controllers

import (
	"GoShip/models"
	"GoShip/services"
	"encoding/json"
	"fmt"
	"path/filepath"
	"strconv"

	beego "github.com/beego/beego/v2/server/web"
)

type ProjectController struct {
	beego.Controller
}

// CreateProject 创建新项目
func (c *ProjectController) CreateProject() {

	// 解析请求体
	var project models.Project
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &project); err != nil {
		fmt.Printf("解析JSON数据失败: %v\n", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "参数解析失败",
			"error":   err.Error(),
		}
		c.ServeJ<PERSON>()
		return
	}

	// 打印解析后的数据
	fmt.Printf("解析后的项目数据: %+v\n", project)

	// 设置默认值
	if project.DeployType == "" {
		project.DeployType = "local"
	}
	if project.ServerPort == "" {
		project.ServerPort = "22"
	}

	if err := models.CreateProject(&project); err != nil {
		fmt.Printf("创建项目失败: %v\n", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "创建项目失败",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}

	fmt.Printf("创建的项目ID: %d\n", project.ID)

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"message": "创建项目成功",
		"data":    project,
	}
	c.ServeJSON()
}

// GetProjects 获取所有项目
func (c *ProjectController) GetProjects() {
	// 获取分页参数
	page, _ := c.GetInt("page", 1)
	size, _ := c.GetInt("size", 10)

	// 获取筛选参数
	environment := c.GetString("environment", "")
	projectType := c.GetString("project_type", "")
	nameFilter := c.GetString("name_filter", "")

	// 参数验证
	if page < 1 {
		page = 1
	}
	if size < 1 {
		size = 10
	}
	if size > 100 {
		size = 100
	}

	// 获取项目列表
	result, err := models.GetAllProjectsWithFilter(page, size, environment, projectType, nameFilter)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "获取项目列表失败",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"message": "获取项目列表成功",
		"data": map[string]interface{}{
			"total": result.Total,
			"items": result.Items,
			"page":  page,
			"size":  size,
		},
	}
	c.ServeJSON()
}

// GetProject 获取单个项目
func (c *ProjectController) GetProject() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的项目ID",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}

	project, err := models.GetProjectByID(id)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "获取项目失败",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"message": "获取项目成功",
		"data":    project,
	}
	c.ServeJSON()
}

// DeleteProject 删除项目
func (c *ProjectController) DeleteProject() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的项目ID",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}

	if err := models.DeleteProject(id); err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "删除项目失败",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"message": "删除项目成功",
	}
	c.ServeJSON()
}

// BuildProject 只构建项目，不部署
func (c *ProjectController) BuildProject() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的项目ID",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}

	// 立即返回，异步执行
	c.Data["json"] = map[string]interface{}{
		"success": true,
		"message": "构建任务已提交，请稍后通过日志查看进度",
	}
	c.ServeJSON()

	go func(projectID int64) {
		project, err := models.GetProjectByID(projectID)
		if err != nil {
			services.SendDeployLog(projectID, "build", "error", "获取项目失败: "+err.Error())
			return
		}
		projectSrcDir := filepath.Join(models.SourceRoot, project.Name)
		gitService := services.GitService{
			GitURL:      project.GitURL,
			GitUsername: project.GitUsername,
			GitPassword: project.GitPassword,
			GitVersion:  project.GitVersion,
		}
		services.SendDeployLog(project.ID, "build", "info", "开始获取源代码...")
		if err := gitService.CloneOrPull(projectSrcDir); err != nil {
			services.SendDeployLog(project.ID, "build", "error", fmt.Sprintf("获取源代码失败: %v", err))
			return
		}
		services.SendDeployLog(project.ID, "build", "info", "源代码获取成功，开始构建...")
		buildNumber := 1 // 可根据实际需要获取buildNumber
		strategy, err := services.NewBuildStrategy(project)
		if err != nil {
			services.SendDeployLog(project.ID, "build", "error", "不支持的项目类型: "+err.Error())
			return
		}
		if err := strategy.Build(projectSrcDir, buildNumber); err != nil {
			services.SendDeployLog(project.ID, "build", "error", fmt.Sprintf("构建失败: %v", err))
			return
		}
		services.SendDeployLog(project.ID, "build", "info", "构建成功")
	}(id)
}

// 一键部署：先构建再部署
func (c *ProjectController) BuildAndDeploy() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的项目ID",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}

	// 立即返回，异步执行
	c.Data["json"] = map[string]interface{}{
		"success": true,
		"message": "一键部署任务已提交，请稍后通过日志查看进度",
	}
	c.ServeJSON()

	go func(projectID int64) {
		project, err := models.GetProjectByID(projectID)
		if err != nil {
			services.SendDeployLog(projectID, "deploy", "error", "获取项目失败: "+err.Error())
			return
		}

		buildNumber := 1 // 可根据实际需要获取buildNumber
		strategy, err := services.NewBuildStrategy(project)
		if err != nil {
			services.SendDeployLog(project.ID, "build", "error", "不支持的项目类型: "+err.Error())
			return
		}

		// Python项目跳过构建阶段的源码拉取，直接进入部署阶段
		if project.BuildConfig.Language == "python" {
			// Python项目不需要构建阶段，直接调用构建方法（实际上是跳过）
			if err := strategy.Build("", buildNumber); err != nil {
				services.SendDeployLog(project.ID, "build", "error", fmt.Sprintf("构建失败: %v", err))
				return
			}

			services.SendDeployLog(project.ID, "deploy", "info", "Python项目无需产物，直接开始部署...")
			if err := strategy.Deploy("", buildNumber); err != nil {
				services.SendDeployLog(project.ID, "deploy", "error", fmt.Sprintf("部署失败: %v", err))
				return
			}
			services.SendDeployLog(project.ID, "deploy", "success", "部署完成")
			return
		}

		// 编译型语言需要先拉取源码进行构建
		projectSrcDir := filepath.Join(models.SourceRoot, project.Name)
		gitService := services.GitService{
			GitURL:      project.GitURL,
			GitUsername: project.GitUsername,
			GitPassword: project.GitPassword,
			GitVersion:  project.GitVersion,
		}
		services.SendDeployLog(project.ID, "build", "info", "开始获取源代码...")
		if err := gitService.CloneOrPull(projectSrcDir); err != nil {
			services.SendDeployLog(project.ID, "build", "error", fmt.Sprintf("获取源代码失败: %v", err))
			return
		}
		services.SendDeployLog(project.ID, "build", "info", "源代码获取成功，开始构建...")

		if err := strategy.Build(projectSrcDir, buildNumber); err != nil {
			services.SendDeployLog(project.ID, "build", "error", fmt.Sprintf("构建失败: %v", err))
			return
		}
		services.SendDeployLog(project.ID, "build", "info", "构建成功，准备部署...")

		// 编译型语言需要检查产物
		artifact, err := models.GetLatestArtifact(project.ID)
		if err != nil || artifact == nil {
			services.SendDeployLog(project.ID, "deploy", "error", "未找到可部署的产物")
			return
		}
		if err := services.DeployArtifact(project, artifact); err != nil {
			services.SendDeployLog(project.ID, "deploy", "error", fmt.Sprintf("部署失败: %v", err))
			return
		}
		services.SendDeployLog(project.ID, "deploy", "info", "一键部署成功")
	}(id)
}

// UpdateProject 更新项目
func (c *ProjectController) UpdateProject() {
	// 打印请求信息
	fmt.Printf("请求方法: %s\n", c.Ctx.Request.Method)
	fmt.Printf("请求头:\n")
	for k, v := range c.Ctx.Request.Header {
		fmt.Printf("%s: %v\n", k, v)
	}

	// 读取请求体
	body := c.Ctx.Input.CopyBody(1 << 20) // 1MB limit
	if len(body) == 0 {
		fmt.Println("请求体为空")
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "请求体为空",
		}
		c.ServeJSON()
		return
	}

	// 打印原始请求体
	fmt.Printf("原始请求体内容: %s\n", string(body))

	// 解析请求体
	var project models.Project
	if err := json.Unmarshal(body, &project); err != nil {
		fmt.Printf("解析JSON数据失败: %v\n", err)
		fmt.Printf("请求体内容: %s\n", string(body))
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "参数解析失败",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}

	// 检查项目ID
	if project.ID <= 0 {
		fmt.Printf("无效的项目ID: %d\n", project.ID)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的项目ID",
		}
		c.ServeJSON()
		return
	}

	// 打印解析后的数据
	fmt.Printf("解析后的项目数据: %+v\n", project)

	// 更新项目
	if err := models.UpdateProject(&project); err != nil {
		fmt.Printf("更新项目失败: %v\n", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "更新项目失败",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"message": "更新项目成功",
		"data":    project,
	}
	c.ServeJSON()
}
