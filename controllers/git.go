package controllers

import (
	"GoShip/services"
	"encoding/json"
	"fmt"
	beego "github.com/beego/beego/v2/server/web"
)

type GitController struct {
	beego.Controller
}

// GetBranches 获取 Git 分支列表
func (c *GitController) GetBranches() {
	var req struct {
		GitURL      string `json:"git_url"`
		GitUsername string `json:"git_username"`
		GitPassword string `json:"git_password"`
	}

	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		fmt.Printf("JSON 解析错误: %v\n", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的请求参数: " + err.Error(),
		}
		c.Serve<PERSON>()
		return
	}

	gitService := services.NewGitService(req.GitURL, req.GitUsername, req.GitPassword)
	branches, err := gitService.GetBranches()
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "获取分支列表失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"data":    branches,
	}
	c.ServeJSON()
}

// GetTags 获取 Git 标签列表
func (c *GitController) GetTags() {
	var req struct {
		GitURL      string `json:"git_url"`
		GitUsername string `json:"git_username"`
		GitPassword string `json:"git_password"`
	}

	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		fmt.Printf("JSON 解析错误: %v\n", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的请求参数: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	gitService := services.NewGitService(req.GitURL, req.GitUsername, req.GitPassword)
	tags, err := gitService.GetTags()
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "获取标签列表失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"data":    tags,
	}
	c.ServeJSON()
}

// Post 处理 POST 请求
func (c *GitController) Post() {
	var req struct {
		GitURL      string `json:"git_url"`
		GitUsername string `json:"git_username"`
		GitPassword string `json:"git_password"`
	}

	// 打印请求头
	fmt.Printf("Content-Type: %s\n", c.Ctx.Input.Header("Content-Type"))

	// 使用 CopyBody 获取请求体
	body := c.Ctx.Input.CopyBody(1 << 20) // 1MB limit
	fmt.Printf("请求体长度: %d\n", len(body))
	fmt.Printf("收到的请求体: %s\n", string(body))

	if len(body) == 0 {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "请求体为空",
		}
		c.ServeJSON()
		return
	}

	if err := json.Unmarshal(body, &req); err != nil {
		fmt.Printf("JSON解析错误: %v\n", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的请求参数: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	// 打印解析后的结构体
	fmt.Printf("解析后的请求数据: %+v\n", req)

	gitService := services.NewGitService(req.GitURL, req.GitUsername, req.GitPassword)

	// 根据 URL 路径判断是获取分支还是标签
	path := c.Ctx.Request.URL.Path
	var (
		data []string
		err  error
	)

	if path == "/api/git/branches" {
		data, err = gitService.GetBranches()
	} else if path == "/api/git/tags" {
		data, err = gitService.GetTags()
	} else {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的请求路径",
		}
		c.ServeJSON()
		return
	}

	if err != nil {
		var message string
		if path == "/api/git/branches" {
			message = "获取分支列表失败"
		} else {
			message = "获取标签列表失败"
		}

		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": fmt.Sprintf("%s: %v", message, err),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"data":    data,
	}
	c.ServeJSON()
}
