package controllers

import (
	"GoShip/models"
	"GoShip/services"
	"encoding/json"
	"log"
	"strconv"

	beego "github.com/beego/beego/v2/server/web"
)

// HostController 主机管理控制器
type HostController struct {
	beego.Controller
}

// GetHosts 获取主机列表
// @Title 获取主机列表
// @Description 分页获取主机列表，支持按环境筛选和排序
// @Param page query int false "页码，默认1"
// @Param size query int false "每页数量，默认10"
// @Param environment query string false "环境类型：test或prod"
// @Param sort_by query string false "排序字段：name、environment、status、created_at，默认environment"
// @Param sort_order query string false "排序方向：asc或desc，默认asc"
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @router /hosts [get]
func (c *HostController) GetHosts() {
	// 获取分页参数
	page, _ := c.GetInt("page", 1)
	size, _ := c.GetInt("size", 10)
	environment := c.GetString("environment")
	sortBy := c.GetString("sort_by", "environment")
	sortOrder := c.GetString("sort_order", "asc")

	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 10
	}

	// 验证排序字段
	validSortFields := map[string]bool{
		"name":        true,
		"environment": true,
		"status":      true,
		"created_at":  true,
	}
	if !validSortFields[sortBy] {
		sortBy = "environment"
	}

	// 验证排序方向
	if sortOrder != "asc" && sortOrder != "desc" {
		sortOrder = "asc"
	}

	hosts, total, err := models.GetHostsPaginated(page, size, environment, sortBy, sortOrder)
	if err != nil {
		log.Printf("获取主机列表失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "获取主机列表失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"hosts": hosts,
			"total": total,
			"page":  page,
			"size":  size,
		},
	}
	c.ServeJSON()
}

// GetHost 获取单个主机详情
// @Title 获取主机详情
// @Description 根据ID获取主机详细信息
// @Param id path int true "主机ID"
// @Success 200 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @router /hosts/:id [get]
func (c *HostController) GetHost() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的主机ID",
		}
		c.ServeJSON()
		return
	}

	host, err := models.GetHostByID(id)
	if err != nil {
		log.Printf("获取主机详情失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "获取主机详情失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	if host == nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "主机不存在",
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"data":    host,
	}
	c.ServeJSON()
}

// CreateHost 创建主机
// @Title 创建主机
// @Description 创建新的主机配置
// @Param body body models.Host true "主机信息"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @router /hosts [post]
func (c *HostController) CreateHost() {
	var host models.Host
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &host); err != nil {
		log.Printf("解析请求数据失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "请求数据格式错误",
		}
		c.ServeJSON()
		return
	}

	// 验证必填字段
	if host.Name == "" || host.IP == "" || host.Username == "" {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "主机名称、IP地址和用户名不能为空",
		}
		c.ServeJSON()
		return
	}

	// 设置默认值
	if host.Port == 0 {
		host.Port = 22
	}
	if host.Environment == "" {
		host.Environment = "test"
	}
	if host.Status == "" {
		host.Status = "active"
	}
	if host.AuthType == "" {
		host.AuthType = "password"
	}

	// 验证认证信息
	if host.AuthType == "password" && host.Password == "" {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "密码认证方式下密码不能为空",
		}
		c.ServeJSON()
		return
	}

	if host.AuthType == "private_key" {
		if host.PrivateKey == "" {
			c.Data["json"] = map[string]interface{}{
				"success": false,
				"message": "私钥认证方式下私钥不能为空",
			}
			c.ServeJSON()
			return
		}

		// 验证私钥格式
		if err := models.ValidatePrivateKey(host.PrivateKey); err != nil {
			c.Data["json"] = map[string]interface{}{
				"success": false,
				"message": "私钥格式验证失败: " + err.Error(),
			}
			c.ServeJSON()
			return
		}
	}

	// 创建主机
	if err := models.CreateHost(&host); err != nil {
		log.Printf("创建主机失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "创建主机失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"message": "主机创建成功",
		"data":    map[string]interface{}{"id": host.ID},
	}
	c.ServeJSON()
}

// UpdateHost 更新主机
// @Title 更新主机
// @Description 更新主机配置信息
// @Param id path int true "主机ID"
// @Param body body models.Host true "主机信息"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @router /hosts/:id [put]
func (c *HostController) UpdateHost() {
	log.Printf("UpdateHost: 开始更新主机")

	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		log.Printf("UpdateHost: 无效的主机ID: %s", idStr)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的主机ID",
		}
		c.ServeJSON()
		return
	}

	log.Printf("UpdateHost: 解析主机ID成功: %d", id)

	// 不需要预先检查主机是否存在，UpdateHostSafely 方法内部会处理

	var host models.Host
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &host); err != nil {
		log.Printf("UpdateHost: 解析请求数据失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "请求数据格式错误",
		}
		c.ServeJSON()
		return
	}

	// 设置ID
	host.ID = id
	log.Printf("UpdateHost: 解析请求数据成功, 主机名=%s, 认证方式=%s, 密码长度=%d", host.Name, host.AuthType, len(host.Password))

	// 验证必填字段
	if host.Name == "" || host.IP == "" || host.Username == "" {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "主机名称、IP地址和用户名不能为空",
		}
		c.ServeJSON()
		return
	}

	// 验证认证信息
	if host.AuthType == "password" && host.Password == "" {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "密码认证方式下密码不能为空",
		}
		c.ServeJSON()
		return
	}

	if host.AuthType == "private_key" && host.PrivateKey == "" {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "私钥认证方式下私钥不能为空",
		}
		c.ServeJSON()
		return
	}

	// 验证私钥格式
	if host.AuthType == "private_key" {
		if err := models.ValidatePrivateKey(host.PrivateKey); err != nil {
			c.Data["json"] = map[string]interface{}{
				"success": false,
				"message": "私钥格式验证失败: " + err.Error(),
			}
			c.ServeJSON()
			return
		}
	}

	// 使用安全更新方法（不解密原有数据）
	if err := models.UpdateHostSafely(&host); err != nil {
		log.Printf("更新主机失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "更新主机失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"message": "主机更新成功",
	}
	c.ServeJSON()
}

// DeleteHost 删除主机
// @Title 删除主机
// @Description 删除主机配置
// @Param id path int true "主机ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @router /hosts/:id [delete]
func (c *HostController) DeleteHost() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的主机ID",
		}
		c.ServeJSON()
		return
	}

	// 检查主机是否存在
	existingHost, err := models.GetHostByID(id)
	if err != nil {
		log.Printf("查询主机失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "查询主机失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	if existingHost == nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "主机不存在",
		}
		c.ServeJSON()
		return
	}

	// 使用安全删除方法（不解密敏感信息）
	if err := models.DeleteHostSafely(id); err != nil {
		log.Printf("删除主机失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "删除主机失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"message": "主机删除成功",
	}
	c.ServeJSON()
}

// TestHostConnection 测试主机连接
// @Title 测试主机连接
// @Description 测试主机SSH连接是否正常
// @Param id path int true "主机ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @router /hosts/:id/test [post]
func (c *HostController) TestHostConnection() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的主机ID",
		}
		c.ServeJSON()
		return
	}

	// 获取主机信息
	host, err := models.GetHostByID(id)
	if err != nil {
		log.Printf("查询主机失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "查询主机失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	if host == nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "主机不存在",
		}
		c.ServeJSON()
		return
	}

	// 测试连接
	result, err := services.TestSSHConnection(host)
	if err != nil {
		log.Printf("测试主机连接失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "测试连接失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	// 更新测试结果
	if err := models.UpdateHostTestResult(id, result.Success, result.Message); err != nil {
		log.Printf("更新主机测试结果失败: %v", err)
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"data":    result,
	}
	c.ServeJSON()
}

// TestHostConnectionTemp 临时测试主机连接（不保存主机信息）
// @Title 临时测试主机连接
// @Description 测试主机SSH连接是否正常，不保存主机信息
// @Param body body models.Host true "主机配置信息"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @router /hosts/test-temp [post]
func (c *HostController) TestHostConnectionTemp() {
	var host models.Host
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &host); err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "请求数据格式错误",
		}
		c.ServeJSON()
		return
	}

	// 验证必填字段
	if host.IP == "" || host.Username == "" {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "IP地址和用户名不能为空",
		}
		c.ServeJSON()
		return
	}

	// 验证认证信息
	if host.AuthType == "password" && host.Password == "" {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "密码认证方式下密码不能为空",
		}
		c.ServeJSON()
		return
	}

	if host.AuthType == "private_key" && host.PrivateKey == "" {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "私钥认证方式下私钥不能为空",
		}
		c.ServeJSON()
		return
	}

	// 设置默认端口
	if host.Port == 0 {
		host.Port = 22
	}

	// 智能处理密码：如果是编辑模式且传递了主机ID，需要比较密文
	if host.ID > 0 {
		// 获取数据库中的密文
		originalHost, err := models.GetHostByIDRaw(host.ID)
		if err != nil {
			log.Printf("获取原始主机数据失败: %v", err)
			c.Data["json"] = map[string]interface{}{
				"success": false,
				"message": "获取主机数据失败: " + err.Error(),
			}
			c.ServeJSON()
			return
		}

		// 比较密码：如果相同说明是密文，需要解密
		if host.Password == originalHost.Password && originalHost.Password != "" {
			decryptedPassword, err := models.DecryptString(host.Password)
			if err != nil {
				log.Printf("解密密码失败: %v", err)
				c.Data["json"] = map[string]interface{}{
					"success": false,
					"message": "解密密码失败: " + err.Error(),
				}
				c.ServeJSON()
				return
			}
			host.Password = decryptedPassword
		}

		// 比较私钥：如果相同说明是密文，需要解密
		if host.PrivateKey == originalHost.PrivateKey && originalHost.PrivateKey != "" {
			decryptedPrivateKey, err := models.DecryptString(host.PrivateKey)
			if err != nil {
				log.Printf("解密私钥失败: %v", err)
				c.Data["json"] = map[string]interface{}{
					"success": false,
					"message": "解密私钥失败: " + err.Error(),
				}
				c.ServeJSON()
				return
			}
			host.PrivateKey = decryptedPrivateKey
		}
	}

	// 进行连接测试
	result, err := services.TestSSHConnection(&host)
	if err != nil {
		log.Printf("临时测试主机连接失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "测试连接失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"data":    result,
	}
	c.ServeJSON()
}

// GetHostsByEnvironment 根据环境获取主机列表
// @Title 根据环境获取主机列表
// @Description 获取指定环境的活跃主机列表，用于项目配置
// @Param environment path string true "环境类型：test或prod"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @router /hosts/environment/:environment [get]
func (c *HostController) GetHostsByEnvironment() {
	environment := c.Ctx.Input.Param(":environment")

	if environment != "test" && environment != "prod" {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "环境类型只能是test或prod",
		}
		c.ServeJSON()
		return
	}

	hosts, err := models.GetHostsByEnvironment(environment)
	if err != nil {
		log.Printf("获取环境主机列表失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "获取主机列表失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"data":    hosts,
	}
	c.ServeJSON()
}
