package controllers

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"

	"GoShip/models"
	"github.com/beego/beego/v2/server/web"
)

// GitAuthController Git认证管理控制器
type GitAuthController struct {
	web.Controller
}

// GetList 获取Git认证配置列表
func (c *GitAuthController) GetList() {
	page, _ := c.GetInt("page", 1)
	size, _ := c.GetInt("size", 10)
	search := c.GetString("search", "")
	status := c.GetString("status", "")
	sortBy := c.GetString("sortBy", "")
	sortOrder := c.GetString("sortOrder", "asc")

	log.Printf("获取Git认证配置列表: page=%d, size=%d, search=%s", page, size, search)

	// 根据状态过滤
	if status != "" && status != "all" {
		search = status
		sortBy = "status"
	}

	configs, total, err := models.GetGitAuths(page, size, search, sortBy, sortOrder)
	if err != nil {
		log.Printf("获取Git认证配置列表失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": fmt.Sprintf("获取Git认证配置列表失败: %v", err),
		}
		c.ServeJSON()
		return
	}

	// 计算分页信息
	totalPages := (total + int64(size) - 1) / int64(size)

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"items":       configs,
			"total":       total,
			"page":        page,
			"size":        size,
			"total_pages": totalPages,
		},
	}
	c.ServeJSON()
}

// Post 创建Git认证配置
func (c *GitAuthController) Post() {
	var config models.GitAuth
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &config); err != nil {
		log.Printf("解析Git认证配置数据失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": fmt.Sprintf("解析请求数据失败: %v", err),
		}
		c.ServeJSON()
		return
	}

	// 数据验证
	if err := validateGitAuth(&config); err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": err.Error(),
		}
		c.ServeJSON()
		return
	}

	log.Printf("创建Git认证配置: %s", config.Name)

	// 创建配置
	if err := models.CreateGitAuth(&config); err != nil {
		log.Printf("创建Git认证配置失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": fmt.Sprintf("创建Git认证配置失败: %v", err),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"message": "Git认证配置创建成功",
		"data":    config,
	}
	c.ServeJSON()
}

// Put 更新Git认证配置
func (c *GitAuthController) Put() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的Git认证配置ID",
		}
		c.ServeJSON()
		return
	}

	var config models.GitAuth
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &config); err != nil {
		log.Printf("解析Git认证配置数据失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": fmt.Sprintf("解析请求数据失败: %v", err),
		}
		c.ServeJSON()
		return
	}

	config.ID = id

	// 数据验证
	if err := validateGitAuth(&config); err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": err.Error(),
		}
		c.ServeJSON()
		return
	}

	log.Printf("更新Git认证配置: %s (ID: %d)", config.Name, id)

	// 更新配置
	if err := models.UpdateGitAuth(&config); err != nil {
		log.Printf("更新Git认证配置失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": fmt.Sprintf("更新Git认证配置失败: %v", err),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"message": "Git认证配置更新成功",
		"data":    config,
	}
	c.ServeJSON()
}

// Delete 删除Git认证配置
func (c *GitAuthController) Delete() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的Git认证配置ID",
		}
		c.ServeJSON()
		return
	}

	log.Printf("删除Git认证配置: ID=%d", id)

	if err := models.DeleteGitAuth(id); err != nil {
		log.Printf("删除Git认证配置失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": fmt.Sprintf("删除Git认证配置失败: %v", err),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"message": "Git认证配置删除成功",
	}
	c.ServeJSON()
}

// Get 根据ID获取Git认证配置详情
func (c *GitAuthController) Get() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的Git认证配置ID",
		}
		c.ServeJSON()
		return
	}

	config, err := models.GetGitAuthByID(id)
	if err != nil {
		log.Printf("获取Git认证配置详情失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": fmt.Sprintf("获取Git认证配置详情失败: %v", err),
		}
		c.ServeJSON()
		return
	}

	if config == nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "Git认证配置不存在",
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"data":    config,
	}
	c.ServeJSON()
}

// TestConnection 测试Git认证连接
func (c *GitAuthController) TestConnection() {
	var config models.GitAuth
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &config); err != nil {
		log.Printf("解析Git认证配置数据失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": fmt.Sprintf("解析请求数据失败: %v", err),
		}
		c.ServeJSON()
		return
	}

	log.Printf("测试Git认证连接: %s", config.Name)

	result, err := models.TestGitAuth(&config)
	if err != nil {
		log.Printf("测试Git认证连接失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": fmt.Sprintf("测试连接失败: %v", err),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"data":    result,
	}
	c.ServeJSON()
}

// GetActiveConfigs 获取活跃的Git认证配置列表
func (c *GitAuthController) GetActiveConfigs() {
	configs, err := models.GetActiveGitAuths()
	if err != nil {
		log.Printf("获取活跃Git认证配置列表失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": fmt.Sprintf("获取活跃Git认证配置列表失败: %v", err),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"data":    configs,
	}
	c.ServeJSON()
}

// validateGitAuth 验证Git认证配置数据
func validateGitAuth(config *models.GitAuth) error {
	if strings.TrimSpace(config.Name) == "" {
		return fmt.Errorf("认证配置名称不能为空")
	}

	if config.Status == "" {
		config.Status = "active"
	}

	return nil
}
