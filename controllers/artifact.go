package controllers

import (
	"GoShip/models"
	"GoShip/services"
	"fmt"
	"os"
	"strconv"

	beego "github.com/beego/beego/v2/server/web"
)

type ArtifactController struct {
	beego.Controller
}

// 部署指定产物
func (c *ArtifactController) DeployArtifact() {
	idStr := c.Ctx.Input.Param(":id")
	artifactID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的产物ID",
			"error":   err.Error(),
		}
		c.Serve<PERSON>()
		return
	}

	// 立即返回，异步执行
	c.Data["json"] = map[string]interface{}{
		"success": true,
		"message": "部署任务已提交，请稍后通过日志查看进度",
	}
	c.ServeJSON()

	go func(artifactID int64) {
		artifact, err := models.GetArtifactByID(artifactID)
		if err != nil || artifact == nil {
			services.SendDeployLog(artifactID, "deploy", "error", "未找到产物: "+err.Error())
			return
		}
		project, err := models.GetProjectByID(artifact.ProjectID)
		if err != nil || project == nil {
			services.SendDeployLog(artifact.ProjectID, "deploy", "error", "未找到项目: "+err.Error())
			return
		}
		if err := services.DeployArtifact(project, artifact); err != nil {
			services.SendDeployLog(artifact.ProjectID, "deploy", "error", "部署失败: "+err.Error())
			return
		}
		services.SendDeployLog(artifact.ProjectID, "deploy", "info", "部署成功")
	}(artifactID)
}

// 查询指定项目的历史产物列表
func (c *ArtifactController) ListByProject() {
	projectIDStr := c.GetString("project_id")
	projectID, err := strconv.ParseInt(projectIDStr, 10, 64)
	if err != nil || projectID <= 0 {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的项目ID",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}
	artifacts, err := models.GetArtifactsByProjectID(projectID)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "获取历史产物失败",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}
	c.Data["json"] = map[string]interface{}{
		"success": true,
		"items":   artifacts,
	}
	c.ServeJSON()
}

// DeleteArtifact 删除产物
func (c *ArtifactController) DeleteArtifact() {
	idStr := c.Ctx.Input.Param(":id")
	artifactID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的产物ID",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}

	// 获取产物信息
	artifact, err := models.GetArtifactByID(artifactID)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "产物不存在",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}

	// 删除数据库记录
	if err := models.DeleteArtifactByID(artifactID); err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "删除产物失败",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}

	// 删除物理文件（如果存在）
	if artifact.ArtifactPath != "" {
		if err := os.Remove(artifact.ArtifactPath); err != nil {
			// 文件删除失败不影响数据库删除的成功，只记录日志
			fmt.Printf("删除产物文件失败: %v\n", err)
		}
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"message": "删除产物成功",
	}
	c.ServeJSON()
}

// ToggleStar 切换产物星标状态
func (c *ArtifactController) ToggleStar() {
	idStr := c.Ctx.Input.Param(":id")
	artifactID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的产物ID",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}

	// 获取产物信息
	artifact, err := models.GetArtifactByID(artifactID)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "产物不存在",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}

	// 切换星标状态
	newStarStatus := !artifact.IsStarred

	// 如果是添加星标，需要检查是否会导致冲突
	if newStarStatus {
		conflict, err := models.CheckStarConflict(artifact.ProjectID, artifactID)
		if err != nil {
			c.Data["json"] = map[string]interface{}{
				"success": false,
				"message": "检查星标冲突失败",
				"error":   err.Error(),
			}
			c.ServeJSON()
			return
		}

		if conflict.HasConflict {
			c.Data["json"] = map[string]interface{}{
				"success":     false,
				"message":     conflict.Message,
				"conflict":    true,
				"suggestions": conflict.Suggestions,
			}
			c.ServeJSON()
			return
		}
	}

	if err := models.UpdateArtifactStar(artifactID, newStarStatus); err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "更新星标状态失败",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}

	action := "取消星标"
	if newStarStatus {
		action = "添加星标"
	}

	c.Data["json"] = map[string]interface{}{
		"success":    true,
		"message":    fmt.Sprintf("%s成功", action),
		"is_starred": newStarStatus,
	}
	c.ServeJSON()
}

// GetStarStatus 获取项目的星标状态统计
func (c *ArtifactController) GetStarStatus() {
	projectIDStr := c.GetString("project_id")
	projectID, err := strconv.ParseInt(projectIDStr, 10, 64)
	if err != nil || projectID <= 0 {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的项目ID",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}

	// 获取项目配置
	project, err := models.GetProjectByID(projectID)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "获取项目配置失败",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}

	// 获取星标统计信息
	starStatus, err := models.GetProjectStarStatus(projectID)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "获取星标状态失败",
			"error":   err.Error(),
		}
		c.ServeJSON()
		return
	}

	// 计算允许的最大星标数量
	maxAllowedStars := 0
	if project.MaxArtifactCount > 0 {
		maxAllowedStars = models.CalculateMaxAllowedStars(project.MaxArtifactCount)
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"starred_count":      starStatus.StarredCount,
			"total_count":        starStatus.TotalCount,
			"max_artifact_count": project.MaxArtifactCount,
			"max_allowed_stars":  maxAllowedStars,
			"can_add_star":       starStatus.StarredCount < maxAllowedStars,
		},
	}
	c.ServeJSON()
}
