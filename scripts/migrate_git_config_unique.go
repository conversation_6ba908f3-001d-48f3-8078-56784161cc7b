package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
	"github.com/beego/beego/v2/server/web"
)

func main() {
	// 读取数据库配置
	dbUser := web.AppConfig.DefaultString("mysqluser", "root")
	dbPass := web.AppConfig.DefaultString("mysqlpass", "")
	dbHost := web.AppConfig.DefaultString("mysqlhost", "127.0.0.1")
	dbPort := web.AppConfig.DefaultString("mysqlport", "3306")
	dbName := web.AppConfig.DefaultString("mysqldb", "goship")

	// 构建数据库连接字符串
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		dbUser, dbPass, dbHost, dbPort, dbName)

	// 连接数据库
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	// 测试连接
	if err := db.Ping(); err != nil {
		log.Fatalf("数据库连接测试失败: %v", err)
	}

	log.Println("开始Git配置表唯一约束迁移...")

	// 执行迁移
	if err := migrateGitConfigUnique(db); err != nil {
		log.Fatalf("迁移失败: %v", err)
	}

	log.Println("Git配置表唯一约束迁移完成!")
}

func migrateGitConfigUnique(db *sql.DB) error {
	// 1. 检查表是否存在
	var count int
	err := db.QueryRow(`
		SELECT COUNT(*)
		FROM information_schema.tables
		WHERE table_schema = DATABASE()
		AND table_name = 'git_configs'
	`).Scan(&count)
	if err != nil {
		return fmt.Errorf("检查git_configs表失败: %v", err)
	}

	if count == 0 {
		log.Println("git_configs表不存在，跳过迁移")
		return nil
	}

	// 2. 检查是否已经有repository_url的唯一约束
	var constraintCount int
	err = db.QueryRow(`
		SELECT COUNT(*)
		FROM information_schema.statistics
		WHERE table_schema = DATABASE()
		AND table_name = 'git_configs'
		AND index_name = 'repository_url'
		AND non_unique = 0
	`).Scan(&constraintCount)
	if err != nil {
		return fmt.Errorf("检查唯一约束失败: %v", err)
	}

	if constraintCount > 0 {
		log.Println("repository_url唯一约束已存在，跳过迁移")
		return nil
	}

	// 3. 检查是否有重复的repository_url
	var duplicateCount int
	err = db.QueryRow(`
		SELECT COUNT(*)
		FROM (
			SELECT repository_url, COUNT(*) as cnt
			FROM git_configs
			GROUP BY repository_url
			HAVING cnt > 1
		) as duplicates
	`).Scan(&duplicateCount)
	if err != nil {
		return fmt.Errorf("检查重复数据失败: %v", err)
	}

	// 4. 如果有重复数据，处理重复数据
	if duplicateCount > 0 {
		log.Printf("发现 %d 个重复的repository_url，开始处理...", duplicateCount)
		
		// 获取重复的repository_url
		rows, err := db.Query(`
			SELECT repository_url, GROUP_CONCAT(id ORDER BY id) as ids
			FROM git_configs
			GROUP BY repository_url
			HAVING COUNT(*) > 1
		`)
		if err != nil {
			return fmt.Errorf("查询重复数据失败: %v", err)
		}
		defer rows.Close()

		for rows.Next() {
			var repoURL, ids string
			if err := rows.Scan(&repoURL, &ids); err != nil {
				return fmt.Errorf("扫描重复数据失败: %v", err)
			}

			log.Printf("处理重复的repository_url: %s (IDs: %s)", repoURL, ids)
			
			// 为重复的配置添加后缀
			_, err = db.Exec(`
				UPDATE git_configs 
				SET name = CONCAT(name, '_', id)
				WHERE repository_url = ? AND id != (
					SELECT min_id FROM (
						SELECT MIN(id) as min_id 
						FROM git_configs 
						WHERE repository_url = ?
					) as t
				)
			`, repoURL, repoURL)
			if err != nil {
				return fmt.Errorf("更新重复配置名称失败: %v", err)
			}
		}
	}

	// 5. 添加repository_url的唯一约束
	log.Println("添加repository_url唯一约束...")
	_, err = db.Exec(`
		ALTER TABLE git_configs 
		ADD CONSTRAINT uk_repository_url UNIQUE (repository_url)
	`)
	if err != nil {
		return fmt.Errorf("添加唯一约束失败: %v", err)
	}

	// 6. 添加repository_url索引（如果不存在）
	var indexCount int
	err = db.QueryRow(`
		SELECT COUNT(*)
		FROM information_schema.statistics
		WHERE table_schema = DATABASE()
		AND table_name = 'git_configs'
		AND index_name = 'idx_repository_url'
	`).Scan(&indexCount)
	if err != nil {
		return fmt.Errorf("检查索引失败: %v", err)
	}

	if indexCount == 0 {
		log.Println("添加repository_url索引...")
		_, err = db.Exec(`
			ALTER TABLE git_configs 
			ADD INDEX idx_repository_url (repository_url)
		`)
		if err != nil {
			return fmt.Errorf("添加索引失败: %v", err)
		}
	}

	log.Println("唯一约束和索引添加完成")
	return nil
}
