package models

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"database/sql"
	"encoding/base64"
	"fmt"
	"io"
	"log"
	"strings"
	"time"

	beego "github.com/beego/beego/v2/server/web"
)

// Host 主机配置模型
type Host struct {
	ID             int64      `json:"id"`
	Name           string     `json:"name"`             // 主机名称
	IP             string     `json:"ip"`               // IP地址
	Port           int        `json:"port"`             // SSH端口
	Username       string     `json:"username"`         // SSH用户名
	AuthType       string     `json:"auth_type"`        // 认证方式: password, private_key
	Password       string     `json:"password"`         // SSH密码（加密存储）
	PrivateKey     string     `json:"private_key"`      // SSH私钥（加密存储）
	Environment    string     `json:"environment"`      // 环境类型: test, prod
	Description    string     `json:"description"`      // 描述信息
	Status         string     `json:"status"`           // 状态: active, inactive
	LastTestTime   *time.Time `json:"last_test_time"`   // 最后测试时间
	LastTestResult string     `json:"last_test_result"` // 最后测试结果: success, failed
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`
}

// HostConnectionTest 主机连接测试结果
type HostConnectionTest struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Latency int64  `json:"latency"` // 连接延迟（毫秒）
}

// 加密密钥（实际应用中应该从环境变量或配置文件读取）
var encryptionKey []byte

func init() {
	// 从配置文件读取加密密钥，如果没有则生成默认密钥
	keyStr := beego.AppConfig.DefaultString("encryption.key", "goship-default-encryption-key-32")
	hash := sha256.Sum256([]byte(keyStr))
	encryptionKey = hash[:]
}

// encrypt 加密敏感信息
func encrypt(plaintext string) (string, error) {
	if plaintext == "" {
		return "", nil
	}

	block, err := aes.NewCipher(encryptionKey)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}

	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// decrypt 解密敏感信息
func decrypt(ciphertext string) (string, error) {
	if ciphertext == "" {
		return "", nil
	}

	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}

	block, err := aes.NewCipher(encryptionKey)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("ciphertext too short")
	}

	nonce, ciphertext_bytes := data[:nonceSize], data[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext_bytes, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}

// EncryptSensitiveData 加密主机敏感信息
func (h *Host) EncryptSensitiveData() error {
	var err error

	if h.Password != "" {
		h.Password, err = encrypt(h.Password)
		if err != nil {
			return fmt.Errorf("加密密码失败: %v", err)
		}
	}

	if h.PrivateKey != "" {
		h.PrivateKey, err = encrypt(h.PrivateKey)
		if err != nil {
			return fmt.Errorf("加密私钥失败: %v", err)
		}
	}

	return nil
}

// DecryptSensitiveData 解密主机敏感信息
func (h *Host) DecryptSensitiveData() error {
	var err error

	if h.Password != "" {
		h.Password, err = decrypt(h.Password)
		if err != nil {
			return fmt.Errorf("解密密码失败: %v", err)
		}
	}

	if h.PrivateKey != "" {
		h.PrivateKey, err = decrypt(h.PrivateKey)
		if err != nil {
			return fmt.Errorf("解密私钥失败: %v", err)
		}
	}

	return nil
}

// PrepareHostForConnection 为连接测试准备主机数据（智能处理密码解密）
func PrepareHostForConnection(h *Host, hostID int64) error {
	// 如果有主机ID，说明是编辑模式的测试，需要比较密文
	if hostID > 0 {
		// 获取数据库中的原始密文
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		var originalPassword, originalPrivateKey string
		query := "SELECT password, private_key FROM hosts WHERE id = ?"
		err := db.QueryRowContext(ctx, query, hostID).Scan(&originalPassword, &originalPrivateKey)
		if err != nil {
			return fmt.Errorf("获取原有主机数据失败: %v", err)
		}

		// 智能处理密码：如果与数据库密文相同，则解密；否则直接使用
		if h.Password == originalPassword && originalPassword != "" {
			// 密码是密文，需要解密
			decryptedPassword, err := decrypt(h.Password)
			if err != nil {
				return fmt.Errorf("解密密码失败: %v", err)
			}
			h.Password = decryptedPassword
		}
		// 如果密码不同，说明是新输入的明文密码，直接使用

		// 智能处理私钥：如果与数据库密文相同，则解密；否则直接使用
		if h.PrivateKey == originalPrivateKey && originalPrivateKey != "" {
			// 私钥是密文，需要解密
			decryptedPrivateKey, err := decrypt(h.PrivateKey)
			if err != nil {
				return fmt.Errorf("解密私钥失败: %v", err)
			}
			h.PrivateKey = decryptedPrivateKey
		}
		// 如果私钥不同，说明是新输入的明文私钥，直接使用
	}
	// 如果没有主机ID，说明是新建模式的测试，密码和私钥都是明文，直接使用

	return nil
}

// CreateHostTableIfNotExists 创建主机表
func CreateHostTableIfNotExists() error {
	return withRetry(func() error {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		// 先检查表是否存在
		checkQuery := "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'hosts'"
		var count int
		err := db.QueryRowContext(ctx, checkQuery).Scan(&count)
		if err != nil {
			return fmt.Errorf("检查主机表是否存在失败: %v", err)
		}

		// 如果表不存在，则创建
		if count == 0 {
			createTableSQL := `
			CREATE TABLE hosts (
				id BIGINT AUTO_INCREMENT PRIMARY KEY,
				name VARCHAR(100) NOT NULL UNIQUE COMMENT '主机名称',
				ip VARCHAR(45) NOT NULL COMMENT 'IP地址',
				port INT NOT NULL DEFAULT 22 COMMENT 'SSH端口',
				username VARCHAR(100) NOT NULL COMMENT 'SSH用户名',
				auth_type ENUM('password', 'private_key') NOT NULL DEFAULT 'password' COMMENT '认证方式',
				password TEXT COMMENT 'SSH密码（加密存储）',
				private_key TEXT COMMENT 'SSH私钥（加密存储）',
				environment ENUM('test', 'prod') NOT NULL DEFAULT 'test' COMMENT '环境类型',
				description TEXT COMMENT '描述信息',
				status ENUM('active', 'inactive') NOT NULL DEFAULT 'active' COMMENT '状态',
				last_test_time DATETIME COMMENT '最后测试时间',
				last_test_result ENUM('success', 'failed') COMMENT '最后测试结果',
				created_at DATETIME NOT NULL COMMENT '创建时间',
				updated_at DATETIME NOT NULL COMMENT '更新时间',
				INDEX idx_environment (environment),
				INDEX idx_status (status),
				INDEX idx_name (name)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='主机配置表'`

			_, err := db.ExecContext(ctx, createTableSQL)
			if err != nil {
				log.Printf("创建主机表失败: %v", err)
				return fmt.Errorf("创建主机表失败: %v", err)
			}
			log.Println("主机表创建成功")
		}

		return nil
	})
}

// CreateHost 创建主机
func CreateHost(h *Host) error {
	return withRetry(func() error {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		now := time.Now()
		h.CreatedAt = now
		h.UpdatedAt = now

		// 加密敏感信息
		if err := h.EncryptSensitiveData(); err != nil {
			return err
		}

		query := `
			INSERT INTO hosts (
				name, ip, port, username, auth_type, password, private_key,
				environment, description, status, created_at, updated_at
			) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`

		stmt, err := db.PrepareContext(ctx, query)
		if err != nil {
			return fmt.Errorf("准备SQL语句失败: %v", err)
		}
		defer stmt.Close()

		result, err := stmt.ExecContext(ctx,
			h.Name, h.IP, h.Port, h.Username, h.AuthType, h.Password, h.PrivateKey,
			h.Environment, h.Description, h.Status, h.CreatedAt, h.UpdatedAt,
		)
		if err != nil {
			return fmt.Errorf("插入主机数据失败: %v", err)
		}

		id, err := result.LastInsertId()
		if err != nil {
			return fmt.Errorf("获取插入ID失败: %v", err)
		}

		h.ID = id
		log.Printf("主机创建成功，ID: %d", h.ID)
		return nil
	})
}

// GetHostsPaginated 分页获取主机列表
func GetHostsPaginated(page, size int, environment, sortBy, sortOrder string) ([]Host, int, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 构建查询条件
	whereClause := "WHERE 1=1"
	args := []interface{}{}

	if environment != "" {
		whereClause += " AND environment = ?"
		args = append(args, environment)
	}

	// 查询总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM hosts %s", whereClause)
	var total int
	err := db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("查询主机总数失败: %v", err)
	}

	// 计算偏移量
	offset := (page - 1) * size

	// 构建排序子句
	var orderClause string
	switch sortBy {
	case "environment":
		// 环境排序：生产环境优先，然后按名称排序
		if sortOrder == "desc" {
			orderClause = "ORDER BY CASE WHEN environment = 'test' THEN 1 WHEN environment = 'prod' THEN 2 END DESC, name ASC"
		} else {
			orderClause = "ORDER BY CASE WHEN environment = 'prod' THEN 1 WHEN environment = 'test' THEN 2 END ASC, name ASC"
		}
	case "name":
		orderClause = fmt.Sprintf("ORDER BY name %s", strings.ToUpper(sortOrder))
	case "status":
		// 状态排序：活跃状态优先
		if sortOrder == "desc" {
			orderClause = "ORDER BY CASE WHEN status = 'inactive' THEN 1 WHEN status = 'active' THEN 2 END DESC, name ASC"
		} else {
			orderClause = "ORDER BY CASE WHEN status = 'active' THEN 1 WHEN status = 'inactive' THEN 2 END ASC, name ASC"
		}
	case "created_at":
		orderClause = fmt.Sprintf("ORDER BY created_at %s", strings.ToUpper(sortOrder))
	default:
		orderClause = "ORDER BY CASE WHEN environment = 'prod' THEN 1 WHEN environment = 'test' THEN 2 END ASC, name ASC"
	}

	// 查询分页数据
	query := fmt.Sprintf(`
		SELECT id, name, ip, port, username, auth_type, password, private_key,
			   environment, description, status, last_test_time, last_test_result,
			   created_at, updated_at
		FROM hosts %s
		%s
		LIMIT ? OFFSET ?
	`, whereClause, orderClause)

	args = append(args, size, offset)
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询主机列表失败: %v", err)
	}
	defer rows.Close()

	var hosts []Host
	for rows.Next() {
		var h Host
		var lastTestResult sql.NullString
		err := rows.Scan(
			&h.ID, &h.Name, &h.IP, &h.Port, &h.Username, &h.AuthType, &h.Password, &h.PrivateKey,
			&h.Environment, &h.Description, &h.Status, &h.LastTestTime, &lastTestResult,
			&h.CreatedAt, &h.UpdatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("扫描主机数据失败: %v", err)
		}

		// 处理NULL值
		if lastTestResult.Valid {
			h.LastTestResult = lastTestResult.String
		}

		// 返回密文形式的敏感信息（用于编辑时的回显和比较）
		// 密码和私钥保持密文状态，不解密

		hosts = append(hosts, h)
	}

	return hosts, total, nil
}

// GetHostByID 根据ID获取主机
func GetHostByID(id int64) (*Host, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var h Host
	var lastTestResult sql.NullString
	err := db.QueryRowContext(ctx, `
		SELECT id, name, ip, port, username, auth_type, password, private_key,
			   environment, description, status, last_test_time, last_test_result,
			   created_at, updated_at
		FROM hosts
		WHERE id = ?
	`, id).Scan(
		&h.ID, &h.Name, &h.IP, &h.Port, &h.Username, &h.AuthType, &h.Password, &h.PrivateKey,
		&h.Environment, &h.Description, &h.Status, &h.LastTestTime, &lastTestResult,
		&h.CreatedAt, &h.UpdatedAt,
	)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("查询主机失败: %v", err)
	}

	// 处理NULL值
	if lastTestResult.Valid {
		h.LastTestResult = lastTestResult.String
	}

	// 解密敏感信息
	if err := h.DecryptSensitiveData(); err != nil {
		return nil, fmt.Errorf("解密主机敏感信息失败: %v", err)
	}

	return &h, nil
}

// GetHostByIDRaw 获取主机原始数据（不解密敏感信息）
func GetHostByIDRaw(id int64) (*Host, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	query := `
		SELECT id, name, ip, port, username, auth_type, password, private_key,
			   environment, description, status, last_test_time, last_test_result,
			   created_at, updated_at
		FROM hosts
		WHERE id = ?
	`

	row := db.QueryRowContext(ctx, query, id)

	var h Host
	var lastTestTime sql.NullTime
	var lastTestResult sql.NullString

	err := row.Scan(
		&h.ID, &h.Name, &h.IP, &h.Port, &h.Username, &h.AuthType,
		&h.Password, &h.PrivateKey, &h.Environment, &h.Description,
		&h.Status, &lastTestTime, &lastTestResult, &h.CreatedAt, &h.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("主机不存在")
		}
		return nil, fmt.Errorf("查询主机失败: %v", err)
	}

	// 处理NULL值
	if lastTestTime.Valid {
		h.LastTestTime = &lastTestTime.Time
	}
	if lastTestResult.Valid {
		h.LastTestResult = lastTestResult.String
	}

	// 不解密，返回原始密文数据
	return &h, nil
}

// DecryptString 解密字符串（公开方法）
func DecryptString(ciphertext string) (string, error) {
	return decrypt(ciphertext)
}

// GetHostByIDForEdit 获取主机信息用于编辑（不解密敏感信息）
func GetHostByIDForEdit(id int64) (*Host, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	query := `
		SELECT id, name, ip, port, username, auth_type, password, private_key,
			   environment, description, status, last_test_time, last_test_result,
			   created_at, updated_at
		FROM hosts
		WHERE id = ?
	`

	row := db.QueryRowContext(ctx, query, id)

	var h Host
	var lastTestTime sql.NullTime
	var lastTestResult sql.NullString

	err := row.Scan(
		&h.ID, &h.Name, &h.IP, &h.Port, &h.Username, &h.AuthType,
		&h.Password, &h.PrivateKey, &h.Environment, &h.Description,
		&h.Status, &lastTestTime, &lastTestResult, &h.CreatedAt, &h.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("主机不存在")
		}
		return nil, fmt.Errorf("查询主机失败: %v", err)
	}

	// 处理NULL值
	if lastTestTime.Valid {
		h.LastTestTime = &lastTestTime.Time
	}
	if lastTestResult.Valid {
		h.LastTestResult = lastTestResult.String
	}

	// 返回密文形式的敏感信息（用于编辑时的回显和比较）
	// 密码和私钥保持密文状态，不解密

	return &h, nil
}

// GetHostsByEnvironment 根据环境获取主机列表
func GetHostsByEnvironment(environment string) ([]Host, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	query := `
		SELECT id, name, ip, port, username, auth_type, password, private_key,
			   environment, description, status, last_test_time, last_test_result,
			   created_at, updated_at
		FROM hosts
		WHERE environment = ? AND status = 'active'
		ORDER BY name ASC
	`

	rows, err := db.QueryContext(ctx, query, environment)
	if err != nil {
		return nil, fmt.Errorf("查询主机列表失败: %v", err)
	}
	defer rows.Close()

	var hosts []Host
	for rows.Next() {
		var h Host
		var lastTestResult sql.NullString
		err := rows.Scan(
			&h.ID, &h.Name, &h.IP, &h.Port, &h.Username, &h.AuthType, &h.Password, &h.PrivateKey,
			&h.Environment, &h.Description, &h.Status, &h.LastTestTime, &lastTestResult,
			&h.CreatedAt, &h.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描主机数据失败: %v", err)
		}

		// 处理NULL值
		if lastTestResult.Valid {
			h.LastTestResult = lastTestResult.String
		}
		// 解密敏感信息
		if err := h.DecryptSensitiveData(); err != nil {
			log.Printf("解密主机 %d 敏感信息失败: %v", h.ID, err)
			continue
		}

		// 注意：这里返回实际的密码和私钥内容，前端使用密码输入框保护显示

		hosts = append(hosts, h)
	}

	return hosts, nil
}

// UpdateHost 更新主机
func UpdateHost(h *Host) error {
	return withRetry(func() error {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		h.UpdatedAt = time.Now()

		// 加密敏感信息
		if err := h.EncryptSensitiveData(); err != nil {
			return err
		}

		query := `
			UPDATE hosts
			SET name = ?, ip = ?, port = ?, username = ?, auth_type = ?,
				password = ?, private_key = ?, environment = ?, description = ?,
				status = ?, updated_at = ?
			WHERE id = ?
		`

		_, err := db.ExecContext(ctx, query,
			h.Name, h.IP, h.Port, h.Username, h.AuthType, h.Password, h.PrivateKey,
			h.Environment, h.Description, h.Status, h.UpdatedAt, h.ID,
		)
		if err != nil {
			return fmt.Errorf("更新主机失败: %v", err)
		}

		log.Printf("主机更新成功，ID: %d", h.ID)
		return nil
	})
}

// DeleteHost 删除主机
func DeleteHost(id int64) error {
	return withRetry(func() error {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		// 检查是否有项目在使用此主机
		var count int
		err := db.QueryRowContext(ctx, "SELECT COUNT(*) FROM projects WHERE host_id = ?", id).Scan(&count)
		if err != nil {
			return fmt.Errorf("检查主机使用情况失败: %v", err)
		}

		if count > 0 {
			return fmt.Errorf("无法删除主机，还有 %d 个项目正在使用此主机", count)
		}

		_, err = db.ExecContext(ctx, "DELETE FROM hosts WHERE id = ?", id)
		if err != nil {
			return fmt.Errorf("删除主机失败: %v", err)
		}

		log.Printf("主机删除成功，ID: %d", id)
		return nil
	})
}

// UpdateHostTestResult 更新主机测试结果
func UpdateHostTestResult(id int64, success bool, message string) error {
	return withRetry(func() error {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		result := "failed"
		if success {
			result = "success"
		}

		now := time.Now()
		_, err := db.ExecContext(ctx, `
			UPDATE hosts
			SET last_test_time = ?, last_test_result = ?, updated_at = ?
			WHERE id = ?
		`, now, result, now, id)

		if err != nil {
			return fmt.Errorf("更新主机测试结果失败: %v", err)
		}

		return nil
	})
}

// ValidatePrivateKey 验证SSH私钥格式
func ValidatePrivateKey(privateKey string) error {
	if privateKey == "" {
		return fmt.Errorf("私钥不能为空")
	}

	// 检查私钥格式
	if !strings.Contains(privateKey, "BEGIN") || !strings.Contains(privateKey, "PRIVATE KEY") {
		return fmt.Errorf("私钥格式不正确，请确保包含完整的私钥内容")
	}

	// 检查常见的私钥类型
	validHeaders := []string{
		"-----BEGIN RSA PRIVATE KEY-----",
		"-----BEGIN OPENSSH PRIVATE KEY-----",
		"-----BEGIN EC PRIVATE KEY-----",
		"-----BEGIN DSA PRIVATE KEY-----",
		"-----BEGIN PRIVATE KEY-----",
	}

	hasValidHeader := false
	for _, header := range validHeaders {
		if strings.Contains(privateKey, header) {
			hasValidHeader = true
			break
		}
	}

	if !hasValidHeader {
		return fmt.Errorf("不支持的私钥类型，请使用RSA、ECDSA、DSA或OpenSSH格式的私钥")
	}

	return nil
}

// UpdateHostSafely 安全更新主机（智能处理密码加密）
func UpdateHostSafely(h *Host) error {
	return withRetry(func() error {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		h.UpdatedAt = time.Now()

		log.Printf("UpdateHostSafely: 开始更新主机 ID=%d, 传入密码长度=%d", h.ID, len(h.Password))

		// 获取原有的加密数据（不解密）
		var originalPassword, originalPrivateKey string
		query := "SELECT password, private_key FROM hosts WHERE id = ?"
		err := db.QueryRowContext(ctx, query, h.ID).Scan(&originalPassword, &originalPrivateKey)
		if err != nil {
			return fmt.Errorf("获取原有主机数据失败: %v", err)
		}

		log.Printf("UpdateHostSafely: 获取到原始密码长度=%d", len(originalPassword))

		// 智能处理密码：比较密文，如果相同则保持不变，否则重新加密
		if h.Password == originalPassword {
			// 密码未改变（密文相同），保持原有数据
			h.Password = originalPassword
		} else if h.Password != "" {
			// 密码已改变，需要加密新密码
			encryptedPassword, err := encrypt(h.Password)
			if err != nil {
				return fmt.Errorf("加密密码失败: %v", err)
			}
			h.Password = encryptedPassword
		} else {
			// 密码为空，保持原有数据
			h.Password = originalPassword
		}

		// 智能处理私钥：比较密文，如果相同则保持不变，否则重新加密
		if h.PrivateKey == originalPrivateKey && originalPrivateKey != "" {
			// 私钥未改变（密文相同），保持原有数据
			h.PrivateKey = originalPrivateKey
		} else if h.PrivateKey != "" {
			// 私钥已改变，需要加密新私钥
			encryptedPrivateKey, err := encrypt(h.PrivateKey)
			if err != nil {
				return fmt.Errorf("加密私钥失败: %v", err)
			}
			h.PrivateKey = encryptedPrivateKey
		} else {
			// 私钥为空，清空私钥字段
			h.PrivateKey = ""
		}

		updateQuery := `
			UPDATE hosts
			SET name = ?, ip = ?, port = ?, username = ?, auth_type = ?,
				password = ?, private_key = ?, environment = ?, description = ?,
				status = ?, updated_at = ?
			WHERE id = ?
		`

		_, err = db.ExecContext(ctx, updateQuery,
			h.Name, h.IP, h.Port, h.Username, h.AuthType, h.Password, h.PrivateKey,
			h.Environment, h.Description, h.Status, h.UpdatedAt, h.ID,
		)
		if err != nil {
			return fmt.Errorf("更新主机失败: %v", err)
		}

		log.Printf("主机智能更新成功，ID: %d", h.ID)
		return nil
	})
}

// DeleteHostSafely 安全删除主机（不解密敏感信息）
func DeleteHostSafely(id int64) error {
	return withRetry(func() error {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		// 直接删除，不需要解密敏感信息
		query := "DELETE FROM hosts WHERE id = ?"
		result, err := db.ExecContext(ctx, query, id)
		if err != nil {
			return fmt.Errorf("删除主机失败: %v", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return fmt.Errorf("获取删除结果失败: %v", err)
		}

		if rowsAffected == 0 {
			return fmt.Errorf("主机不存在或已被删除")
		}

		log.Printf("主机删除成功，ID: %d", id)
		return nil
	})
}

// ResetEncryptionForAllHosts 重置所有主机的加密（用于密钥更换后的数据修复）
// 注意：这个方法会清空所有主机的密码和私钥，需要重新设置
func ResetEncryptionForAllHosts() error {
	return withRetry(func() error {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		// 清空所有主机的加密字段
		query := `UPDATE hosts SET password = '', private_key = '', updated_at = NOW()`
		_, err := db.ExecContext(ctx, query)
		if err != nil {
			return fmt.Errorf("重置主机加密数据失败: %v", err)
		}

		log.Printf("所有主机的加密数据已重置，请重新设置密码和私钥")
		return nil
	})
}
