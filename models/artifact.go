package models

import (
	"context"
	"fmt"
	"log"
	"time"
)

type Artifact struct {
	ID           int64     `json:"id"`
	ProjectID    int64     `json:"project_id"`
	ProjectName  string    `json:"project_name"`
	ArtifactName string    `json:"artifact_name"`
	ArtifactPath string    `json:"artifact_path"`
	GitRef       string    `json:"git_ref"`
	GitCommitID  *string   `json:"git_commit_id"`
	GitCommitMsg *string   `json:"git_commit_msg"`
	CreatedAt    time.Time `json:"created_at"`
	Size         int64     `json:"size"`
	IsStarred    bool      `json:"is_starred"`
	Remark       string    `json:"remark"`
}

// 自动建表（如不存在）
func CreateArtifactTableIfNotExists() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 先检查表是否存在
	checkQuery := "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'artifacts'"
	var count int
	err := db.QueryRowContext(ctx, checkQuery).Scan(&count)
	if err != nil {
		return fmt.Errorf("检查artifacts表是否存在失败: %v", err)
	}

	// 如果表不存在，则创建
	if count == 0 {
		query := `
		CREATE TABLE artifacts (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			project_id BIGINT NOT NULL,
			project_name VARCHAR(128) NOT NULL,
			artifact_name VARCHAR(256) NOT NULL,
			artifact_path VARCHAR(512) NOT NULL,
			git_ref VARCHAR(128) DEFAULT NULL,
			git_commit_id VARCHAR(64) DEFAULT NULL,
			git_commit_msg VARCHAR(512) DEFAULT NULL,
			created_at DATETIME NOT NULL,
			size BIGINT DEFAULT 0,
			is_starred TINYINT(1) DEFAULT 0,
			remark VARCHAR(256) DEFAULT NULL
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
		`
		_, err := db.ExecContext(ctx, query)
		if err != nil {
			return fmt.Errorf("创建artifacts表失败: %v", err)
		}
		log.Println("artifacts表创建成功")
	}

	// 如果表已存在，检查并添加新字段
	if count > 0 {
		alterQueries := []struct {
			query string
			field string
		}{
			{"ALTER TABLE artifacts ADD COLUMN git_commit_id VARCHAR(64) DEFAULT NULL", "git_commit_id"},
			{"ALTER TABLE artifacts ADD COLUMN git_commit_msg VARCHAR(512) DEFAULT NULL", "git_commit_msg"},
		}

		for _, alter := range alterQueries {
			// 先检查字段是否存在
			checkQuery := "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'artifacts' AND COLUMN_NAME = ?"
			var fieldCount int
			err := db.QueryRowContext(ctx, checkQuery, alter.field).Scan(&fieldCount)
			if err != nil {
				continue
			}

			// 如果字段不存在，则添加
			if fieldCount == 0 {
				_, err := db.ExecContext(ctx, alter.query)
				if err != nil {
					log.Printf("添加字段 %s 失败: %v", alter.field, err)
				} else {
					log.Printf("成功添加字段: %s", alter.field)
				}
			}
		}
	}

	return nil
}

// 插入产物元数据
func InsertArtifact(a *Artifact) error {
	return withRetry(func() error {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		query := `
		INSERT INTO artifacts (
			project_id, project_name, artifact_name, artifact_path, git_ref, git_commit_id, git_commit_msg, created_at, size, is_starred, remark
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`
		stmt, err := db.PrepareContext(ctx, query)
		if err != nil {
			log.Printf("准备插入artifact失败: %v", err)
			return err
		}
		defer stmt.Close()
		result, err := stmt.ExecContext(ctx,
			a.ProjectID, a.ProjectName, a.ArtifactName, a.ArtifactPath, a.GitRef, a.GitCommitID, a.GitCommitMsg, a.CreatedAt, a.Size, a.IsStarred, a.Remark,
		)
		if err != nil {
			log.Printf("插入artifact失败: %v", err)
			return err
		}
		id, err := result.LastInsertId()
		if err == nil {
			a.ID = id
		}
		return err
	})
}

// 通过项目ID和产物名查找产物
func GetArtifactByName(projectID int64, artifactName string) (*Artifact, error) {
	query := `SELECT id, project_id, project_name, artifact_name, artifact_path, git_ref, git_commit_id, git_commit_msg, created_at, size, is_starred, remark FROM artifacts WHERE project_id = ? AND artifact_name = ? LIMIT 1`
	row := db.QueryRow(query, projectID, artifactName)
	var a Artifact
	err := row.Scan(&a.ID, &a.ProjectID, &a.ProjectName, &a.ArtifactName, &a.ArtifactPath, &a.GitRef, &a.GitCommitID, &a.GitCommitMsg, &a.CreatedAt, &a.Size, &a.IsStarred, &a.Remark)
	if err != nil {
		return nil, err
	}
	return &a, nil
}

// 查找最新产物（按创建时间倒序）
func GetLatestArtifact(projectID int64) (*Artifact, error) {
	query := `SELECT id, project_id, project_name, artifact_name, artifact_path, git_ref, git_commit_id, git_commit_msg, created_at, size, is_starred, remark FROM artifacts WHERE project_id = ? ORDER BY created_at DESC LIMIT 1`
	row := db.QueryRow(query, projectID)
	var a Artifact
	err := row.Scan(&a.ID, &a.ProjectID, &a.ProjectName, &a.ArtifactName, &a.ArtifactPath, &a.GitRef, &a.GitCommitID, &a.GitCommitMsg, &a.CreatedAt, &a.Size, &a.IsStarred, &a.Remark)
	if err != nil {
		return nil, err
	}
	return &a, nil
}

// 获取某项目某天的所有产物（不含星标）
func GetArtifactsByProjectIDAndDay(projectID int64, day time.Time) ([]*Artifact, error) {
	start := time.Date(day.Year(), day.Month(), day.Day(), 0, 0, 0, 0, day.Location())
	end := start.Add(24 * time.Hour)
	query := `SELECT id, project_id, project_name, artifact_name, artifact_path, git_ref, git_commit_id, git_commit_msg, created_at, size, is_starred, remark FROM artifacts WHERE project_id = ? AND created_at >= ? AND created_at < ? ORDER BY created_at ASC`
	rows, err := db.Query(query, projectID, start, end)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var artifacts []*Artifact
	for rows.Next() {
		var a Artifact
		err := rows.Scan(&a.ID, &a.ProjectID, &a.ProjectName, &a.ArtifactName, &a.ArtifactPath, &a.GitRef, &a.GitCommitID, &a.GitCommitMsg, &a.CreatedAt, &a.Size, &a.IsStarred, &a.Remark)
		if err != nil {
			return nil, err
		}
		artifacts = append(artifacts, &a)
	}
	return artifacts, nil
}

// 获取某项目所有非星标产物，按创建时间升序
func GetNonStarredArtifacts(projectID int64) ([]*Artifact, error) {
	query := `SELECT id, project_id, project_name, artifact_name, artifact_path, git_ref, git_commit_id, git_commit_msg, created_at, size, is_starred, remark FROM artifacts WHERE project_id = ? AND is_starred = 0 ORDER BY created_at ASC`
	rows, err := db.Query(query, projectID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var artifacts []*Artifact
	for rows.Next() {
		var a Artifact
		err := rows.Scan(&a.ID, &a.ProjectID, &a.ProjectName, &a.ArtifactName, &a.ArtifactPath, &a.GitRef, &a.GitCommitID, &a.GitCommitMsg, &a.CreatedAt, &a.Size, &a.IsStarred, &a.Remark)
		if err != nil {
			return nil, err
		}
		artifacts = append(artifacts, &a)
	}
	return artifacts, nil
}

// 删除产物
func DeleteArtifactByID(id int64) error {
	_, err := db.Exec("DELETE FROM artifacts WHERE id = ?", id)
	return err
}

// 根据ID获取产物
func GetArtifactByID(id int64) (*Artifact, error) {
	query := `SELECT id, project_id, project_name, artifact_name, artifact_path, git_ref, git_commit_id, git_commit_msg, created_at, size, is_starred, remark FROM artifacts WHERE id = ? LIMIT 1`
	row := db.QueryRow(query, id)
	var a Artifact
	err := row.Scan(&a.ID, &a.ProjectID, &a.ProjectName, &a.ArtifactName, &a.ArtifactPath, &a.GitRef, &a.GitCommitID, &a.GitCommitMsg, &a.CreatedAt, &a.Size, &a.IsStarred, &a.Remark)
	if err != nil {
		return nil, err
	}
	return &a, nil
}

// 按项目ID查询所有产物，按创建时间倒序
func GetArtifactsByProjectID(projectID int64) ([]*Artifact, error) {
	query := `SELECT id, project_id, project_name, artifact_name, artifact_path, git_ref, git_commit_id, git_commit_msg, created_at, size, is_starred, remark FROM artifacts WHERE project_id = ? ORDER BY created_at DESC`
	rows, err := db.Query(query, projectID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var artifacts []*Artifact
	for rows.Next() {
		var a Artifact
		err := rows.Scan(&a.ID, &a.ProjectID, &a.ProjectName, &a.ArtifactName, &a.ArtifactPath, &a.GitRef, &a.GitCommitID, &a.GitCommitMsg, &a.CreatedAt, &a.Size, &a.IsStarred, &a.Remark)
		if err != nil {
			return nil, err
		}
		artifacts = append(artifacts, &a)
	}
	return artifacts, nil
}

// UpdateArtifactStar 更新产物星标状态
func UpdateArtifactStar(artifactID int64, isStarred bool) error {
	return withRetry(func() error {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		query := "UPDATE artifacts SET is_starred = ? WHERE id = ?"
		_, err := db.ExecContext(ctx, query, isStarred, artifactID)
		if err != nil {
			return fmt.Errorf("更新产物星标状态失败: %v", err)
		}

		return nil
	})
}

// StarConflict 星标冲突信息
type StarConflict struct {
	HasConflict bool     `json:"has_conflict"`
	Message     string   `json:"message"`
	Suggestions []string `json:"suggestions"`
}

// CheckStarConflict 检查添加星标是否会导致冲突
func CheckStarConflict(projectID, artifactID int64) (*StarConflict, error) {
	// 获取项目配置
	project, err := GetProjectByID(projectID)
	if err != nil {
		return nil, fmt.Errorf("获取项目配置失败: %v", err)
	}

	// 如果项目没有设置数量限制，不会有冲突
	if project.MaxArtifactCount <= 0 {
		return &StarConflict{HasConflict: false}, nil
	}

	// 获取当前星标产物数量
	starredCount, err := getStarredArtifactCount(projectID)
	if err != nil {
		return nil, fmt.Errorf("获取星标产物数量失败: %v", err)
	}

	// 计算星标后的数量
	newStarredCount := starredCount + 1

	// 星标限制策略：
	// 1. 星标数量不能超过最大产物数量的80%
	// 2. 至少要为新产物预留1个位置
	// 3. 星标数量不能超过最大产物数量-1
	maxAllowedStars := calculateMaxAllowedStars(project.MaxArtifactCount)

	conflict := &StarConflict{HasConflict: false}

	if newStarredCount > maxAllowedStars {
		conflict.HasConflict = true
		conflict.Message = fmt.Sprintf("星标产物过多！当前项目最多允许 %d 个星标产物（最大产物数：%d）",
			maxAllowedStars, project.MaxArtifactCount)

		conflict.Suggestions = []string{
			fmt.Sprintf("取消一些不再重要的星标产物（当前已有 %d 个星标）", starredCount),
			fmt.Sprintf("增加项目的最大产物数量限制（当前：%d）", project.MaxArtifactCount),
			"考虑将重要产物备份到其他位置后取消星标",
		}
	}

	return conflict, nil
}

// calculateMaxAllowedStars 计算允许的最大星标数量
func calculateMaxAllowedStars(maxArtifactCount int) int {
	if maxArtifactCount <= 1 {
		return 0 // 如果最大数量只有1个，不允许星标
	}

	// 策略：星标数量不超过最大数量的80%，且至少预留1个位置给新产物
	maxByPercentage := int(float64(maxArtifactCount) * 0.8)
	maxByReservation := maxArtifactCount - 1

	// 取较小值，确保安全
	if maxByPercentage < maxByReservation {
		return maxByPercentage
	}
	return maxByReservation
}

// getStarredArtifactCount 获取项目的星标产物数量
func getStarredArtifactCount(projectID int64) (int, error) {
	query := "SELECT COUNT(*) FROM artifacts WHERE project_id = ? AND is_starred = 1"
	var count int
	err := db.QueryRow(query, projectID).Scan(&count)
	if err != nil {
		return 0, err
	}
	return count, nil
}

// CheckBuildConflict 检查构建新产物是否会导致冲突
func CheckBuildConflict(projectID int64) (*StarConflict, error) {
	// 获取项目配置
	project, err := GetProjectByID(projectID)
	if err != nil {
		return nil, fmt.Errorf("获取项目配置失败: %v", err)
	}

	// 如果项目没有设置数量限制，不会有冲突
	if project.MaxArtifactCount <= 0 {
		return &StarConflict{HasConflict: false}, nil
	}

	// 获取当前所有产物数量
	allArtifacts, err := GetArtifactsByProjectID(projectID)
	if err != nil {
		return nil, fmt.Errorf("获取产物列表失败: %v", err)
	}

	// 获取星标产物数量
	starredCount, err := getStarredArtifactCount(projectID)
	if err != nil {
		return nil, fmt.Errorf("获取星标产物数量失败: %v", err)
	}

	// 获取非星标产物数量
	nonStarredCount := len(allArtifacts) - starredCount

	conflict := &StarConflict{HasConflict: false}

	// 检查冲突条件：
	// 1. 当前总产物数量已达到或超过限制
	// 2. 没有非星标产物可以删除（或删除后仍然超限）
	if len(allArtifacts) >= project.MaxArtifactCount {
		// 如果所有产物都是星标，无法清理
		if starredCount == len(allArtifacts) {
			conflict.HasConflict = true
			conflict.Message = fmt.Sprintf("无法构建新产物！所有 %d 个产物都已星标，无法自动清理", len(allArtifacts))
			conflict.Suggestions = []string{
				"取消一些不再重要的产物的星标状态",
				fmt.Sprintf("增加项目的最大产物数量限制（当前：%d）", project.MaxArtifactCount),
				"手动删除一些产物为新构建腾出空间",
			}
		} else if nonStarredCount == 0 {
			// 理论上不会到这里，但为了安全起见
			conflict.HasConflict = true
			conflict.Message = "无法构建新产物！没有可清理的非星标产物"
			conflict.Suggestions = []string{
				"取消一些产物的星标状态",
				"手动删除一些产物",
			}
		}
		// 如果有非星标产物可以删除，则不会冲突
	}

	return conflict, nil
}

// ProjectStarStatus 项目星标状态
type ProjectStarStatus struct {
	StarredCount int `json:"starred_count"`
	TotalCount   int `json:"total_count"`
}

// GetProjectStarStatus 获取项目的星标状态统计
func GetProjectStarStatus(projectID int64) (*ProjectStarStatus, error) {
	// 获取星标产物数量
	starredCount, err := getStarredArtifactCount(projectID)
	if err != nil {
		return nil, err
	}

	// 获取总产物数量
	artifacts, err := GetArtifactsByProjectID(projectID)
	if err != nil {
		return nil, err
	}

	return &ProjectStarStatus{
		StarredCount: starredCount,
		TotalCount:   len(artifacts),
	}, nil
}

// CalculateMaxAllowedStars 导出计算最大星标数量的方法
func CalculateMaxAllowedStars(maxArtifactCount int) int {
	return calculateMaxAllowedStars(maxArtifactCount)
}
