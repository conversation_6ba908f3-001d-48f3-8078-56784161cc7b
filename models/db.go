package models

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"strings"
	"time"

	beego "github.com/beego/beego/v2/server/web"
	_ "github.com/go-sql-driver/mysql"
)

var db *sql.DB
var ArtifactBasePath string
var SourceRoot string

// 读取 artifact_path 和 source_root 配置
func LoadPaths() {
	ArtifactBasePath = beego.AppConfig.DefaultString("artifact_path", "./artifact")
	SourceRoot = beego.AppConfig.DefaultString("source_root", "./workspace")
}

// 初始化数据库连接
func InitDB() error {
	var err error

	// 读取 artifact_path 和 source_root 配置
	LoadPaths()

	// 从配置文件读取数据库配置
	host, _ := beego.AppConfig.String("mysql.host")
	port, _ := beego.AppConfig.String("mysql.port")
	user, _ := beego.AppConfig.String("mysql.user")
	password, _ := beego.AppConfig.String("mysql.password")
	database, _ := beego.AppConfig.String("mysql.database")
	charset, _ := beego.AppConfig.String("mysql.charset")
	maxIdle, _ := beego.AppConfig.Int("mysql.maxIdle")
	maxOpen, _ := beego.AppConfig.Int("mysql.maxOpen")

	// 构建 DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=true&loc=Local",
		user, password, host, port, database, charset)

	// 连接数据库
	db, err = sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %v", err)
	}

	// 设置连接池
	db.SetMaxIdleConns(maxIdle)      // 最大空闲连接数
	db.SetMaxOpenConns(maxOpen)      // 最大打开连接数
	db.SetConnMaxLifetime(time.Hour) // 连接最大生命周期

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err = db.PingContext(ctx); err != nil {
		return fmt.Errorf("数据库连接测试失败: %v", err)
	}

	// 自动建表 artifacts
	err = CreateArtifactTableIfNotExists()
	if err != nil {
		return err
	}

	// 自动建表 hosts
	err = CreateHostTableIfNotExists()
	if err != nil {
		return err
	}

	// 自动建表 git_auths
	err = InitGitAuthTable()
	if err != nil {
		return err
	}

	// 执行数据库迁移
	err = MigrateDatabase()
	if err != nil {
		return err
	}

	return nil
}

// 执行带重试的数据库操作
func withRetry(operation func() error) error {
	var err error
	for i := 0; i < 3; i++ { // 最多重试3次
		err = operation()
		if err == nil {
			return nil
		}
		if !isRetryableError(err) {
			return err
		}
		time.Sleep(time.Millisecond * 100 * time.Duration(i+1))
	}
	return err
}

// 判断是否是可重试的错误
func isRetryableError(err error) bool {
	if err == nil {
		return false
	}
	errMsg := strings.ToLower(err.Error())
	return strings.Contains(errMsg, "deadlock") ||
		strings.Contains(errMsg, "duplicate entry") ||
		strings.Contains(errMsg, "connection refused") ||
		strings.Contains(errMsg, "too many connections")
}

// 关闭数据库连接
func CloseDB() error {
	if db != nil {
		return db.Close()
	}
	return nil
}

// MigrateDatabase 执行数据库迁移
func MigrateDatabase() error {
	return withRetry(func() error {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		// 检查projects表是否存在host_id字段
		var columnExists bool
		err := db.QueryRowContext(ctx, `
			SELECT COUNT(*) > 0
			FROM INFORMATION_SCHEMA.COLUMNS
			WHERE TABLE_SCHEMA = DATABASE()
			AND TABLE_NAME = 'projects'
			AND COLUMN_NAME = 'host_id'
		`).Scan(&columnExists)

		if err != nil {
			log.Printf("检查host_id字段失败: %v", err)
			return fmt.Errorf("检查host_id字段失败: %v", err)
		}

		// 如果字段不存在，则添加字段
		if !columnExists {
			log.Println("正在为projects表添加host_id字段...")
			_, err = db.ExecContext(ctx, `
				ALTER TABLE projects
				ADD COLUMN host_id BIGINT NULL COMMENT '主机ID，关联hosts表'
				AFTER build_config
			`)
			if err != nil {
				log.Printf("添加host_id字段失败: %v", err)
				return fmt.Errorf("添加host_id字段失败: %v", err)
			}

			// 添加外键约束
			_, err = db.ExecContext(ctx, `
				ALTER TABLE projects
				ADD CONSTRAINT fk_projects_host_id
				FOREIGN KEY (host_id) REFERENCES hosts(id)
				ON DELETE SET NULL ON UPDATE CASCADE
			`)
			if err != nil {
				log.Printf("添加外键约束失败: %v", err)
				// 外键约束失败不影响主要功能，只记录日志
			} else {
				log.Println("外键约束添加成功")
			}

			log.Println("host_id字段添加成功")
		}

		return nil
	})
}
