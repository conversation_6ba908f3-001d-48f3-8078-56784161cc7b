package models

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"
)

type Project struct {
	ID          int64       `json:"id"`
	Name        string      `json:"name"`
	Description string      `json:"description"`
	GitURL      string      `json:"git_url"`
	GitUsername string      `json:"git_username"`
	GitPassword string      `json:"git_password"`
	GitVersion  string      `json:"git_version"`  // 新增字段，格式: "branch:master" 或 "tag:v1.0.0"
	DeployType  string      `json:"deploy_type"`  // local或remote
	Environment string      `json:"environment"`  // test或prod
	ProjectType string      `json:"project_type"` // frontend或backend
	BuildConfig BuildConfig `json:"build_config"`
	// 主机配置 - 新增字段
	HostID *int64 `json:"host_id"` // 主机ID，关联hosts表
	// 兼容性字段 - 保留原有字段以确保向后兼容
	ServerIP         string    `json:"server_ip"`
	ServerPort       string    `json:"server_port"`
	ServerUsername   string    `json:"server_username"`
	ServerPassword   string    `json:"server_password"`
	DeployPath       string    `json:"deploy_path"`
	BuildCommand     string    `json:"build_command"`
	PostDeployShell  string    `json:"post_deploy_shell"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
	MaxArtifactDays  int       `json:"max_artifact_days"`
	MaxArtifactCount int       `json:"max_artifact_count"`
}

// BuildConfig 构建配置
type BuildConfig struct {
	Language string                 `json:"language"` // go、vue、java等
	Config   map[string]interface{} `json:"config"`   // 语言特定的配置
}

// GetBuildCommand 根据不同语言生成构建命令
func (p *Project) GetBuildCommand() string {
	if p.BuildConfig.Language == "" {
		return p.BuildCommand
	}

	switch p.BuildConfig.Language {
	case "go":
		config := p.BuildConfig.Config
		serverOS, _ := config["server_os"].(string)
		serverArch, _ := config["server_arch"].(string)
		if serverOS != "" && serverArch != "" {
			// 只返回基本的构建命令，不包含输出文件名
			return fmt.Sprintf("GOOS=%s GOARCH=%s go build", serverOS, serverArch)
		}
	case "vue":
		return "npm install && npm run build"
	case "java":
		buildTool, _ := p.BuildConfig.Config["build_tool"].(string)
		if buildTool == "maven" {
			return "mvn clean package -DskipTests"
		} else if buildTool == "gradle" {
			return "./gradlew build -x test"
		}
	}

	return p.BuildCommand
}

// ProjectList 项目列表响应结构
type ProjectList struct {
	Total int64     `json:"total"`
	Items []Project `json:"items"`
}

func CreateProject(p *Project) error {
	return withRetry(func() error {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		now := time.Now()
		p.CreatedAt = now
		p.UpdatedAt = now

		query := `
			INSERT INTO projects (
				name, description, git_url, git_username, git_password,
				git_version,
				deploy_type, environment, project_type, build_config,
				host_id,
				server_ip, server_port, server_username, server_password,
				deploy_path, post_deploy_shell,
				max_artifact_days, max_artifact_count,
				created_at, updated_at
			) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`
		log.Printf("执行SQL插入: %s", query)

		stmt, err := db.PrepareContext(ctx, query)
		if err != nil {
			log.Printf("准备SQL语句失败: %v", err)
			return fmt.Errorf("准备SQL语句失败: %v", err)
		}
		defer stmt.Close()

		// 将 BuildConfig 转换为 JSON
		buildConfigJSON, err := json.Marshal(p.BuildConfig)
		if err != nil {
			log.Printf("序列化构建配置失败: %v", err)
			return fmt.Errorf("序列化构建配置失败: %v", err)
		}

		result, err := stmt.ExecContext(ctx,
			p.Name, p.Description, p.GitURL, p.GitUsername, p.GitPassword,
			p.GitVersion,
			p.DeployType, p.Environment, p.ProjectType, buildConfigJSON,
			p.HostID,
			p.ServerIP, p.ServerPort, p.ServerUsername, p.ServerPassword,
			p.DeployPath, p.PostDeployShell,
			p.MaxArtifactDays, p.MaxArtifactCount,
			p.CreatedAt, p.UpdatedAt,
		)
		if err != nil {
			log.Printf("插入项目数据失败: %v", err)
			return fmt.Errorf("插入项目数据失败: %v", err)
		}

		id, err := result.LastInsertId()
		if err != nil {
			log.Printf("获取插入ID失败: %v", err)
			return fmt.Errorf("获取插入ID失败: %v", err)
		}
		p.ID = id
		log.Printf("成功创建项目，ID: %d", id)
		return nil
	})
}

// GetAllProjects 获取项目列表（带分页）
func GetAllProjects(page, size int) (*ProjectList, error) {
	return GetAllProjectsWithFilter(page, size, "", "", "")
}

// GetAllProjectsWithFilter 获取项目列表（带分页+筛选）
func GetAllProjectsWithFilter(page, size int, environment, projectType, nameFilter string) (*ProjectList, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 构建WHERE条件
	var whereConditions []string
	var args []interface{}

	if environment != "" {
		whereConditions = append(whereConditions, "environment = ?")
		args = append(args, environment)
	}

	if projectType != "" {
		whereConditions = append(whereConditions, "project_type = ?")
		args = append(args, projectType)
	}

	if nameFilter != "" {
		whereConditions = append(whereConditions, "name LIKE ?")
		args = append(args, "%"+nameFilter+"%")
	}

	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
	}

	// 获取总数
	var total int64
	countQuery := "SELECT COUNT(*) FROM projects " + whereClause
	err := db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		log.Printf("获取项目总数失败: %v", err)
		return nil, fmt.Errorf("获取项目总数失败: %v", err)
	}

	// 计算偏移量
	offset := (page - 1) * size

	// 查询分页数据
	query := `
		SELECT id, name, description, git_url, git_username, git_password,
			   git_version,
			   deploy_type, environment, project_type, build_config,
			   host_id,
			   server_ip, server_port, server_username, server_password,
			   deploy_path, post_deploy_shell,
			   max_artifact_days, max_artifact_count,
			   created_at, updated_at
		FROM projects ` + whereClause + `
		ORDER BY created_at DESC
		LIMIT ? OFFSET ?
	`

	// 添加分页参数
	queryArgs := append(args, size, offset)

	rows, err := db.QueryContext(ctx, query, queryArgs...)
	if err != nil {
		log.Printf("查询项目列表失败: %v", err)
		return nil, fmt.Errorf("查询项目列表失败: %v", err)
	}
	defer rows.Close()

	var projects []Project
	for rows.Next() {
		var p Project
		var buildConfigJSON []byte
		err := rows.Scan(
			&p.ID, &p.Name, &p.Description, &p.GitURL, &p.GitUsername, &p.GitPassword,
			&p.GitVersion,
			&p.DeployType, &p.Environment, &p.ProjectType, &buildConfigJSON,
			&p.HostID,
			&p.ServerIP, &p.ServerPort, &p.ServerUsername, &p.ServerPassword,
			&p.DeployPath, &p.PostDeployShell,
			&p.MaxArtifactDays, &p.MaxArtifactCount,
			&p.CreatedAt, &p.UpdatedAt,
		)
		if err != nil {
			log.Printf("扫描项目数据失败: %v", err)
			return nil, fmt.Errorf("扫描项目数据失败: %v", err)
		}

		// 解析 JSON 到 BuildConfig
		if len(buildConfigJSON) > 0 {
			if err := json.Unmarshal(buildConfigJSON, &p.BuildConfig); err != nil {
				log.Printf("解析构建配置失败: %v", err)
				return nil, fmt.Errorf("解析构建配置失败: %v", err)
			}
		}

		projects = append(projects, p)
	}

	if err = rows.Err(); err != nil {
		log.Printf("遍历项目数据失败: %v", err)
		return nil, fmt.Errorf("遍历项目数据失败: %v", err)
	}

	return &ProjectList{
		Total: total,
		Items: projects,
	}, nil
}

func GetProjectByID(id int64) (*Project, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	p := &Project{}
	var buildConfigJSON []byte

	err := db.QueryRowContext(ctx, `
		SELECT id, name, description, git_url, git_username, git_password,
			   git_version,
			   deploy_type, environment, project_type, build_config,
			   host_id,
			   server_ip, server_port, server_username, server_password,
			   deploy_path, post_deploy_shell,
			   max_artifact_days, max_artifact_count,
			   created_at, updated_at
		FROM projects
		WHERE id = ?
	`, id).Scan(
		&p.ID, &p.Name, &p.Description, &p.GitURL, &p.GitUsername, &p.GitPassword,
		&p.GitVersion,
		&p.DeployType, &p.Environment, &p.ProjectType, &buildConfigJSON,
		&p.HostID,
		&p.ServerIP, &p.ServerPort, &p.ServerUsername, &p.ServerPassword,
		&p.DeployPath, &p.PostDeployShell,
		&p.MaxArtifactDays, &p.MaxArtifactCount,
		&p.CreatedAt, &p.UpdatedAt,
	)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}

	// 解析 JSON 到 BuildConfig
	if len(buildConfigJSON) > 0 {
		if err := json.Unmarshal(buildConfigJSON, &p.BuildConfig); err != nil {
			log.Printf("解析构建配置失败: %v", err)
			return nil, fmt.Errorf("解析构建配置失败: %v", err)
		}
	}

	return p, nil
}

func DeleteProject(id int64) error {
	return withRetry(func() error {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		_, err := db.ExecContext(ctx, `
			DELETE FROM projects 
			WHERE id = ?
		`, id)
		return err
	})
}

// UpdateProject 更新项目
func UpdateProject(p *Project) error {
	return withRetry(func() error {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		p.UpdatedAt = time.Now()

		// 将 BuildConfig 转换为 JSON
		buildConfigJSON, err := json.Marshal(p.BuildConfig)
		if err != nil {
			log.Printf("序列化构建配置失败: %v", err)
			return fmt.Errorf("序列化构建配置失败: %v", err)
		}

		_, err = db.ExecContext(ctx, `
			UPDATE projects
			SET name = ?, description = ?, git_url = ?, git_username = ?, git_password = ?,
				git_version = ?,
				deploy_type = ?, environment = ?, project_type = ?, build_config = ?,
				host_id = ?,
				server_ip = ?, server_port = ?, server_username = ?, server_password = ?,
				deploy_path = ?, post_deploy_shell = ?,
				max_artifact_days = ?, max_artifact_count = ?,
				updated_at = ?
			WHERE id = ?
		`,
			p.Name, p.Description, p.GitURL, p.GitUsername, p.GitPassword,
			p.GitVersion,
			p.DeployType, p.Environment, p.ProjectType, buildConfigJSON,
			p.HostID,
			p.ServerIP, p.ServerPort, p.ServerUsername, p.ServerPassword,
			p.DeployPath, p.PostDeployShell,
			p.MaxArtifactDays, p.MaxArtifactCount,
			p.UpdatedAt, p.ID,
		)
		return err
	})
}

// GetProjectHost 获取项目关联的主机配置
func (p *Project) GetProjectHost() (*Host, error) {
	// 如果项目配置了主机ID，则从主机表获取配置
	if p.HostID != nil && *p.HostID > 0 {
		return GetHostByID(*p.HostID)
	}

	// 如果没有配置主机ID，则使用项目中的传统配置字段（向后兼容）
	if p.ServerIP != "" {
		host := &Host{
			IP:       p.ServerIP,
			Username: p.ServerUsername,
			AuthType: "password",
			Password: p.ServerPassword,
			Port:     22, // 默认SSH端口
		}

		// 尝试解析端口
		if p.ServerPort != "" {
			if port, err := strconv.Atoi(p.ServerPort); err == nil {
				host.Port = port
			}
		}

		return host, nil
	}

	return nil, fmt.Errorf("项目未配置主机信息")
}

// HasHostConfiguration 检查项目是否配置了主机信息
func (p *Project) HasHostConfiguration() bool {
	// 检查是否配置了主机ID
	if p.HostID != nil && *p.HostID > 0 {
		return true
	}

	// 检查是否配置了传统的服务器信息
	if p.ServerIP != "" && p.ServerUsername != "" {
		return true
	}

	return false
}

// GetAllProjectsForCleanup 获取所有项目（用于定时清理）
func GetAllProjectsForCleanup() ([]*Project, error) {
	query := `SELECT id, name, description, git_url, git_username, git_password, git_version,
			  deploy_type, environment, project_type, build_config, host_id, server_ip, server_port,
			  server_username, server_password, deploy_path, post_deploy_shell,
			  max_artifact_days, max_artifact_count, created_at, updated_at
			  FROM projects ORDER BY id ASC`

	rows, err := db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var projects []*Project
	for rows.Next() {
		project := &Project{}
		var buildConfigJSON string
		var hostID sql.NullInt64

		err := rows.Scan(
			&project.ID, &project.Name, &project.Description, &project.GitURL,
			&project.GitUsername, &project.GitPassword, &project.GitVersion,
			&project.DeployType, &project.Environment, &project.ProjectType,
			&buildConfigJSON, &hostID, &project.ServerIP, &project.ServerPort,
			&project.ServerUsername, &project.ServerPassword, &project.DeployPath,
			&project.PostDeployShell, &project.MaxArtifactDays, &project.MaxArtifactCount,
			&project.CreatedAt, &project.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}

		// 处理host_id
		if hostID.Valid {
			project.HostID = &hostID.Int64
		}

		// 解析BuildConfig JSON
		if buildConfigJSON != "" {
			if err := json.Unmarshal([]byte(buildConfigJSON), &project.BuildConfig); err != nil {
				return nil, fmt.Errorf("解析项目 %s 的构建配置失败: %v", project.Name, err)
			}
		}

		projects = append(projects, project)
	}

	return projects, nil
}
