package models

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"database/sql"
	"encoding/base64"
	"fmt"
	"io"
	"log"
	"os/exec"
	"strings"
	"time"

	beego "github.com/beego/beego/v2/server/web"
)

// GitAuth Git认证配置模型
type GitAuth struct {
	ID          int64     `json:"id"`
	Name        string    `json:"name"`        // 认证配置名称
	AuthType    string    `json:"auth_type"`   // 认证类型: none, token, ssh, username
	Username    string    `json:"username"`    // Git用户名
	Password    string    `json:"password"`    // Git密码或Token（加密存储）
	SSHKey      string    `json:"ssh_key"`     // SSH私钥（加密存储）
	Description string    `json:"description"` // 描述信息
	Status      string    `json:"status"`      // 状态: active, inactive
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	UserID      *int64    `json:"user_id"` // 创建用户ID
}

// GitAuthTest Git认证测试结果
type GitAuthTest struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Latency int64  `json:"latency"` // 连接延迟（毫秒）
}

// Git配置加密密钥（与主机配置共用）
var gitEncryptionKey []byte

func init() {
	// 从配置文件读取加密密钥
	keyStr := beego.AppConfig.DefaultString("encryption.key", "goship-default-encryption-key-32")
	hash := sha256.Sum256([]byte(keyStr))
	gitEncryptionKey = hash[:]
}

// EncryptSensitiveData 加密敏感数据
func (g *GitAuth) EncryptSensitiveData() error {
	var err error

	// 加密密码/Token
	if g.Password != "" {
		g.Password, err = encryptGitData(g.Password)
		if err != nil {
			return fmt.Errorf("加密密码失败: %v", err)
		}
	}

	// 加密SSH密钥
	if g.SSHKey != "" {
		g.SSHKey, err = encryptGitData(g.SSHKey)
		if err != nil {
			return fmt.Errorf("加密SSH密钥失败: %v", err)
		}
	}

	return nil
}

// DecryptSensitiveData 解密敏感数据
func (g *GitAuth) DecryptSensitiveData() error {
	var err error

	// 解密密码/Token
	if g.Password != "" {
		g.Password, err = decryptGitData(g.Password)
		if err != nil {
			return fmt.Errorf("解密密码失败: %v", err)
		}
	}

	// 解密SSH密钥
	if g.SSHKey != "" {
		g.SSHKey, err = decryptGitData(g.SSHKey)
		if err != nil {
			return fmt.Errorf("解密SSH密钥失败: %v", err)
		}
	}

	return nil
}

// encryptGitData 加密数据
func encryptGitData(plaintext string) (string, error) {
	if plaintext == "" {
		return "", nil
	}
	
	block, err := aes.NewCipher(gitEncryptionKey)
	if err != nil {
		return "", err
	}
	
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}
	
	nonce := make([]byte, gcm.NonceSize())
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}
	
	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// decryptGitData 解密数据
func decryptGitData(ciphertext string) (string, error) {
	if ciphertext == "" {
		return "", nil
	}
	
	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}
	
	block, err := aes.NewCipher(gitEncryptionKey)
	if err != nil {
		return "", err
	}
	
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}
	
	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("密文长度不足")
	}
	
	nonce, ciphertext_bytes := data[:nonceSize], data[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext_bytes, nil)
	if err != nil {
		return "", err
	}
	
	return string(plaintext), nil
}

// InitGitAuthTable 初始化Git认证配置表
func InitGitAuthTable() error {
	return withRetry(func() error {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		// 检查新表是否存在
		var count int
		err := db.QueryRowContext(ctx, `
			SELECT COUNT(*)
			FROM information_schema.tables
			WHERE table_schema = DATABASE()
			AND table_name = 'git_auths'
		`).Scan(&count)
		if err != nil {
			return fmt.Errorf("检查git_auths表失败: %v", err)
		}

		// 强制重建表以确保结构正确
		if count > 0 {
			// 删除现有表
			_, err = db.ExecContext(ctx, "DROP TABLE IF EXISTS git_auths")
			if err != nil {
				log.Printf("删除旧git_auths表失败: %v", err)
				return fmt.Errorf("删除旧git_auths表失败: %v", err)
			}
			log.Println("已删除旧git_auths表")
		}

		// 创建新表
		createTableSQL := `
			CREATE TABLE git_auths (
				id BIGINT AUTO_INCREMENT PRIMARY KEY,
				name VARCHAR(100) NOT NULL UNIQUE COMMENT 'Git认证配置名称',
				auth_type ENUM('none', 'token', 'ssh', 'username') NOT NULL DEFAULT 'none' COMMENT '认证类型',
				username VARCHAR(100) COMMENT 'Git用户名',
				password TEXT COMMENT 'Git密码或Token（加密存储）',
				ssh_key TEXT COMMENT 'SSH私钥（加密存储）',
				description TEXT COMMENT '配置描述',
				status ENUM('active', 'inactive') NOT NULL DEFAULT 'active' COMMENT '状态',
				created_at DATETIME NOT NULL COMMENT '创建时间',
				updated_at DATETIME NOT NULL COMMENT '更新时间',
				user_id BIGINT COMMENT '创建用户ID',
				INDEX idx_name (name),
				INDEX idx_status (status),
				INDEX idx_user_id (user_id)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Git认证配置表'`

		_, err = db.ExecContext(ctx, createTableSQL)
		if err != nil {
			log.Printf("创建git_auths表失败: %v", err)
			return fmt.Errorf("创建git_auths表失败: %v", err)
		}
		log.Println("git_auths表创建成功")

		// 检查是否需要从旧表迁移数据
		err = migrateFromOldGitConfigTable(ctx)
		if err != nil {
			log.Printf("从git_configs表迁移数据失败: %v", err)
			return fmt.Errorf("从git_configs表迁移数据失败: %v", err)
		}

		return nil
	})
}

// migrateFromOldGitConfigTable 从旧的git_configs表迁移数据到新的git_auths表
func migrateFromOldGitConfigTable(ctx context.Context) error {
	// 检查旧表是否存在
	var oldTableCount int
	err := db.QueryRowContext(ctx, `
		SELECT COUNT(*)
		FROM information_schema.tables
		WHERE table_schema = DATABASE()
		AND table_name = 'git_configs'
	`).Scan(&oldTableCount)
	if err != nil {
		return fmt.Errorf("检查git_configs表失败: %v", err)
	}

	// 如果旧表不存在，无需迁移
	if oldTableCount == 0 {
		return nil
	}

	// 检查新表中是否已有数据
	var newTableDataCount int
	err = db.QueryRowContext(ctx, `SELECT COUNT(*) FROM git_auths`).Scan(&newTableDataCount)
	if err != nil {
		return fmt.Errorf("检查git_auths表数据失败: %v", err)
	}

	// 如果新表已有数据，跳过迁移
	if newTableDataCount > 0 {
		log.Println("git_auths表已有数据，跳过迁移")
		return nil
	}

	// 从旧表查询数据并转换为新表格式
	rows, err := db.QueryContext(ctx, `
		SELECT id, name, repository_url, auth_type, username, password, ssh_key,
			   description, status, last_test_time, last_test_result,
			   created_at, updated_at, user_id
		FROM git_configs
	`)
	if err != nil {
		return fmt.Errorf("查询git_configs表数据失败: %v", err)
	}
	defer rows.Close()

	migratedCount := 0
	for rows.Next() {
		var oldConfig struct {
			ID             int64
			Name           string
			RepositoryURL  string
			AuthType       string
			Username       sql.NullString
			Password       sql.NullString
			SSHKey         sql.NullString
			Description    sql.NullString
			Status         string
			LastTestTime   sql.NullTime
			LastTestResult sql.NullString
			CreatedAt      time.Time
			UpdatedAt      time.Time
			UserID         sql.NullInt64
		}

		err := rows.Scan(
			&oldConfig.ID, &oldConfig.Name, &oldConfig.RepositoryURL, &oldConfig.AuthType,
			&oldConfig.Username, &oldConfig.Password, &oldConfig.SSHKey,
			&oldConfig.Description, &oldConfig.Status, &oldConfig.LastTestTime,
			&oldConfig.LastTestResult, &oldConfig.CreatedAt, &oldConfig.UpdatedAt, &oldConfig.UserID,
		)
		if err != nil {
			log.Printf("扫描git_configs数据失败: %v", err)
			continue
		}

		// 插入到新表
		_, err = db.ExecContext(ctx, `
			INSERT INTO git_auths (
				name, auth_type, username, password, ssh_key,
				description, status, created_at, updated_at, user_id
			) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`,
			oldConfig.Name+"_迁移", oldConfig.AuthType,
			nullStringToString(oldConfig.Username), nullStringToString(oldConfig.Password),
			nullStringToString(oldConfig.SSHKey),
			nullStringToString(oldConfig.Description), oldConfig.Status,
			oldConfig.CreatedAt, oldConfig.UpdatedAt, nullInt64ToInt64Ptr(oldConfig.UserID),
		)
		if err != nil {
			log.Printf("迁移数据失败 (ID: %d): %v", oldConfig.ID, err)
			continue
		}
		migratedCount++
	}

	if migratedCount > 0 {
		log.Printf("成功迁移 %d 条数据从git_configs到git_auths", migratedCount)
	}

	return nil
}



// 辅助函数：处理NULL值转换
func nullStringToString(ns sql.NullString) string {
	if ns.Valid {
		return ns.String
	}
	return ""
}

func nullTimeToTimePtr(nt sql.NullTime) *time.Time {
	if nt.Valid {
		return &nt.Time
	}
	return nil
}

func nullInt64ToInt64Ptr(ni sql.NullInt64) *int64 {
	if ni.Valid {
		return &ni.Int64
	}
	return nil
}

// CreateGitAuth 创建Git认证配置
func CreateGitAuth(g *GitAuth) error {
	return withRetry(func() error {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		now := time.Now()
		g.CreatedAt = now
		g.UpdatedAt = now

		// 加密敏感信息
		if err := g.EncryptSensitiveData(); err != nil {
			return err
		}

		query := `
			INSERT INTO git_auths (
				name, auth_type, username, password, ssh_key,
				description, status, created_at, updated_at, user_id
			) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`

		stmt, err := db.PrepareContext(ctx, query)
		if err != nil {
			return fmt.Errorf("准备SQL语句失败: %v", err)
		}
		defer stmt.Close()

		result, err := stmt.ExecContext(ctx,
			g.Name, g.AuthType, g.Username, g.Password, g.SSHKey,
			g.Description, g.Status, g.CreatedAt, g.UpdatedAt, g.UserID,
		)
		if err != nil {
			return fmt.Errorf("插入Git认证配置数据失败: %v", err)
		}

		id, err := result.LastInsertId()
		if err != nil {
			return fmt.Errorf("获取插入ID失败: %v", err)
		}

		g.ID = id
		log.Printf("Git认证配置创建成功，ID: %d", g.ID)
		return nil
	})
}

// GetGitAuths 获取Git认证配置列表
func GetGitAuths(page, size int, search, sortBy, sortOrder string) ([]GitAuth, int64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 计算偏移量
	offset := (page - 1) * size

	// 构建WHERE条件
	whereClause := "WHERE 1=1"
	args := []interface{}{}

	if search != "" {
		whereClause += " AND (name LIKE ? OR description LIKE ?)"
		searchPattern := "%" + search + "%"
		args = append(args, searchPattern, searchPattern)
	}

	// 查询总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM git_auths %s", whereClause)
	var total int64
	err := db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("查询Git认证配置总数失败: %v", err)
	}

	// 构建排序条件
	var orderClause string
	switch sortBy {
	case "name":
		orderClause = fmt.Sprintf("ORDER BY name %s", strings.ToUpper(sortOrder))
	case "status":
		orderClause = fmt.Sprintf("ORDER BY status %s", strings.ToUpper(sortOrder))
	case "created_at":
		orderClause = fmt.Sprintf("ORDER BY created_at %s", strings.ToUpper(sortOrder))
	default:
		orderClause = "ORDER BY CASE WHEN status = 'active' THEN 1 WHEN status = 'inactive' THEN 2 END ASC, name ASC"
	}

	// 查询分页数据
	query := fmt.Sprintf(`
		SELECT id, name, auth_type, username, password, ssh_key,
			   description, status, created_at, updated_at, user_id
		FROM git_auths %s
		%s
		LIMIT ? OFFSET ?
	`, whereClause, orderClause)

	args = append(args, size, offset)
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询Git认证配置列表失败: %v", err)
	}
	defer rows.Close()

	var configs []GitAuth
	for rows.Next() {
		var g GitAuth
		var password sql.NullString
		var sshKey sql.NullString
		var userID sql.NullInt64
		err := rows.Scan(
			&g.ID, &g.Name, &g.AuthType, &g.Username, &password, &sshKey,
			&g.Description, &g.Status, &g.CreatedAt, &g.UpdatedAt, &userID,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("扫描Git认证配置数据失败: %v", err)
		}

		// 处理NULL值
		if password.Valid {
			g.Password = password.String
		}
		if sshKey.Valid {
			g.SSHKey = sshKey.String
		}
		if userID.Valid {
			g.UserID = &userID.Int64
		}

		// 返回密文形式的敏感信息（用于编辑时的回显和比较）
		// 密码和SSH密钥保持密文状态，不解密

		configs = append(configs, g)
	}

	return configs, total, nil
}

// GetGitAuthByID 根据ID获取Git认证配置
func GetGitAuthByID(id int64) (*GitAuth, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var g GitAuth
	var password sql.NullString
	var sshKey sql.NullString
	var userID sql.NullInt64
	err := db.QueryRowContext(ctx, `
		SELECT id, name, auth_type, username, password, ssh_key,
			   description, status, created_at, updated_at, user_id
		FROM git_auths
		WHERE id = ?
	`, id).Scan(
		&g.ID, &g.Name, &g.AuthType, &g.Username, &password, &sshKey,
		&g.Description, &g.Status, &g.CreatedAt, &g.UpdatedAt, &userID,
	)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("查询Git配置失败: %v", err)
	}

	// 处理NULL值
	if password.Valid {
		g.Password = password.String
	}
	if sshKey.Valid {
		g.SSHKey = sshKey.String
	}
	if userID.Valid {
		g.UserID = &userID.Int64
	}

	// 解密敏感信息
	if err := g.DecryptSensitiveData(); err != nil {
		log.Printf("解密Git配置敏感信息失败: %v", err)
		// 解密失败时清空敏感信息
		g.Password = ""
		g.SSHKey = ""
	}

	return &g, nil
}

// GetGitAuthByIDRaw 获取Git认证配置原始数据（不解密敏感信息）
func GetGitAuthByIDRaw(id int64) (*GitAuth, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	query := `
		SELECT id, name, auth_type, username, password, ssh_key,
			   description, status, created_at, updated_at, user_id
		FROM git_auths
		WHERE id = ?
	`

	row := db.QueryRowContext(ctx, query, id)

	var g GitAuth
	var password sql.NullString
	var sshKey sql.NullString
	var userID sql.NullInt64

	err := row.Scan(
		&g.ID, &g.Name, &g.AuthType, &g.Username,
		&password, &sshKey, &g.Description,
		&g.Status, &g.CreatedAt, &g.UpdatedAt, &userID,
	)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("查询Git配置失败: %v", err)
	}

	// 处理NULL值
	if password.Valid {
		g.Password = password.String
	}
	if sshKey.Valid {
		g.SSHKey = sshKey.String
	}
	if userID.Valid {
		g.UserID = &userID.Int64
	}

	return &g, nil
}

// UpdateGitAuth 更新Git认证配置
func UpdateGitAuth(g *GitAuth) error {
	return withRetry(func() error {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		g.UpdatedAt = time.Now()

		// 加密敏感信息
		if err := g.EncryptSensitiveData(); err != nil {
			return err
		}

		query := `
			UPDATE git_auths
			SET name = ?, auth_type = ?, username = ?,
				password = ?, ssh_key = ?, description = ?,
				status = ?, updated_at = ?
			WHERE id = ?
		`

		_, err := db.ExecContext(ctx, query,
			g.Name, g.AuthType, g.Username, g.Password, g.SSHKey,
			g.Description, g.Status, g.UpdatedAt, g.ID,
		)
		if err != nil {
			return fmt.Errorf("更新Git认证配置失败: %v", err)
		}

		log.Printf("Git认证配置更新成功，ID: %d", g.ID)
		return nil
	})
}

// DeleteGitAuth 删除Git认证配置
func DeleteGitAuth(id int64) error {
	return withRetry(func() error {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		result, err := db.ExecContext(ctx, "DELETE FROM git_auths WHERE id = ?", id)
		if err != nil {
			return fmt.Errorf("删除Git认证配置失败: %v", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return fmt.Errorf("获取删除结果失败: %v", err)
		}

		if rowsAffected == 0 {
			return fmt.Errorf("Git认证配置不存在")
		}

		log.Printf("Git认证配置删除成功，ID: %d", id)
		return nil
	})
}

// GetActiveGitAuths 获取活跃的Git认证配置列表（用于下拉选择）
func GetActiveGitAuths() ([]GitAuth, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	query := `
		SELECT id, name, auth_type, description
		FROM git_auths
		WHERE status = 'active'
		ORDER BY name ASC
	`

	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("查询活跃Git认证配置列表失败: %v", err)
	}
	defer rows.Close()

	var configs []GitAuth
	for rows.Next() {
		var g GitAuth
		err := rows.Scan(
			&g.ID, &g.Name, &g.AuthType, &g.Description,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描Git认证配置数据失败: %v", err)
		}

		configs = append(configs, g)
	}

	return configs, nil
}



// TestGitAuth 测试Git认证
func TestGitAuth(g *GitAuth) (*GitAuthTest, error) {
	startTime := time.Now()

	// 使用默认的测试仓库
	testRepoURL := "https://github.com/octocat/Hello-World.git"

	// 解密敏感信息用于测试
	testConfig := *g
	if err := testConfig.DecryptSensitiveData(); err != nil {
		return &GitAuthTest{
			Success: false,
			Message: fmt.Sprintf("解密配置失败: %v", err),
			Latency: time.Since(startTime).Milliseconds(),
		}, nil
	}

	// 根据认证类型进行不同的测试
	var cmd *exec.Cmd
	switch testConfig.AuthType {
	case "none":
		// 公开仓库，直接测试ls-remote
		cmd = exec.Command("git", "ls-remote", "--heads", testRepoURL)
	case "token":
		// 使用token认证
		if testConfig.Password == "" {
			return &GitAuthTest{
				Success: false,
				Message: "Token不能为空",
				Latency: time.Since(startTime).Milliseconds(),
			}, nil
		}
		// 构建带token的URL
		authURL := strings.Replace(testRepoURL, "https://", fmt.Sprintf("https://%s@", testConfig.Password), 1)
		cmd = exec.Command("git", "ls-remote", "--heads", authURL)
	case "username":
		// 用户名密码认证
		if testConfig.Username == "" || testConfig.Password == "" {
			return &GitAuthTest{
				Success: false,
				Message: "用户名和密码不能为空",
				Latency: time.Since(startTime).Milliseconds(),
			}, nil
		}
		// 构建带认证的URL
		authURL := strings.Replace(testRepoURL, "https://", fmt.Sprintf("https://%s:%s@", testConfig.Username, testConfig.Password), 1)
		cmd = exec.Command("git", "ls-remote", "--heads", authURL)
	case "ssh":
		// SSH密钥认证（需要更复杂的处理）
		if testConfig.SSHKey == "" {
			return &GitAuthTest{
				Success: false,
				Message: "SSH密钥不能为空",
				Latency: time.Since(startTime).Milliseconds(),
			}, nil
		}
		// 简单测试SSH连接（实际应用中需要临时写入密钥文件）
		cmd = exec.Command("git", "ls-remote", "--heads", testRepoURL)
	default:
		return &GitAuthTest{
			Success: false,
			Message: "不支持的认证类型",
			Latency: time.Since(startTime).Milliseconds(),
		}, nil
	}

	// 设置超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	cmd = exec.CommandContext(ctx, cmd.Args[0], cmd.Args[1:]...)

	// 执行命令
	output, err := cmd.CombinedOutput()
	latency := time.Since(startTime).Milliseconds()

	if err != nil {
		return &GitAuthTest{
			Success: false,
			Message: fmt.Sprintf("连接失败: %v, 输出: %s", err, string(output)),
			Latency: latency,
		}, nil
	}

	// 更新测试结果到数据库
	now := time.Now()
	updateQuery := `
		UPDATE git_auths
		SET last_test_time = ?, last_test_result = ?
		WHERE id = ?
	`
	_, err = db.Exec(updateQuery, now, "success", g.ID)
	if err != nil {
		log.Printf("更新Git认证配置测试结果失败: %v", err)
	}

	return &GitAuthTest{
		Success: true,
		Message: "连接成功",
		Latency: latency,
	}, nil
}

// GetGitBranches 获取Git仓库分支列表
func GetGitBranches(config *GitAuth, repoURL string) ([]string, error) {
	// 解密敏感数据
	testConfig := *config
	if err := testConfig.DecryptSensitiveData(); err != nil {
		return nil, fmt.Errorf("解密配置数据失败: %v", err)
	}

	// 根据认证类型构建git命令
	var cmd *exec.Cmd
	switch testConfig.AuthType {
	case "none":
		// 公开仓库
		cmd = exec.Command("git", "ls-remote", "--heads", repoURL)
	case "token":
		if testConfig.Password == "" {
			return nil, fmt.Errorf("Token不能为空")
		}
		// 构建带token的URL
		authURL := strings.Replace(repoURL, "https://", fmt.Sprintf("https://%s@", testConfig.Password), 1)
		cmd = exec.Command("git", "ls-remote", "--heads", authURL)
	case "username":
		if testConfig.Username == "" || testConfig.Password == "" {
			return nil, fmt.Errorf("用户名和密码不能为空")
		}
		// 构建带认证的URL
		authURL := strings.Replace(repoURL, "https://", fmt.Sprintf("https://%s:%s@", testConfig.Username, testConfig.Password), 1)
		cmd = exec.Command("git", "ls-remote", "--heads", authURL)
	case "ssh":
		// SSH认证暂不支持
		return nil, fmt.Errorf("SSH认证暂不支持")
	default:
		return nil, fmt.Errorf("不支持的认证类型: %s", testConfig.AuthType)
	}

	// 设置超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	cmd = exec.CommandContext(ctx, cmd.Args[0], cmd.Args[1:]...)

	// 执行命令
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("获取分支列表失败: %v, 输出: %s", err, string(output))
	}

	// 解析输出
	lines := strings.Split(string(output), "\n")
	var branches []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		// 格式: hash refs/heads/branch-name
		parts := strings.Fields(line)
		if len(parts) >= 2 && strings.HasPrefix(parts[1], "refs/heads/") {
			branchName := strings.TrimPrefix(parts[1], "refs/heads/")
			branches = append(branches, branchName)
		}
	}

	return branches, nil
}

// GetGitTags 获取Git仓库标签列表
func GetGitTags(config *GitAuth, repoURL string) ([]string, error) {
	// 解密敏感数据
	testConfig := *config
	if err := testConfig.DecryptSensitiveData(); err != nil {
		return nil, fmt.Errorf("解密配置数据失败: %v", err)
	}

	// 根据认证类型构建git命令
	var cmd *exec.Cmd
	switch testConfig.AuthType {
	case "none":
		// 公开仓库
		cmd = exec.Command("git", "ls-remote", "--tags", repoURL)
	case "token":
		if testConfig.Password == "" {
			return nil, fmt.Errorf("Token不能为空")
		}
		// 构建带token的URL
		authURL := strings.Replace(repoURL, "https://", fmt.Sprintf("https://%s@", testConfig.Password), 1)
		cmd = exec.Command("git", "ls-remote", "--tags", authURL)
	case "username":
		if testConfig.Username == "" || testConfig.Password == "" {
			return nil, fmt.Errorf("用户名和密码不能为空")
		}
		// 构建带认证的URL
		authURL := strings.Replace(repoURL, "https://", fmt.Sprintf("https://%s:%s@", testConfig.Username, testConfig.Password), 1)
		cmd = exec.Command("git", "ls-remote", "--tags", authURL)
	case "ssh":
		// SSH认证暂不支持
		return nil, fmt.Errorf("SSH认证暂不支持")
	default:
		return nil, fmt.Errorf("不支持的认证类型: %s", testConfig.AuthType)
	}

	// 设置超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	cmd = exec.CommandContext(ctx, cmd.Args[0], cmd.Args[1:]...)

	// 执行命令
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("获取标签列表失败: %v, 输出: %s", err, string(output))
	}

	// 解析输出
	lines := strings.Split(string(output), "\n")
	var tags []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		// 格式: hash refs/tags/tag-name
		parts := strings.Fields(line)
		if len(parts) >= 2 && strings.HasPrefix(parts[1], "refs/tags/") {
			tagName := strings.TrimPrefix(parts[1], "refs/tags/")
			// 过滤掉^{}结尾的标签（这些是带注释的标签的对象引用）
			if !strings.HasSuffix(tagName, "^{}") {
				tags = append(tags, tagName)
			}
		}
	}

	return tags, nil
}
