package robobus_wx

import (
	"ccapi/controllers"
	"ccapi/models/dto"
	"ccapi/models/robobus_wx"
	"ccapi/pkg"
	robobus_wx_service "ccapi/service/robobus_wx"
	beego "github.com/beego/beego/v2/server/web"
)

type BroadcastRuleController struct {
	controllers.BaseController
	broadcastRuleService robobus_wx_service.BroadcastRuleServiceInter
}

func (c *BroadcastRuleController) Prepare() {
	c.broadcastRuleService = robobus_wx_service.NewBroadcastRuleService()
}

// Page 列表查询
func (c *BroadcastRuleController) Page() {
	var req dto.BroadcastRuleQueryDTO
	if err := pkg.UnmarshalQuery(c.GetString, &req); err != nil {
		c.Fail(err.Error())
		return
	}

	page, err := c.broadcastRuleService.Page(req, c.Ctx)
	if err != nil {
		c.Fail(err.Error())
		return
	}
	c.Success(page)
}

// Save 新增
func (c *BroadcastRuleController) Save() {
	var req dto.BroadcastRuleCreateDTO
	if err := pkg.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		c.Fail(err.Error())
		return
	}

	if err := req.Validate(); err != nil {
		c.Fail(err.Error())
		return
	}

	rule := &robobus_wx.BroadcastRule{
		RuleName:          req.RuleName,
		BroadcastType:     req.BroadcastType,
		ContentID:         req.ContentID,
		BroadcastPosition: req.BroadcastPosition,
		Priority:          req.Priority,
		Status:            1,
	}

	if err := c.broadcastRuleService.Save(rule); err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(nil)
}

// Update 更新
func (c *BroadcastRuleController) Update() {
	var req dto.BroadcastRuleUpdateDTO
	if err := pkg.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		c.Fail(err.Error())
		return
	}

	if err := req.Validate(); err != nil {
		c.Fail(err.Error())
		return
	}

	rule := &robobus_wx.BroadcastRule{
		ID:                req.ID,
		RuleName:          req.RuleName,
		BroadcastType:     req.BroadcastType,
		ContentID:         req.ContentID,
		BroadcastPosition: req.BroadcastPosition,
		Priority:          req.Priority,
		Status:            req.Status,
	}

	if err := c.broadcastRuleService.Update(rule); err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(nil)
}

// Delete 删除
func (c *BroadcastRuleController) Delete() {
	id, _ := c.GetInt64("id")

	if err := c.broadcastRuleService.Delete(id); err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(nil)
}

func InitBroadcastRuleRouter() beego.LinkNamespace {
	return beego.NSNamespace("/operation/broadcast-rule",
		beego.NSRouter("/page", &BroadcastRuleController{}, "get:Page"),
		beego.NSRouter("/save", &BroadcastRuleController{}, "post:Save"),
		beego.NSRouter("/update", &BroadcastRuleController{}, "put:Update"),
		beego.NSRouter("/delete", &BroadcastRuleController{}, "delete:Delete"),
	)
}
