package robobus_wx

import (
	"ccapi/controllers"
	"ccapi/models/robobus_wx"
	"ccapi/pkg"
	"ccapi/pkg/orm_helper"
	"ccapi/pkg/types"
	rs "ccapi/service/robobus_wx"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
	beego "github.com/beego/beego/v2/server/web"
	"time"
)

type RouteTimeSlotController struct {
	controllers.BaseController
	timeSlotService *rs.RouteTimeSlotService
}

func (r *RouteTimeSlotController) TimeSlotPage() {
	sql := `select rts.id,rts.route_id,rts.weekday,rts.name,rbr.booking_start_time,rbr.booking_end_time,
		rts.start_time,rts.end_time,rbr.max_orders_per_user,rbr.max_tickets_per_order,rts.max_tickets
		from route_time_slot rts
		left join route_booking_rule rbr on rts.id = rbr.time_slot_id
		left join route_ticket_stock rtss on rtss.time_slot_id = rts.id`

	routeId, _ := r.GetInt64("routeId", -1)
	if routeId == -1 {
		r.Fail("routeId 不能为空")
		return
	}

	o := orm.NewOrmUsingDB("robobusWX")
	timeSlots, err := orm_helper.NewQueryBuilder[robobus_wx.RouteTimeSlotVO](o, r.Ctx, sql).Where().Eq("rts.route_id", routeId).Result()
	if err != nil {
		r.Fail(err.Error())
		return
	}
	r.Success(timeSlots)
}

// 废弃，新增功能在编辑中使用
func (r *RouteTimeSlotController) AddTimeSlot() {

	var addTimeSlot robobus_wx.AddRouteTimeSlot
	err2 := pkg.Unmarshal(r.Ctx.Input.RequestBody, &addTimeSlot)
	if err2 != nil {
		fmt.Println(err2)
		return
	}

	o := orm.NewOrmUsingDB("robobusWX")

	// 开启事务
	tx, err := o.Begin()
	if err != nil {
		r.Fail(err.Error())
		return
	}

	// 使用 defer 确保在函数返回时进行事务的提交或回滚
	defer func() {
		if r := recover(); r != nil {
			// 发生 panic 时回滚事务
			_ = tx.Rollback()
			panic(r) // 重新抛出 panic
		}
	}()

	for _, t := range addTimeSlot.Timeslots {
		timeslot := robobus_wx.RouteTimeSlot{
			RouteId:    int(addTimeSlot.RouteId),
			Weekday:    int(t.Weekday),
			Name:       t.Name,
			StartTime:  t.StartTime.String(),
			EndTime:    t.EndTime.String(),
			Status:     1,
			MaxTickets: t.MaxTickets,
		}

		// 使用事务插入时间段
		_, err = tx.Insert(&timeslot)
		if err != nil {
			_ = tx.Rollback()
			fmt.Println(err)
			r.Fail(err.Error())
			return
		}

		bookingRule := robobus_wx.RouteBookingRule{
			RouteId:            addTimeSlot.RouteId,
			TimeSlotId:         int64(timeslot.Id),
			BookingStartTime:   t.BookingStartTime,
			BookingEndTime:     t.BookingEndTime,
			MaxOrdersPerUser:   t.MaxOrdersPerUser,
			MaxTicketsPerOrder: t.MaxTicketsPerOrder,
			Status:             1,
		}

		// 使用事务插入预订规则
		_, err = tx.Insert(&bookingRule)
		if err != nil {
			_ = tx.Rollback()
			fmt.Println(err)
			r.Fail(err.Error())
			return
		}
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		_ = tx.Rollback()
		fmt.Println(err)
		r.Fail(err.Error())
		return
	}

	// 成功响应
	r.Success(addTimeSlot.RouteId)
}

func (r *RouteTimeSlotController) EditTimeSlot() {
	var editTimeSlot robobus_wx.AddRouteTimeSlot
	if err := pkg.Unmarshal(r.Ctx.Input.RequestBody, &editTimeSlot); err != nil {
		r.Fail(err.Error())
		return
	}

	o := orm.NewOrmUsingDB("robobusWX")
	tx, err := o.Begin()
	if err != nil {
		r.Fail(err.Error())
		return
	}

	var txErr error
	defer func() {
		if r := recover(); r != nil {
			_ = tx.Rollback()
			panic(r)
		}

		if txErr != nil {
			logs.Error("事务执行失败: %v", txErr)
			_ = tx.Rollback()
			r.Fail(txErr.Error())
		} else {
			if err := tx.Commit(); err != nil {
				logs.Error("事务提交失败: %v", err)
				_ = tx.Rollback()
				r.Fail(fmt.Sprintf("提交事务失败: %v", err))
			} else {
				logs.Info("事务提交成功")
				r.Success(editTimeSlot.RouteId)
			}
		}
	}()

	// 1. 获取数据库中现有的未删除的时间段列表
	var existingTimeSlots []robobus_wx.RouteTimeSlot
	if _, err := tx.QueryTable("route_time_slot").
		Filter("route_id", editTimeSlot.RouteId).
		Filter("is_delete", 0).
		All(&existingTimeSlots); err != nil {
		txErr = fmt.Errorf("查询现有时间段失败: %v", err)
		return
	}
	logs.Info("查询到现有时间段数量: %d", len(existingTimeSlots))

	// 2. 创建映射，标记要保留和要删除的记录
	existingMap := make(map[int64]robobus_wx.RouteTimeSlot)
	toDeleteIds := make(map[int64]bool)
	newMap := make(map[int64]bool)

	// 记录新参数中的ID
	logs.Info("开始处理新参数列表，共 %d 条记录", len(editTimeSlot.Timeslots))
	for i, t := range editTimeSlot.Timeslots {
		logs.Info("处理第 %d 条记录: ID=%d, Name=%s, Weekday=%d", i+1, t.Id, t.Name, t.Weekday)
		if t.Id > 0 {
			newMap[t.Id] = true
		}
	}

	// 标记要删除的记录
	for _, ts := range existingTimeSlots {
		existingMap[int64(ts.Id)] = ts
		if !newMap[int64(ts.Id)] {
			logs.Info("标记要删除的记录: ID=%d, Name=%s", ts.Id, ts.Name)
			toDeleteIds[int64(ts.Id)] = true
		}
	}

	// 3. 检查时段名称重复和时间重叠
	for _, t := range editTimeSlot.Timeslots {
		operation := "新增"
		if t.Id > 0 {
			operation = "编辑"
		}

		// 3.1 检查名称重复
		existingSlot, err := r.timeSlotService.CheckNameDuplicate(
			tx,
			editTimeSlot.RouteId,
			t.Weekday,
			t.Name,
			t.Id,
			toDeleteIds,
		)
		if err != nil {
			txErr = err
			return
		}
		if existingSlot != nil {
			txErr = fmt.Errorf(r.timeSlotService.FormatNameDuplicateError(
				operation,
				t.Weekday,
				t.Name,
				existingSlot,
			))
			return
		}

		// 3.2 检查时间重叠
		overlappingSlot, err := r.timeSlotService.CheckTimeOverlap(
			tx,
			editTimeSlot.RouteId,
			t.Weekday,
			t.StartTime,
			t.EndTime,
			t.Id,
			toDeleteIds,
		)
		if err != nil {
			txErr = err
			return
		}
		if overlappingSlot != nil {
			txErr = fmt.Errorf(r.timeSlotService.FormatTimeOverlapError(
				operation,
				t.Weekday,
				t.Name,
				t.StartTime,
				t.EndTime,
				overlappingSlot,
			))
			return
		}
	}

	// 4. 执行软删除操作
	for id := range toDeleteIds {
		logs.Info("执行软删除操作: ID=%d", id)
		existing := existingMap[id]
		existing.IsDelete = 1
		if _, err := tx.Update(&existing, "IsDelete", "UpdatedTime"); err != nil {
			txErr = fmt.Errorf("软删除时间段失败: %v", err)
			return
		}

		// 软删除对应的预订规则
		bookingRule := robobus_wx.RouteBookingRule{TimeSlotId: int64(id), RouteId: editTimeSlot.RouteId}
		if err := tx.Read(&bookingRule, "TimeSlotId", "RouteId"); err == nil {
			bookingRule.IsDelete = 1
			if _, err := tx.Update(&bookingRule, "IsDelete", "UpdatedTime"); err != nil {
				txErr = fmt.Errorf("软删除预订规则失败: %v", err)
				return
			}
			logs.Info("软删除对应的预订规则成功: TimeSlotId=%d", id)
		}
	}

	// 5. 处理新增和更新操作
	for _, t := range editTimeSlot.Timeslots {
		if t.Id <= 0 {
			// 新增操作
			logs.Info("执行新增操作: Name=%s, Weekday=%d", t.Name, t.Weekday)
			timeSlot := robobus_wx.RouteTimeSlot{
				RouteId:    int(editTimeSlot.RouteId),
				Weekday:    int(t.Weekday),
				Name:       t.Name,
				StartTime:  t.StartTime.String(),
				EndTime:    t.EndTime.String(),
				MaxTickets: t.MaxTickets,
				Status:     1,
				IsDelete:   0,
			}

			id, err := tx.Insert(&timeSlot)
			if err != nil {
				txErr = fmt.Errorf("新增时间段失败: %v", err)
				return
			}
			logs.Info("新增时间段成功，ID=%d", id)

			bookingRule := robobus_wx.RouteBookingRule{
				TimeSlotId:         int64(id),
				RouteId:            int64(editTimeSlot.RouteId),
				BookingStartTime:   t.BookingStartTime,
				BookingEndTime:     t.BookingEndTime,
				MaxOrdersPerUser:   t.MaxOrdersPerUser,
				MaxTicketsPerOrder: t.MaxTicketsPerOrder,
				Status:             1,
				IsDelete:           0,
			}

			if _, err := tx.Insert(&bookingRule); err != nil {
				txErr = fmt.Errorf("新增预订规则失败: %v", err)
				return
			}
			logs.Info("新增预订规则成功")
		} else {
			// 更新操作
			logs.Info("执行更新操作: ID=%d, Name=%s", t.Id, t.Name)
			timeSlot := robobus_wx.RouteTimeSlot{Id: int(t.Id)}
			if err := tx.Read(&timeSlot); err != nil {
				txErr = fmt.Errorf("读取时间段失败: %v", err)
				return
			}

			timeSlot.Weekday = int(t.Weekday)
			timeSlot.Name = t.Name
			timeSlot.StartTime = t.StartTime.String()
			timeSlot.EndTime = t.EndTime.String()
			timeSlot.MaxTickets = t.MaxTickets

			if _, err := tx.Update(&timeSlot, "Weekday", "Name", "StartTime", "EndTime", "MaxTickets", "UpdatedTime"); err != nil {
				txErr = fmt.Errorf("更新时间段失败: %v", err)
				return
			}
			logs.Info("更新时间段成功")

			bookingRule := robobus_wx.RouteBookingRule{TimeSlotId: int64(t.Id), RouteId: int64(editTimeSlot.RouteId)}
			if err := tx.Read(&bookingRule, "TimeSlotId", "RouteId"); err != nil {
				if errors.Is(err, orm.ErrNoRows) {
					// 创建新的预订规则
					bookingRule = robobus_wx.RouteBookingRule{
						TimeSlotId:         int64(t.Id),
						RouteId:            int64(editTimeSlot.RouteId),
						BookingStartTime:   t.BookingStartTime,
						BookingEndTime:     t.BookingEndTime,
						MaxOrdersPerUser:   t.MaxOrdersPerUser,
						MaxTicketsPerOrder: t.MaxTicketsPerOrder,
						Status:             1,
						IsDelete:           0,
					}

					if _, err := tx.Insert(&bookingRule); err != nil {
						txErr = fmt.Errorf("创建预订规则失败: %v", err)
						return
					}
					logs.Info("新增预订规则成功")
				} else {
					txErr = fmt.Errorf("读取预订规则失败: %v", err)
					return
				}
			} else {
				bookingRule.BookingStartTime = t.BookingStartTime
				bookingRule.BookingEndTime = t.BookingEndTime
				bookingRule.MaxOrdersPerUser = t.MaxOrdersPerUser
				bookingRule.MaxTicketsPerOrder = t.MaxTicketsPerOrder

				if _, err := tx.Update(&bookingRule, "BookingStartTime", "BookingEndTime", "MaxOrdersPerUser", "MaxTicketsPerOrder", "UpdatedTime"); err != nil {
					txErr = fmt.Errorf("更新预订规则失败: %v", err)
					return
				}
				logs.Info("更新预订规则成功")
			}
		}
	}
}

func (r *RouteTimeSlotController) TimeSlotTicketPage() {
	userInfo, err := r.CheckToken()
	if err != nil {
		r.Fail("用户身份校验失败：" + err.Error())
		return
	}
	userId := userInfo.Id

	// 修正SQL查询，使用正确的SQL注释语法
	sql := `SELECT 
            rt_stock.id AS id,
            route.id AS route_id,
            route.name AS route_name,  -- 确保获取路线名称
            rt_stock.id AS stock_id,
            rts.name AS time_slot_name,
            rt_stock.travel_date AS travel_date,
            rts.start_time AS start_time,
            rts.end_time AS end_time,
            rt_stock.total_stock AS total_stock,
            rt_stock.sold_count AS sold_count,
            (rt_stock.total_stock - rt_stock.sold_count) AS remain_count
        FROM route 
        INNER JOIN route_time_slot rts ON route.id = rts.route_id
        INNER JOIN route_ticket_stock rt_stock ON rt_stock.time_slot_id = rts.id
        WHERE rts.is_delete = 0 AND route.is_delete = 0`

	// 数据权限控制
	if userId != 1 {
		sql += fmt.Sprintf(" AND route.fms_user_id = %d", userId)
	}

	// 处理查询参数
	var query robobus_wx.TicketStockQuery
	if err = pkg.UnmarshalQuery(r.GetString, &query); err != nil {
		r.Fail(err.Error())
		return
	}

	// 手动构建条件
	var params []interface{}
	if query.RouteName != "" {
		sql += " AND route.name LIKE ?"
		params = append(params, "%"+query.RouteName+"%")
	}

	// 处理日期条件 - 确保正确处理前端传来的Unix时间戳
	if query.TravelDate.MyTimestamp.Unix() > 0 {
		sql += " AND rt_stock.travel_date = ?"
		dateStr := query.TravelDate.MyTimestamp.Format("2006-01-02")
		params = append(params, dateStr)
	}

	// 调试输出SQL
	fmt.Println("Executing SQL:", sql)
	fmt.Println("With params:", params)

	// 执行查询
	o := orm.NewOrmUsingDB("robobusWX")

	// 使用中间结构体接收结果
	type QueryResult struct {
		Id           int64     `orm:"column(id)"`
		RouteId      int64     `orm:"column(route_id)"`
		RouteName    string    `orm:"column(route_name)"`
		StockId      int64     `orm:"column(stock_id)"`
		TimeSlotName string    `orm:"column(time_slot_name)"`
		TravelDate   time.Time `orm:"column(travel_date)"`
		StartTime    string    `orm:"column(start_time)"`
		EndTime      string    `orm:"column(end_time)"`
		TotalStock   int       `orm:"column(total_stock)"`
		SoldCount    int       `orm:"column(sold_count)"`
		RemainCount  int       `orm:"column(remain_count)"`
	}

	var queryResults []QueryResult
	_, err = o.Raw(sql, params...).QueryRows(&queryResults)
	if err != nil {
		r.Fail("查询失败：" + err.Error())
		return
	}

	// 转换为前端需要的格式
	routeMap := make(map[int64]*robobus_wx.TicketStockVO)
	for _, item := range queryResults {
		// 初始化路线信息
		if _, exists := routeMap[item.RouteId]; !exists {
			routeMap[item.RouteId] = &robobus_wx.TicketStockVO{
				Id:        item.RouteId,
				RouteName: item.RouteName,
				Tickets:   []*robobus_wx.TicketStockListVO{},
			}
		}

		// 转换时间字段
		travelDate := types.MyUnixDate{MyTimestamp: item.TravelDate}
		startTime, _ := time.Parse("15:04:05", item.StartTime)
		endTime, _ := time.Parse("15:04:05", item.EndTime)

		// 添加票务信息
		ticket := &robobus_wx.TicketStockListVO{
			Id:                item.Id,
			RouteId:           item.RouteId,
			StockId:           item.StockId,
			TimeSlotName:      item.TimeSlotName,
			TravelDate:        travelDate,
			TimeSlotStartTime: types.MyUnixTime{MyTimestamp: startTime},
			TimeSlotEndTime:   types.MyUnixTime{MyTimestamp: endTime},
			TotalStock:        item.TotalStock,
			SoldCount:         item.SoldCount,
			RemainCount:       item.RemainCount,
		}

		routeMap[item.RouteId].Tickets = append(routeMap[item.RouteId].Tickets, ticket)
	}

	// 转换为列表
	var list []robobus_wx.TicketStockVO
	for _, vo := range routeMap {
		list = append(list, *vo)
	}

	// 分页处理
	page := map[string]interface{}{
		"list":  list,
		"total": len(list),
	}
	r.Success(page)
}

func (r *RouteTimeSlotController) EditTimeSlotTicket() {
	var editTicketStock robobus_wx.TicketStockListVO
	err := json.Unmarshal(r.Ctx.Input.RequestBody, &editTicketStock)
	if err != nil {
		r.Fail(err.Error())
		return
	}
	o := orm.NewOrmUsingDB("robobusWX")

	// 修改：不再使用QueryBuilder，直接使用标准ORM API获取对象
	var slotTicket robobus_wx.RouteTicketStock
	err = o.QueryTable("route_ticket_stock").Filter("id", editTicketStock.Id).One(&slotTicket)
	if err != nil {
		r.Fail(err.Error())
		return
	}

	remainCount := slotTicket.TotalStock - slotTicket.SoldCount
	slotTicket.TotalStock = slotTicket.TotalStock + (editTicketStock.RemainCount - remainCount)
	_, err = o.Update(&slotTicket, "total_stock")
	if err != nil {
		r.Fail(err.Error())
		return
	}
	r.Success(editTicketStock.Id)
}

func InitRouteTimeSlot() beego.LinkNamespace {
	return beego.NSNamespace("/operation",
		beego.NSRouter("/time_slot_page", &RouteTimeSlotController{}, "get:TimeSlotPage"),
		beego.NSRouter("/add_time_slot", &RouteTimeSlotController{}, "post:AddTimeSlot"),
		beego.NSRouter("/edit_time_slot", &RouteTimeSlotController{}, "put:EditTimeSlot"),
		beego.NSRouter("/ticket_page", &RouteTimeSlotController{}, "get:TimeSlotTicketPage"),
		beego.NSRouter("/edit_timeslot_ticket", &RouteTimeSlotController{}, "put:EditTimeSlotTicket"),
	)
}
