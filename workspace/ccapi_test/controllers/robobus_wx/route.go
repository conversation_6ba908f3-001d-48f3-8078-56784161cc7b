package robobus_wx

import (
	"ccapi/common"
	"ccapi/controllers"
	"ccapi/models/dto"
	"ccapi/pkg"
	"ccapi/service/robobus_wx"
	beego "github.com/beego/beego/v2/server/web"
	"strconv"
)

type RouteController struct {
	controllers.BaseController
}

// @Title 获取所有路线
// @Description 获取所有路线列表
// @router /api/wx/route/list [get]
func (c *RouteController) List() {
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("用户身份校验失败：" + err.Error())
		return
	}
	city := c.GetString("city")
	list, err := robobus_wx.GetAllRoutes(userInfo.Id, city)
	if err != nil {
		c.Fail(err.Error())
		return
	}
	c.Success(list)
}

// 运营平台删除路线功能
func (c *RouteController) Delete() {
	// 校验用户身份
	userInfo, err := c.CheckToken()
	if err != nil {
		c.<PERSON>ail("用户身份校验失败：" + err.Error())
		return
	}
	// 获取路线ID
	routeIdStr := c.GetString("id")
	if routeIdStr == "" {
		c.Fail("路线ID不能为空")
		return
	}

	routeId, err := strconv.Atoi(routeIdStr)
	if err != nil {
		c.Fail("路线ID格式错误")
		return
	}

	// 执行删除操作
	err = robobus_wx.DeleteRoute(routeId, userInfo.Id)
	if err != nil {
		c.Fail("删除路线失败：" + err.Error())
		return
	}

	c.Success("路线删除成功")
}

func (c *RouteController) Create() {
	// 校验用户身份
	userInfo, err1 := c.CheckToken()
	if err1 != nil {
		c.Fail("用户身份校验失败：" + err1.Error())
		return
	}
	var (
		err error
		dto dto.RouteCreateDTO
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &dto)
	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	// 解析请求参数
	//var dto dto.RouteCreateDTO
	//err = c.ParseForm(&dto)
	//if err != nil {
	//	c.Fail("参数解析失败：" + err.Error())
	//	return
	//}

	// 补充操作人ID
	dto.OperatorId = userInfo.Id

	// 执行创建操作
	_, err = robobus_wx.CreateRoute(dto)
	if err != nil {
		c.Fail("创建路线失败：" + err.Error())
		return
	}

	c.Success("路线创建成功")
}

// Update 更新路线
func (c *RouteController) Update() {
	// 校验用户身份
	userInfo, err1 := c.CheckToken()
	if err1 != nil {
		c.Fail("用户身份校验失败：" + err1.Error())
		return
	}
	var (
		err error
		dto dto.RouteUpdateDTO
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &dto)
	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	// 补充操作人ID
	dto.OperatorId = userInfo.Id

	// 执行更新操作
	err = robobus_wx.UpdateRoute(dto)
	if err != nil {
		c.Fail("更新路线失败：" + err.Error())
		return
	}

	c.Success("路线更新成功")
}

func (c *RouteController) ListByQuery() {
	// 解析查询参数
	var query dto.RouteQuerryDTO
	if err := pkg.UnmarshalQuery(c.GetString, &query); err != nil {
		c.Fail("获取参数异常：" + err.Error())
		return
	}

	// 设置默认分页参数
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.Limit <= 0 {
		query.Limit = 10
	}

	// 调用服务层获取数据
	list, total, err := robobus_wx.ListByQuery(query)
	if err != nil {
		c.Fail("查询路线失败：" + err.Error())
		return
	}

	// 返回分页结果
	c.Success(map[string]interface{}{
		"items": list,
		"total": total,
		"page":  query.Page,
		"limit": query.Limit,
	})
}

func InitRouteRouter() beego.LinkNamespace {
	return beego.NSNamespace("/wx",
		beego.NSRouter("/route/list", &RouteController{}, "get:List"),
		beego.NSRouter("/route/delete", &RouteController{}, "get:Delete"),
		beego.NSRouter("/route/update", &RouteController{}, "post:Update"),
		beego.NSRouter("/route/create", &RouteController{}, "post:Create"),
		beego.NSRouter("/route/listByQuery", &RouteController{}, "get:ListByQuery"),
	)
}
