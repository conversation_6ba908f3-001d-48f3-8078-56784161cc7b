package auth

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt"
)

// JWTClaims 自定义的 JWT Claims
type JWTClaims struct {
	UID       int64  `json:"uid"`
	SessionID string `json:"session_id"`
	jwt.StandardClaims
}

// GenerateToken 生成 JWT token
func GenerateToken(uid int64, sessionId string) (string, error) {
	claims := JWTClaims{
		uid,
		sessionId,
		jwt.StandardClaims{
			ExpiresAt: time.Now().Add(TokenExpireTime).Unix(),
			IssuedAt:  time.Now().Unix(),
			Issuer:    JWTIssuer,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(JWTSecret))
}

// GenerateTokenPair 生成 token 和 refresh token
func GenerateTokenPair(uid int64, sessionId string) (string, string, error) {
	token, err := GenerateToken(uid, sessionId)
	if err != nil {
		return "", "", err
	}

	claims := JWTClaims{
		uid,
		sessionId,
		jwt.StandardClaims{
			ExpiresAt: time.Now().Add(RefreshTokenExpireTime).Unix(),
			IssuedAt:  time.Now().Unix(),
			Issuer:    JWTIssuer,
		},
	}

	refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	refreshTokenString, err := refreshToken.SignedString([]byte(JWTSecret))
	if err != nil {
		return "", "", err
	}

	return token, refreshTokenString, nil
}

// ParseToken 解析 JWT token
func ParseToken(tokenString string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(JWTSecret), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}
