package auth

import (
	"ccapi/common"
	"ccapi/models/dto"
	"ccapi/models/vo"
	"ccapi/server/redis"
	"ccapi/service"
	"fmt"
	"strconv"
	"time"

	beego "github.com/beego/beego/v2/server/web"
)

const (
	// RedisJWTTokenPrefix JWT token 在 Redis 中的前缀
	RedisJWTTokenPrefix = "jwt:token:"
	// RedisJWTRefreshTokenPrefix JWT refresh token 在 Redis 中的前缀
	RedisJWTRefreshTokenPrefix = "jwt:refresh:"
)

var (
	// TokenExpireTime token 过期时间
	TokenExpireTime time.Duration
	// RefreshTokenExpireTime refresh token 过期时间
	RefreshTokenExpireTime time.Duration
	// JWTIssuer JWT签发者
	JWTIssuer string
	// JWTSecret JWT密钥
	JWTSecret string
)

func init() {
	// 从配置文件读取 JWT 配置
	tokenExpire, err := beego.AppConfig.Int("jwt_token_expire")
	if err != nil {
		panic("读取 jwt_token_expire 配置失败: " + err.Error())
	}
	TokenExpireTime = time.Duration(tokenExpire) * time.Second

	refreshExpire, err := beego.AppConfig.Int("jwt_refresh_expire")
	if err != nil {
		panic("读取 jwt_refresh_expire 配置失败: " + err.Error())
	}
	RefreshTokenExpireTime = time.Duration(refreshExpire) * time.Second

	JWTIssuer, err = beego.AppConfig.String("jwt_issuer")
	if err != nil {
		panic("读取 jwt_issuer 配置失败: " + err.Error())
	}

	JWTSecret, err = beego.AppConfig.String("jwt_secret")
	if err != nil {
		panic("读取 jwt_secret 配置失败: " + err.Error())
	}
}

// Service JWT 认证服务接口
type Service interface {
	Login(username, password string) (int, *vo.LoginResponse, error, dto.UserInfo)
	Logout(token string) error
	RefreshToken(refreshToken string) (*vo.LoginResponse, error)
}

// authService JWT 认证服务实现
type authService struct {
	userService service.UserServiceInter
}

// VerifyToken 统一校验token,返回用户ID
func VerifyToken(token string) (int64, error) {
	if token == "" {
		return 0, fmt.Errorf("token不能为空")
	}

	// 尝试JWT验证
	claims, err := ParseToken(token)
	if err == nil {
		// JWT token验证成功
		tokenKey := fmt.Sprintf(common.RedisUserTokenKey, claims.UID, claims.SessionID)
		storedToken := redis.Get(tokenKey)
		if storedToken == "" || storedToken != token {
			return 0, fmt.Errorf("token已失效")
		}
		return claims.UID, nil
	}

	// 尝试旧的token验证方式
	key := fmt.Sprintf(common.RedisSessionMultiTokenUid, token)
	uidStr := redis.Get(key)
	if uidStr == "" {
		return 0, fmt.Errorf("token已失效")
	}

	uid, err := strconv.ParseInt(uidStr, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("token无效")
	}

	return uid, nil
}

// NewAuthService 创建 JWT 认证服务
func NewAuthService(userService service.UserServiceInter) Service {
	return &authService{
		userService: userService,
	}
}

// LoginResponse JWT登录响应
type LoginResponse struct {
	Token        string `json:"token"`
	RefreshToken string `json:"refreshToken"`
	ExpiresIn    int64  `json:"expiresIn"` // 改为时间戳
}

// Login JWT 登录
func (s *authService) Login(username, password string) (int, *vo.LoginResponse, error, dto.UserInfo) {
	code, _, err, userInfo := s.userService.ValidateUser(username, password)
	if code == 1 {
		return code, nil, err, userInfo
	}

	// 生成会话ID
	sessionId := fmt.Sprintf("%d_%s", userInfo.Id, common.RandomString("alnum", 32))

	// 生成 token 和 refresh token
	token, refreshToken, err := GenerateTokenPair(userInfo.Id, sessionId)
	if err != nil {
		return 1, nil, fmt.Errorf("生成令牌失败: %v", err), userInfo
	}

	// 存储 token 到 Redis
	tokenKey := fmt.Sprintf(common.RedisUserTokenKey, userInfo.Id, sessionId)
	err = redis.SetEx2(tokenKey, int(TokenExpireTime.Seconds()), token)
	if err != nil {
		return 1, nil, fmt.Errorf("存储令牌失败: %v", err), userInfo
	}

	// 存储 refresh token 到 Redis
	refreshTokenKey := fmt.Sprintf(common.RedisUserRefreshToken, userInfo.Id, sessionId)
	err = redis.SetEx2(refreshTokenKey, int(RefreshTokenExpireTime.Seconds()), refreshToken)
	if err != nil {
		return 1, nil, fmt.Errorf("存储刷新令牌失败: %v", err), userInfo
	}

	// 获取用户详细信息
	user, err := s.userService.GetUserByUsername(username)
	if err != nil {
		return 1, nil, fmt.Errorf("获取用户信息失败: %v", err), userInfo
	}

	// 存储用户信息和权限到Redis
	err = s.userService.StoreUserInfoToRedis(*user)
	if err != nil {
		return 1, nil, fmt.Errorf("存储用户信息失败: %v", err), userInfo
	}

	// 计算过期时间戳
	expiresAt := time.Now().Add(TokenExpireTime).Unix()

	response := &vo.LoginResponse{
		Token:        token,
		RefreshToken: refreshToken,
		ExpiresIn:    expiresAt,
	}

	return 0, response, nil, userInfo
}

// Logout JWT 登出
func (s *authService) Logout(token string) error {
	// 解析 token 获取 uid 和 sessionId
	claims, err := ParseToken(token)
	if err != nil {
		return err
	}

	// 删除 Redis 中的 token
	tokenKey := fmt.Sprintf(common.RedisUserTokenKey, claims.UID, claims.SessionID)
	redis.Del(tokenKey)

	// 删除 Redis 中的 refresh token
	refreshTokenKey := fmt.Sprintf(common.RedisUserRefreshToken, claims.UID, claims.SessionID)
	redis.Del(refreshTokenKey)

	return nil
}

// RefreshToken 刷新token
func (s *authService) RefreshToken(refreshToken string) (*vo.LoginResponse, error) {
	// 解析 refresh token 获取 uid 和 sessionId
	claims, err := ParseToken(refreshToken)
	if err != nil {
		return nil, err
	}

	// 验证 refresh token 是否存在于 Redis
	refreshTokenKey := fmt.Sprintf(common.RedisUserRefreshToken, claims.UID, claims.SessionID)
	storedRefreshToken := redis.Get(refreshTokenKey)
	if storedRefreshToken == "" || storedRefreshToken != refreshToken {
		return nil, fmt.Errorf("refresh token 已失效")
	}

	// 生成新的token对
	token, newRefreshToken, err := GenerateTokenPair(claims.UID, claims.SessionID)
	if err != nil {
		return nil, err
	}

	// 更新 Redis 中的 token
	tokenKey := fmt.Sprintf(common.RedisUserTokenKey, claims.UID, claims.SessionID)
	err = redis.SetEx2(tokenKey, int(TokenExpireTime.Seconds()), token)
	if err != nil {
		return nil, err
	}

	// 更新 Redis 中的 refreshToken
	err = redis.SetEx2(refreshTokenKey, int(RefreshTokenExpireTime.Seconds()), newRefreshToken)
	if err != nil {
		return nil, err
	}

	// 计算过期时间戳
	expiresAt := time.Now().Add(TokenExpireTime).Unix()

	return &vo.LoginResponse{
		Token:        token,
		RefreshToken: newRefreshToken,
		ExpiresIn:    expiresAt,
	}, nil
}
