/**
 * 低代码配置服务
 */

import { ref, reactive } from 'vue'
import type {
  LowCodeConfig,
  RuntimeState,
  ApiResponse,
  SearchFieldConfig,
  ColumnConfig
} from '@/types/lowcode'
import { lowCodeConfigSchema } from '@/schemas/lowcode-config.schema'
import request from '@/utils/request'

// JSON Schema验证器（简化版）
class SchemaValidator {
  validate(data: any, schema: any): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    // 简化的验证逻辑，实际项目中建议使用 ajv 等专业库
    if (schema.required) {
      for (const field of schema.required) {
        if (!(field in data)) {
          errors.push(`缺少必需字段: ${field}`)
        }
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
}

const validator = new SchemaValidator()

// 配置管理类
export class LowCodeConfigManager {
  private configs = ref<LowCodeConfig[]>([])
  private currentConfig = ref<LowCodeConfig | null>(null)
  private runtimeState = reactive<RuntimeState>({
    loading: false,
    data: [],
    total: 0,
    currentPage: 1,
    pageSize: 20,
    searchParams: {},
    selectedRows: []
  })

  constructor() {
    // 初始化
  }

  // 获取所有配置
  async getConfigs(params?: { page?: number; pageSize?: number; keyword?: string; category?: string }): Promise<LowCodeConfig[]> {
    try {
      const queryParams = new URLSearchParams()
      if (params?.page) queryParams.append('page', params.page.toString())
      if (params?.pageSize) queryParams.append('page_size', params.pageSize.toString())
      if (params?.keyword) queryParams.append('keyword', params.keyword)
      if (params?.category) queryParams.append('category', params.category)

      const response = await request.get(`/lowcode/configs?${queryParams.toString()}`)

      if (response.data.success) {
        const configs = response.data.data.list.map((item: any) => this.convertFromAPI(item))
        this.configs.value = configs
        return configs
      } else {
        throw new Error(response.data.message || '获取配置失败')
      }
    } catch (error) {
      console.error('获取配置失败:', error)
      // 如果API调用失败，返回空数组
      this.configs.value = []
      return []
    }
  }

  // 刷新配置列表
  async refreshConfigs(): Promise<void> {
    await this.getConfigs()
  }

  // 获取单个配置
  async getConfig(id: string): Promise<LowCodeConfig | null> {
    try {
      const response = await request.get(`/lowcode/configs/${id}`)

      if (response.data.success) {
        return this.convertFromAPI(response.data.data)
      } else {
        throw new Error(response.data.message || '获取配置失败')
      }
    } catch (error) {
      console.error('获取配置失败:', error)
      return null
    }
  }

  // 保存配置
  async saveConfig(config: LowCodeConfig): Promise<boolean> {
    // 验证配置
    const validation = validator.validate(config, lowCodeConfigSchema)
    if (!validation.valid) {
      console.error('配置验证失败:', validation.errors)
      throw new Error(`配置验证失败: ${validation.errors.join(', ')}`)
    }

    try {
      const isUpdate = !!(config.id && config.id.trim())
      let response

      if (isUpdate) {
        // 更新配置
        response = await request.put(`/lowcode/configs/${config.id}`, {
          name: config.name,
          description: config.description,
          config_data: {
            dataSource: config.dataSource,
            searchConfig: config.searchConfig,
            searchLayoutConfig: config.searchLayoutConfig,
            tableConfig: config.tableConfig,
            exportConfig: config.exportConfig
          }
        })
      } else {
        // 创建新配置
        response = await request.post('/lowcode/configs', {
          name: config.name,
          description: config.description,
          config_data: {
            dataSource: config.dataSource,
            searchConfig: config.searchConfig,
            searchLayoutConfig: config.searchLayoutConfig,
            tableConfig: config.tableConfig,
            exportConfig: config.exportConfig
          }
        })
      }

      if (response.data.success) {
        // 更新本地缓存
        const savedConfig = this.convertFromAPI(response.data.data)
        if (isUpdate) {
          const index = this.configs.value.findIndex(c => c.id === config.id)
          if (index !== -1) {
            this.configs.value[index] = savedConfig
          }
        } else {
          this.configs.value.push(savedConfig)
        }
        return true
      } else {
        throw new Error(response.data.message || '保存失败')
      }
    } catch (error) {
      console.error('保存配置失败:', error)
      throw error
    }
  }

  // 删除配置
  async deleteConfig(id: string): Promise<boolean> {
    try {
      const response = await request.delete(`/lowcode/configs/${id}`)

      if (response.data.success) {
        // 从本地缓存中移除
        this.configs.value = this.configs.value.filter(c => c.id !== id)
        return true
      } else {
        throw new Error(response.data.message || '删除失败')
      }
    } catch (error) {
      console.error('删除配置失败:', error)
      return false
    }
  }

  // 复制配置
  async cloneConfig(id: string, newName: string): Promise<LowCodeConfig | null> {
    try {
      const response = await request.post(`/lowcode/configs/${id}/clone`, {
        name: newName
      })

      if (response.data.success) {
        const clonedConfig = this.convertFromAPI(response.data.data)
        this.configs.value.push(clonedConfig)
        return clonedConfig
      } else {
        throw new Error(response.data.message || '克隆失败')
      }
    } catch (error) {
      console.error('克隆配置失败:', error)
      return null
    }
  }

  // 从API响应转换为前端配置格式
  private convertFromAPI(apiData: any): LowCodeConfig {
    const config: LowCodeConfig = {
      id: apiData.id,
      name: apiData.name,
      description: apiData.description,
      dataSource: apiData.config_data?.dataSource || {
        url: '/api/data/list',
        method: 'GET',
        params: {},
        headers: {},
        dynamicParams: [],
        responseMapping: {
          dataPath: 'data.list',
          totalPath: 'data.total'
        }
      },
      searchConfig: apiData.config_data?.searchConfig || [],
      searchLayoutConfig: (() => {
        const defaultConfig = {
          gap: '16px',
          labelPosition: 'top',
          buttonConfig: {
            position: 'newline',
            alignment: 'start',
            buttons: [
              {
                type: 'search',
                enabled: true
              },
              {
                type: 'reset',
                enabled: true
              },
              {
                type: 'export',
                enabled: true
              }
            ]
          }
        }

        const apiConfig = apiData.config_data?.searchLayoutConfig
        if (!apiConfig) return defaultConfig

        // 合并按钮配置
        const mergeButtons = (existingButtons: any[], defaultButtons: any[]) => {
          const merged = [...defaultButtons]

          // 用现有配置覆盖默认配置
          existingButtons?.forEach(existingBtn => {
            const index = merged.findIndex(btn => btn.type === existingBtn.type)
            if (index >= 0) {
              merged[index] = { ...merged[index], ...existingBtn }
            }
          })

          return merged
        }

        return {
          ...defaultConfig,
          ...apiConfig,
          buttonConfig: {
            ...defaultConfig.buttonConfig,
            ...apiConfig.buttonConfig,
            buttons: mergeButtons(apiConfig.buttonConfig?.buttons || [], defaultConfig.buttonConfig.buttons)
          }
        }
      })(),
      tableConfig: apiData.config_data?.tableConfig || {
        columns: []
      },
      exportConfig: apiData.config_data?.exportConfig || {
        enabled: false,
        api: {
          url: '/api/export',
          method: 'POST'
        },
        filename: '{name}_{timestamp}',
        columns: []
      },
      metadata: {
        createdBy: apiData.created_by,
        createdAt: apiData.created_at,
        updatedBy: apiData.updated_by,
        updatedAt: apiData.updated_at
      }
    }

    // 返回配置
    return config
  }

  // 获取响应式配置列表
  getConfigsRef() {
    return this.configs
  }



  // 设置当前配置
  setCurrentConfig(config: LowCodeConfig | null) {
    this.currentConfig.value = config
  }

  // 获取当前配置
  getCurrentConfig(): LowCodeConfig | null {
    return this.currentConfig.value
  }

  // 获取响应式当前配置
  getCurrentConfigRef() {
    return this.currentConfig
  }

  // 获取运行时状态
  getRuntimeState(): RuntimeState {
    return this.runtimeState
  }

  // 获取响应式运行时状态
  getRuntimeStateRef() {
    return this.runtimeState
  }

  // 更新运行时状态
  updateRuntimeState(updates: Partial<RuntimeState>) {
    Object.assign(this.runtimeState, updates)
  }

  // 生成唯一ID
  private generateId(): string {
    return 'lc_' + Date.now().toString(36) + Math.random().toString(36).substr(2)
  }


}

// 创建全局实例
export const lowCodeConfigManager = new LowCodeConfigManager()

// 导出响应式状态
export const useLowCodeConfig = () => {
  return {
    configs: lowCodeConfigManager.getConfigsRef(),
    currentConfig: lowCodeConfigManager.getCurrentConfigRef(),
    runtimeState: lowCodeConfigManager.getRuntimeStateRef(),

    // 方法
    getConfig: lowCodeConfigManager.getConfig.bind(lowCodeConfigManager),
    saveConfig: lowCodeConfigManager.saveConfig.bind(lowCodeConfigManager),
    deleteConfig: lowCodeConfigManager.deleteConfig.bind(lowCodeConfigManager),
    cloneConfig: lowCodeConfigManager.cloneConfig.bind(lowCodeConfigManager),
    setCurrentConfig: lowCodeConfigManager.setCurrentConfig.bind(lowCodeConfigManager),
    updateRuntimeState: lowCodeConfigManager.updateRuntimeState.bind(lowCodeConfigManager),
    refreshConfigs: lowCodeConfigManager.refreshConfigs.bind(lowCodeConfigManager)
  }
}
