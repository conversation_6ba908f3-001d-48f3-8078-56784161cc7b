/**
 * 低代码工具函数
 */

import type { 
  ColumnConfig, 
  SearchFieldConfig, 
  FormatterConfig,
  LowCodeConfig 
} from '@/types/lowcode'

// 深度获取对象属性值
export function getNestedValue(obj: any, path: string): any {
  if (!obj || !path) return undefined
  
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined
  }, obj)
}

// 深度设置对象属性值
export function setNestedValue(obj: any, path: string, value: any): void {
  if (!obj || !path) return
  
  const keys = path.split('.')
  const lastKey = keys.pop()!
  
  const target = keys.reduce((current, key) => {
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {}
    }
    return current[key]
  }, obj)
  
  target[lastKey] = value
}

// 格式化数据值
export function formatValue(value: any, formatter?: FormatterConfig): string {
  if (value === null || value === undefined) return ''
  
  if (!formatter) return String(value)
  
  switch (formatter.type) {
    case 'date':
      return formatDate(value, formatter.format || 'YYYY-MM-DD')
    
    case 'number':
      return formatNumber(value, formatter.precision)
    
    case 'currency':
      return formatCurrency(value, formatter.precision, formatter.prefix, formatter.suffix)
    
    case 'custom':
      if (formatter.customFunction) {
        try {
          // 安全执行自定义函数
          const func = new Function('value', 'formatter', formatter.customFunction)
          return func(value, formatter)
        } catch (error) {
          console.error('自定义格式化函数执行失败:', error)
          return String(value)
        }
      }
      return String(value)
    
    default:
      return String(value)
  }
}

// 日期格式化
export function formatDate(value: any, format: string = 'YYYY-MM-DD'): string {
  if (!value) return ''
  
  let date: Date
  
  // 处理不同的日期输入格式
  if (typeof value === 'number') {
    // 时间戳处理
    date = value.toString().length === 10 ? new Date(value * 1000) : new Date(value)
  } else if (typeof value === 'string') {
    date = new Date(value)
  } else if (value instanceof Date) {
    date = value
  } else {
    return String(value)
  }
  
  if (isNaN(date.getTime())) return String(value)
  
  // 简单的日期格式化
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

// 数字格式化
export function formatNumber(value: any, precision?: number): string {
  const num = Number(value)
  if (isNaN(num)) return String(value)
  
  if (precision !== undefined) {
    return num.toFixed(precision)
  }
  
  return num.toLocaleString()
}

// 货币格式化
export function formatCurrency(
  value: any, 
  precision: number = 2, 
  prefix: string = '¥', 
  suffix: string = ''
): string {
  const num = Number(value)
  if (isNaN(num)) return String(value)
  
  const formatted = num.toFixed(precision)
  return `${prefix}${formatted}${suffix}`
}

// 枚举值转换
export function formatEnum(value: any, enumOptions?: Record<string, string>): string {
  if (!enumOptions) return String(value)
  return enumOptions[String(value)] || String(value)
}

// 验证字段值
export function validateFieldValue(value: any, field: SearchFieldConfig): string[] {
  const errors: string[] = []
  
  if (!field.validation) return errors
  
  for (const rule of field.validation) {
    switch (rule.type) {
      case 'required':
        if (!value || (typeof value === 'string' && value.trim() === '')) {
          errors.push(rule.message)
        }
        break
      
      case 'min':
        if (typeof value === 'number' && value < rule.value) {
          errors.push(rule.message)
        } else if (typeof value === 'string' && value.length < rule.value) {
          errors.push(rule.message)
        }
        break
      
      case 'max':
        if (typeof value === 'number' && value > rule.value) {
          errors.push(rule.message)
        } else if (typeof value === 'string' && value.length > rule.value) {
          errors.push(rule.message)
        }
        break
      
      case 'pattern':
        if (typeof value === 'string' && rule.value) {
          const regex = new RegExp(rule.value)
          if (!regex.test(value)) {
            errors.push(rule.message)
          }
        }
        break
      
      case 'custom':
        // 自定义验证逻辑
        break
    }
  }
  
  return errors
}

// 生成搜索参数
export function buildSearchParams(
  formData: Record<string, any>, 
  searchConfig: SearchFieldConfig[]
): Record<string, any> {
  const params: Record<string, any> = {}
  
  for (const field of searchConfig) {
    if (!field.enabled) continue
    
    const value = formData[field.key]
    if (value !== undefined && value !== null && value !== '') {
      // 处理日期范围
      if (field.type === 'daterange' && Array.isArray(value) && value.length === 2) {
        params[`${field.key}Begin`] = value[0]
        params[`${field.key}End`] = value[1]
      } else {
        params[field.key] = value
      }
    }
  }
  
  return params
}

// 排序配置转换
export function buildSortParams(sortField?: string, sortOrder?: 'asc' | 'desc'): Record<string, any> {
  if (!sortField) return {}
  
  return {
    sortField,
    sortOrder: sortOrder || 'asc'
  }
}

// 导出文件名生成
export function generateExportFilename(template: string, config: LowCodeConfig): string {
  const now = new Date()
  const timestamp = formatDate(now, 'YYYYMMDDHHmmss')
  const date = formatDate(now, 'YYYY-MM-DD')
  
  return template
    .replace('{name}', config.name)
    .replace('{dataSource}', config.dataSource.url.split('/').pop() || 'data')
    .replace('{timestamp}', timestamp)
    .replace('{date}', date)
}

// 深拷贝
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj
  
  if (obj instanceof Date) return new Date(obj.getTime()) as any
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as any
  
  const cloned = {} as T
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key])
    }
  }
  
  return cloned
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let lastTime = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastTime >= wait) {
      lastTime = now
      func(...args)
    }
  }
}

// 数组移动元素
export function moveArrayItem<T>(array: T[], fromIndex: number, toIndex: number): T[] {
  const result = [...array]
  const [removed] = result.splice(fromIndex, 1)
  result.splice(toIndex, 0, removed)
  return result
}

// 生成唯一ID
export function generateUniqueId(prefix: string = 'id'): string {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// 检查是否为空值
export function isEmpty(value: any): boolean {
  if (value === null || value === undefined) return true
  if (typeof value === 'string') return value.trim() === ''
  if (Array.isArray(value)) return value.length === 0
  if (typeof value === 'object') return Object.keys(value).length === 0
  return false
}

// 安全的JSON解析
export function safeJsonParse<T = any>(str: string, defaultValue: T): T {
  try {
    return JSON.parse(str)
  } catch {
    return defaultValue
  }
}

// 下载文件
export function downloadFile(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}
