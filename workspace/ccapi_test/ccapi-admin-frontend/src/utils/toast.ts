import { createApp, h } from 'vue'
import Toast from '@/components/Toast.vue'
import type { ToastProps } from '@/components/Toast.vue'

export interface ToastOptions extends Omit<ToastProps, 'onClose'> {
  onClose?: () => void
}

class ToastManager {
  private toasts: Array<{ id: string; app: any }> = []

  private createToast(options: ToastOptions) {
    const id = `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

    // 创建容器
    const container = document.createElement('div')
    container.id = id
    document.body.appendChild(container)

    // 保存对 this 的引用
    const toastManager = this

    // 创建Vue应用实例
    const app = createApp({
      render() {
        return h(Toast, {
          ...options,
          onClose: () => {
            toastManager.removeToast(id)
            options.onClose?.()
          }
        })
      }
    })

    // 挂载应用
    app.mount(container)

    // 保存引用
    this.toasts.push({ id, app })

    return id
  }

  private removeToast(id: string) {
    const index = this.toasts.findIndex(toast => toast.id === id)
    if (index > -1) {
      const toast = this.toasts[index]
      
      // 卸载Vue应用
      toast.app.unmount()
      
      // 移除DOM元素
      const container = document.getElementById(id)
      if (container) {
        document.body.removeChild(container)
      }
      
      // 从数组中移除
      this.toasts.splice(index, 1)
    }
  }

  // 成功通知
  success(message: string, options?: Partial<ToastOptions>) {
    return this.createToast({
      type: 'success',
      message,
      ...options
    })
  }

  // 错误通知
  error(message: string, options?: Partial<ToastOptions>) {
    return this.createToast({
      type: 'error',
      message,
      duration: 5000, // 错误消息显示更长时间
      ...options
    })
  }

  // 警告通知
  warning(message: string, options?: Partial<ToastOptions>) {
    return this.createToast({
      type: 'warning',
      message,
      ...options
    })
  }

  // 信息通知
  info(message: string, options?: Partial<ToastOptions>) {
    return this.createToast({
      type: 'info',
      message,
      ...options
    })
  }

  // 登录成功特殊通知（带表情符号）
  loginSuccess(username: string = 'admin') {
    return this.createToast({
      type: 'success',
      title: '登录成功！🎉',
      message: `欢迎回来，${username}！正在跳转到主页...`,
      duration: 2000,
      closable: false
    })
  }

  // 清除所有通知
  clear() {
    this.toasts.forEach(toast => {
      this.removeToast(toast.id)
    })
  }
}

// 创建全局实例
export const toast = new ToastManager()

// 默认导出
export default toast

// 类型导出
export type { ToastOptions }
