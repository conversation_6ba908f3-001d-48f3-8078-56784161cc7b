/**
 * 动态参数解析引擎
 * 支持表达式语法和动态值计算
 */

import { useAuthStore } from '@/stores/auth'

// 参数值类型
export type ParamValueType = 'static' | 'expression' | 'function' | 'template' | 'array'

// 数组元素配置
export interface ArrayElementConfig {
  type: Exclude<ParamValueType, 'array'> // 数组元素不能再是数组
  value: string
  description?: string
}

// 动态参数配置
export interface DynamicParamConfig {
  key: string
  type: ParamValueType
  value: string
  description?: string
  enabled: boolean
  // 数组类型专用配置
  arrayElements?: ArrayElementConfig[]
}

// 内置函数定义
export interface BuiltinFunction {
  name: string
  description: string
  category: string
  parameters?: Array<{
    name: string
    type: string
    required: boolean
    description: string
    default?: any
  }>
  example: string
  handler: (...args: any[]) => any
}

/**
 * 动态参数解析器
 */
export class DynamicParamResolver {
  private builtinFunctions: Map<string, BuiltinFunction> = new Map()
  private contextCache: Record<string, any> = {}

  constructor() {
    this.registerBuiltinFunctions()
  }

  /**
   * 注册内置函数
   */
  private registerBuiltinFunctions() {
    // 时间相关函数
    this.registerFunction({
      name: 'now',
      description: '当前时间戳（秒）',
      category: 'time',
      example: 'now()',
      handler: () => Math.floor(Date.now() / 1000) // 转换为秒级时间戳
    })

    this.registerFunction({
      name: 'today',
      description: '今天开始时间戳（00:00:00）',
      category: 'time',
      example: 'today()',
      handler: () => {
        const today = new Date()
        today.setHours(0, 0, 0, 0)
        return Math.floor(today.getTime() / 1000) // 转换为秒级时间戳
      }
    })

    this.registerFunction({
      name: 'todayEnd',
      description: '今天结束时间戳（23:59:59）',
      category: 'time',
      example: 'todayEnd()',
      handler: () => {
        const today = new Date()
        today.setHours(23, 59, 59, 999)
        return Math.floor(today.getTime() / 1000) // 转换为秒级时间戳
      }
    })

    this.registerFunction({
      name: 'daysAgo',
      description: 'N天前的时间戳',
      category: 'time',
      parameters: [{
        name: 'days',
        type: 'number',
        required: true,
        description: '天数'
      }],
      example: 'daysAgo(7)',
      handler: (days: number) => {
        const date = new Date()
        date.setDate(date.getDate() - days)
        return Math.floor(date.getTime() / 1000) // 转换为秒级时间戳
      }
    })

    this.registerFunction({
      name: 'monthsAgo',
      description: 'N个月前的时间戳',
      category: 'time',
      parameters: [{
        name: 'months',
        type: 'number',
        required: true,
        description: '月数'
      }],
      example: 'monthsAgo(3)',
      handler: (months: number) => {
        const date = new Date()
        date.setMonth(date.getMonth() - months)
        return Math.floor(date.getTime() / 1000) // 转换为秒级时间戳
      }
    })

    this.registerFunction({
      name: 'lastMonthStart',
      description: '上个月开始时间戳（1号00:00:00）',
      category: 'time',
      example: 'lastMonthStart()',
      handler: () => {
        const date = new Date()
        date.setMonth(date.getMonth() - 1)
        date.setDate(1)
        date.setHours(0, 0, 0, 0)
        return Math.floor(date.getTime() / 1000)
      }
    })

    this.registerFunction({
      name: 'lastMonthEnd',
      description: '上个月结束时间戳（最后一天23:59:59）',
      category: 'time',
      example: 'lastMonthEnd()',
      handler: () => {
        const date = new Date()
        date.setDate(0) // 设置为上个月最后一天
        date.setHours(23, 59, 59, 999)
        return Math.floor(date.getTime() / 1000)
      }
    })

    this.registerFunction({
      name: 'thisMonthStart',
      description: '本月开始时间戳（1号00:00:00）',
      category: 'time',
      example: 'thisMonthStart()',
      handler: () => {
        const date = new Date()
        date.setDate(1)
        date.setHours(0, 0, 0, 0)
        return Math.floor(date.getTime() / 1000)
      }
    })

    // 用户相关函数
    this.registerFunction({
      name: 'userId',
      description: '当前用户ID',
      category: 'user',
      example: 'userId()',
      handler: () => {
        const authStore = useAuthStore()
        return authStore.user?.id || ''
      }
    })

    this.registerFunction({
      name: 'username',
      description: '当前用户名',
      category: 'user',
      example: 'username()',
      handler: () => {
        const authStore = useAuthStore()
        return authStore.user?.username || ''
      }
    })

    // 工具函数
    this.registerFunction({
      name: 'uuid',
      description: '生成UUID',
      category: 'utility',
      example: 'uuid()',
      handler: () => {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
          const r = Math.random() * 16 | 0
          const v = c == 'x' ? r : (r & 0x3 | 0x8)
          return v.toString(16)
        })
      }
    })
  }

  /**
   * 注册函数
   */
  registerFunction(func: BuiltinFunction) {
    this.builtinFunctions.set(func.name, func)
  }

  /**
   * 获取所有内置函数
   */
  getBuiltinFunctions(): BuiltinFunction[] {
    return Array.from(this.builtinFunctions.values())
  }

  /**
   * 解析单个参数值
   */
  async resolveValue(param: DynamicParamConfig): Promise<any> {
    switch (param.type) {
      case 'static':
        return this.parseStaticValue(param.value)
      
      case 'function':
        return this.callFunction(param.value)
      
      case 'expression':
        return this.evaluateExpression(param.value)
      
      case 'template':
        return this.processTemplate(param.value)
      
      case 'array':
        return this.resolveArrayValue(param)
      
      default:
        throw new Error(`不支持的参数类型: ${param.type}`)
    }
  }

  /**
   * 解析静态值
   */
  private parseStaticValue(value: string): any {
    // 尝试解析为数字
    if (/^\d+$/.test(value)) {
      return parseInt(value, 10)
    }
    
    if (/^\d+\.\d+$/.test(value)) {
      return parseFloat(value)
    }
    
    // 尝试解析为布尔值
    if (value === 'true') return true
    if (value === 'false') return false
    
    // 返回字符串
    return value
  }

  /**
   * 调用函数
   */
  private callFunction(expression: string): any {
    // 解析函数调用：functionName(arg1, arg2, ...)
    const match = expression.match(/^(\w+)\((.*)\)$/)
    if (!match) {
      throw new Error(`无效的函数调用格式: ${expression}`)
    }

    const [, functionName, argsStr] = match
    const func = this.builtinFunctions.get(functionName)
    
    if (!func) {
      throw new Error(`未知的函数: ${functionName}`)
    }

    // 解析参数
    const args = argsStr.trim() ? this.parseArguments(argsStr) : []
    
    try {
      return func.handler(...args)
    } catch (error) {
      throw new Error(`函数 ${functionName} 执行失败: ${error}`)
    }
  }

  /**
   * 解析函数参数
   */
  private parseArguments(argsStr: string): any[] {
    if (!argsStr.trim()) return []
    
    const args: any[] = []
    const parts = argsStr.split(',')
    
    for (const part of parts) {
      const trimmed = part.trim()
      
      // 数字
      if (/^\d+$/.test(trimmed)) {
        args.push(parseInt(trimmed, 10))
      } else if (/^\d+\.\d+$/.test(trimmed)) {
        args.push(parseFloat(trimmed))
      }
      // 字符串（带引号）
      else if (/^["'].*["']$/.test(trimmed)) {
        args.push(trimmed.slice(1, -1))
      }
      // 布尔值
      else if (trimmed === 'true') {
        args.push(true)
      } else if (trimmed === 'false') {
        args.push(false)
      }
      // 默认作为字符串
      else {
        args.push(trimmed)
      }
    }
    
    return args
  }

  /**
   * 计算表达式
   */
  private evaluateExpression(expression: string): any {
    // 获取上下文
    const context = this.getContext()
    
    try {
      // 创建安全的执行环境
      const func = new Function(...Object.keys(context), `return ${expression}`)
      return func(...Object.values(context))
    } catch (error) {
      throw new Error(`表达式计算失败: ${error}`)
    }
  }

  /**
   * 处理模板字符串
   */
  private processTemplate(template: string): string {
    const context = this.getContext()
    
    return template.replace(/\{\{([^}]+)\}\}/g, (match, expression) => {
      try {
        const func = new Function(...Object.keys(context), `return ${expression.trim()}`)
        const result = func(...Object.values(context))
        return String(result)
      } catch (error) {
        console.warn(`模板表达式 ${expression} 计算失败:`, error)
        return match // 返回原始字符串
      }
    })
  }

  /**
   * 解析数组值
   */
  private async resolveArrayValue(param: DynamicParamConfig): Promise<any[]> {
    if (!param.arrayElements || param.arrayElements.length === 0) {
      return []
    }

    const results: any[] = []
    
    for (const element of param.arrayElements) {
      const elementParam: DynamicParamConfig = {
        key: '',
        type: element.type,
        value: element.value,
        description: element.description,
        enabled: true
      }
      
      const value = await this.resolveValue(elementParam)
      results.push(value)
    }
    
    return results
  }

  /**
   * 获取执行上下文
   */
  private getContext(): Record<string, any> {
    const now = new Date()
    const authStore = useAuthStore()
    const user = authStore.user

    return {
      user: {
        id: user?.id ? String(user.id) : 'anonymous',
        username: user?.username || 'guest',
        roles: user?.roles || [],
        email: user?.email || '',
        department: user?.department || '',
        tenantId: user?.tenantId || ''
      },
      system: {
        timestamp: now.getTime(),
        date: now.toISOString().split('T')[0],
        time: now.toTimeString().split(' ')[0],
        version: import.meta.env.VITE_APP_VERSION || '1.0.0',
        platform: typeof navigator !== 'undefined' ? navigator.platform : 'unknown'
      },
      env: {
        isDev: import.meta.env.DEV,
        isProd: import.meta.env.PROD,
        isTest: import.meta.env.MODE === 'test',
        baseURL: import.meta.env.VITE_API_BASE_URL || '/api'
      },
      // 时间函数快捷方式（秒级时间戳）
      now: () => Math.floor(Date.now() / 1000),
      today: () => {
        const today = new Date()
        today.setHours(0, 0, 0, 0)
        return Math.floor(today.getTime() / 1000)
      },
      todayEnd: () => {
        const today = new Date()
        today.setHours(23, 59, 59, 999)
        return Math.floor(today.getTime() / 1000)
      }
    }
  }

  /**
   * 解析动态参数
   */
  async resolveParams(params: DynamicParamConfig[]): Promise<Record<string, any>> {
    const result: Record<string, any> = {}

    for (const param of params) {
      if (!param.enabled) continue

      try {
        const value = await this.resolveValue(param)
        if (value !== undefined && value !== null) {
          result[param.key] = value
        }
      } catch (error) {
        console.error(`解析参数 ${param.key} 失败:`, error)
        // 可以选择跳过错误的参数或抛出异常
      }
    }

    return result
  }
}

// 创建全局实例
export const dynamicParamResolver = new DynamicParamResolver()
