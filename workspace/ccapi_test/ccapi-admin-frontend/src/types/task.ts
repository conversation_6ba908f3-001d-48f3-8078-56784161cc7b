// 任务执行相关类型定义

// 任务执行查询参数
export interface TaskExecutionListQuery {
  taskName?: string
  imsi?: string
  vehiclePlate?: string
  creator?: string
  sortField?: string
  sortOrder?: string
  // 数值字段
  taskType?: number // -1表示全部
  status?: number // -1表示全部
  taskId?: number // -1表示全部
  childId?: number // -1表示全部
  // 时间字段 - 兼容原有格式
  departureTimeBegin?: number // 时间戳
  departureTimeEnd?: number // 时间戳
  endTimeBegin?: number // 时间戳
  endTimeEnd?: number // 时间戳
  createdTimeBegin?: number // 时间戳
  createdTimeEnd?: number // 时间戳

  // 新增：时间范围列表格式 [开始时间, 结束时间]
  createdTime?: [number, number] // 创建时间范围
  departureTime?: [number, number] // 出发时间范围
  endTime?: [number, number] // 结束时间范围
  page?: number
  pageSize?: number
}

// 任务执行结果数据
export interface TaskExecutionResult {
  id: number
  taskId: number
  childId: number
  executionNo: string
  taskName: string
  taskType: number
  executionStatus: ExecutionStatus
  status: number
  imsi: string
  vehiclePlate: string
  vehicleType?: string
  plannedDistance?: number
  actualMileage?: number
  executionDuration?: number
  averageSpeed?: number
  actualSpeedKmh?: number
  departureTime?: number
  endTime?: number
  createdTime: number
  updatedTime: number
  creator: string
}

// 任务执行状态枚举
export enum ExecutionStatus {
  NOT_EXECUTE = 0, // 不执行
  WILL_EXECUTE = 1, // 将要执行
  EXECUTING = 2, // 正在执行
  COMPLETED = 3, // 已完成
  PAUSED = 4, // 已暂停
  CANCELLED = 5, // 已取消
  TERMINATED = 6, // 已终止
  CANNOT_FINISH = 7 // 无法完成
}

// 任务执行状态标签映射
export const ExecutionStatusLabels: Record<ExecutionStatus, string> = {
  [ExecutionStatus.NOT_EXECUTE]: '不执行',
  [ExecutionStatus.WILL_EXECUTE]: '将要执行',
  [ExecutionStatus.EXECUTING]: '正在执行',
  [ExecutionStatus.COMPLETED]: '已完成',
  [ExecutionStatus.PAUSED]: '已暂停',
  [ExecutionStatus.CANCELLED]: '已取消',
  [ExecutionStatus.TERMINATED]: '已终止',
  [ExecutionStatus.CANNOT_FINISH]: '无法完成'
}

// 任务类型枚举（根据实际业务定义）
export enum TaskType {
  ALL = -1,
  ROUTE_TASK = 1,
  MAINTENANCE_TASK = 2,
  INSPECTION_TASK = 3
}

// 任务执行分页结果
export interface TaskExecutionPageResult {
  data: TaskExecutionResult[]
  total: number
  page: number
  pageSize: number
}

// 任务执行导出请求
export interface ExportTaskExecutionRequest extends TaskExecutionListQuery {
  fields: ExportField[]
  fileType: 'excel' | 'pdf' | 'csv'
}

// 导出字段定义（复用order.ts中的定义）
export interface ExportField {
  field: string
  label: string
  width?: number
}
