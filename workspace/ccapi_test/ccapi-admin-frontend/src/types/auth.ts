// 登录认证相关类型定义

// 验证码相关
export interface CaptchaResponse {
  captchaId: string
  image: string // base64编码的图片
}

export interface VerifyCaptchaRequest {
  captchaId: string
  captchaCode: string  // 后端期望的字段名是 captchaCode
}

export interface VerifyCaptchaResponse {
  success: boolean
  message?: string
}

// 登录相关
export interface LoginRequest {
  username: string
  password: string
  captchaId: string
  captcha: string
}

export interface LoginResponse {
  success: boolean
  message?: string
  data?: {
    token: string
    refreshToken: string
    user: UserInfo
  }
}

export interface UserInfo {
  id: number
  username: string
  nickname?: string
  email?: string
  phone?: string
  role?: string
  permissions?: string[]
}

// Token刷新相关
export interface RefreshTokenRequest {
  refreshToken: string
}

export interface RefreshTokenResponse {
  success: boolean
  message?: string
  data?: {
    token: string
    refreshToken: string
  }
}

// 登出相关
export interface LogoutResponse {
  success: boolean
  message?: string
}

// 通用API响应格式
export interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  code?: number
}
