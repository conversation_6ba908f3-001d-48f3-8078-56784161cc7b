// 通用类型定义

// 分页参数
export interface PaginationParams {
  page: number
  pageSize: number
}

// 分页结果
export interface PaginationResult<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
}

// 排序参数
export interface SortParams {
  sortField?: string
  sortOrder?: 'asc' | 'desc'
}

// 时间范围参数
export interface TimeRangeParams {
  startTime?: number
  endTime?: number
}

// 下拉选项
export interface SelectOption {
  label: string
  value: string | number
}

// 表格列定义
export interface TableColumn {
  key: string
  title: string
  width?: number
  sortable?: boolean
  render?: (value: any, record: any) => string
}

// 搜索表单项定义
export interface FormField {
  key: string
  label: string
  type: 'input' | 'select' | 'daterange' | 'number'
  options?: SelectOption[]
  placeholder?: string
  rules?: any[]
}

// HTTP请求配置
export interface RequestConfig {
  baseURL?: string
  timeout?: number
  headers?: Record<string, string>
}

// 文件下载响应
export interface DownloadResponse {
  data: Blob
  filename?: string
  contentType?: string
}
