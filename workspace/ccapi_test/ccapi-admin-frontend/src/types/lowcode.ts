/**
 * 低代码配置相关类型定义
 */

// 基础字段类型
export type FieldType = 'text' | 'number' | 'date' | 'datetime' | 'enum' | 'boolean' | 'custom'

// 搜索字段类型
export type SearchFieldType = 'input' | 'select' | 'date' | 'daterange' | 'number' | 'textarea'



// 验证规则
export interface ValidationRule {
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom'
  value?: any
  message: string
}

// 格式化配置
export interface FormatterConfig {
  type: 'date' | 'number' | 'currency' | 'custom'
  format?: string
  precision?: number
  prefix?: string
  suffix?: string
  customFunction?: string
}

// 数组元素配置
export interface ArrayElementConfig {
  type: 'static' | 'expression' | 'function' | 'template'
  value: string
  description?: string
}

// 动态参数配置
export interface DynamicParamConfig {
  key: string
  type: 'static' | 'expression' | 'function' | 'template' | 'array'
  value: string
  description?: string
  enabled: boolean
  // 数组类型专用配置
  arrayElements?: ArrayElementConfig[]
}





// 数据源配置
export interface DataSourceConfig {
  url: string
  method: 'GET' | 'POST'
  params?: Record<string, any> // 默认请求参数（简单模式）
  headers?: Record<string, string> // 请求头
  dynamicParams?: DynamicParamConfig[] // 动态参数配置（高级模式）
  responseMapping?: {
    dataPath: string // 数据在响应中的路径，如 'data.list'
    totalPath?: string // 总数在响应中的路径，如 'data.total'
  }
}

// 按钮类型
export type ButtonType = 'search' | 'reset' | 'export'

// 按钮配置
export interface ButtonConfig {
  type: ButtonType
  enabled: boolean // 是否启用
  width?: string // 按钮宽度
}

// 搜索布局配置
export interface SearchLayoutConfig {
  gap?: string // 组件间距：'8px' | '12px' | '16px' | '20px' | '24px' 等
  labelPosition?: 'top' | 'left' | 'none' // 标签位置
  // 按钮配置
  buttonConfig?: {
    position?: 'inline' | 'newline' // 按钮位置：与字段同行或新行
    alignment?: 'start' | 'center' | 'end' // 按钮对齐方式
    buttons: ButtonConfig[] // 按钮列表
  }
}

// 搜索字段配置
export interface SearchFieldConfig {
  id: string
  key: string // API参数名
  label: string // 显示标签
  type: SearchFieldType
  placeholder?: string
  options?: Array<{label: string, value: any}>
  defaultValue?: any
  validation?: ValidationRule[]
  enabled: boolean
  order: number
  width?: string // CSS宽度，如 '200px', '20%', 'auto'
  // 特殊配置
  dateFormat?: string // 日期格式
  multiple?: boolean // 多选
  clearable?: boolean // 可清空
}

// 表格列配置
export interface ColumnConfig {
  id: string
  key: string // 数据字段名，支持嵌套如 'user.name'
  label: string // 列标题
  type: FieldType
  width?: number
  minWidth?: number
  sortable: boolean
  filterable: boolean
  resizable: boolean
  formatter?: FormatterConfig
  enumOptions?: Record<string, string>
  enabled: boolean
  order: number
  align?: 'left' | 'center' | 'right'
  fixed?: 'left' | 'right' // 固定列
  // 自定义渲染
  customRender?: {
    type: 'slot' | 'component' | 'function'
    content: string
  }
}

// 表格配置
export interface TableConfig {
  columns: ColumnConfig[]
  pagination: {
    pageSize: number
    showSizeChanger: boolean
    pageSizeOptions: number[]
    showQuickJumper: boolean
    showTotal: boolean
  }
  sorting: {
    defaultSort?: string
    defaultOrder?: 'asc' | 'desc'
    multiple: boolean // 多列排序
  }
  features: {
    selection: boolean
    export: boolean
    refresh: boolean
    columnSettings: boolean // 已废弃，保留以兼容现有配置
    fullscreen: boolean // 全屏
  }
  rowKey: string // 行唯一标识字段
  size: 'small' | 'medium' | 'large'
  bordered: boolean
  striped: boolean
}

// 导出配置
export interface ExportConfig {
  enabled: boolean
  // 导出接口配置
  api: {
    url: string // 导出接口地址
    method: 'GET' | 'POST' // 请求方式
    headers?: Record<string, string> // 请求头
  }
  filename: string // 支持模板变量
  columns: string[] // 要导出的列ID
}

// 完整的低代码配置
export interface LowCodeConfig {
  id: string
  name: string
  description?: string
  dataSource: DataSourceConfig
  searchConfig: SearchFieldConfig[]
  searchLayoutConfig?: SearchLayoutConfig // 搜索表单布局配置
  tableConfig: TableConfig
  exportConfig: ExportConfig
  // 元数据
  metadata: {
    createdBy: string
    createdAt: string
    updatedBy: string
    updatedAt: string
  }
}



// 运行时状态
export interface RuntimeState {
  loading: boolean
  data: any[]
  total: number
  currentPage: number
  pageSize: number
  searchParams: Record<string, any>
  sortField?: string
  sortOrder?: 'asc' | 'desc'
  selectedRows: any[]
  error?: string
}

// API响应格式
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  total?: number
  page?: number
  pageSize?: number
}

// 组件注册接口
export interface ComponentRegistry {
  [key: string]: {
    component: any
    props?: Record<string, any>
    description?: string
  }
}

// 事件类型
export type LowCodeEvent = 
  | 'search'
  | 'reset'
  | 'export'
  | 'refresh'
  | 'sort'
  | 'filter'
  | 'select'
  | 'page-change'
  | 'page-size-change'

// 事件处理器
export interface EventHandler {
  type: LowCodeEvent
  handler: (payload: any) => void | Promise<void>
}

// 插件接口
export interface LowCodePlugin {
  name: string
  version: string
  install: (app: any, options?: any) => void
  components?: ComponentRegistry
  validators?: Record<string, (value: any, rule: ValidationRule) => boolean>
  formatters?: Record<string, (value: any, config: FormatterConfig) => string>
}
