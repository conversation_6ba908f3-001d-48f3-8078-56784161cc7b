/**
 * 低代码配置JSON Schema定义
 */

export const searchFieldConfigSchema = {
  type: 'object',
  required: ['id', 'key', 'label', 'type', 'enabled', 'order'],
  properties: {
    id: { type: 'string' },
    key: { type: 'string' },
    label: { type: 'string' },
    type: { 
      type: 'string',
      enum: ['input', 'select', 'date', 'daterange', 'number', 'textarea']
    },
    placeholder: { type: 'string' },
    options: {
      type: 'array',
      items: {
        type: 'object',
        required: ['label', 'value'],
        properties: {
          label: { type: 'string' },
          value: { type: ['string', 'number', 'boolean'] }
        }
      }
    },
    defaultValue: { type: ['string', 'number', 'boolean', 'null'] },
    validation: {
      type: 'array',
      items: {
        type: 'object',
        required: ['type', 'message'],
        properties: {
          type: { 
            type: 'string',
            enum: ['required', 'min', 'max', 'pattern', 'custom']
          },
          value: { type: ['string', 'number'] },
          message: { type: 'string' }
        }
      }
    },
    enabled: { type: 'boolean' },
    order: { type: 'number' },
    width: { type: 'string' },
    dateFormat: { type: 'string' },
    multiple: { type: 'boolean' },
    clearable: { type: 'boolean' }
  }
}

export const columnConfigSchema = {
  type: 'object',
  required: ['id', 'key', 'label', 'type', 'enabled', 'order'],
  properties: {
    id: { type: 'string' },
    key: { type: 'string' },
    label: { type: 'string' },
    type: {
      type: 'string',
      enum: ['text', 'number', 'date', 'datetime', 'enum', 'boolean', 'custom']
    },
    width: { type: 'number', minimum: 50 },
    minWidth: { type: 'number', minimum: 50 },
    sortable: { type: 'boolean' },
    filterable: { type: 'boolean' },
    resizable: { type: 'boolean' },
    formatter: {
      type: 'object',
      required: ['type'],
      properties: {
        type: {
          type: 'string',
          enum: ['date', 'number', 'currency', 'custom']
        },
        format: { type: 'string' },
        precision: { type: 'number' },
        prefix: { type: 'string' },
        suffix: { type: 'string' },
        customFunction: { type: 'string' }
      }
    },
    enumOptions: {
      type: 'object',
      patternProperties: {
        '.*': { type: 'string' }
      }
    },
    enabled: { type: 'boolean' },
    order: { type: 'number' },
    align: {
      type: 'string',
      enum: ['left', 'center', 'right']
    },
    fixed: {
      type: 'string',
      enum: ['left', 'right']
    },
    customRender: {
      type: 'object',
      required: ['type', 'content'],
      properties: {
        type: {
          type: 'string',
          enum: ['slot', 'component', 'function']
        },
        content: { type: 'string' }
      }
    }
  }
}

export const dataSourceConfigSchema = {
  type: 'object',
  required: ['url', 'method'],
  properties: {
    url: { type: 'string', minLength: 1 },
    method: {
      type: 'string',
      enum: ['GET', 'POST']
    },
    params: { type: 'object' },
    headers: { type: 'object' },
    responseMapping: {
      type: 'object',
      required: ['dataPath'],
      properties: {
        dataPath: { type: 'string' },
        totalPath: { type: 'string' }
      }
    }
  }
}

export const tableConfigSchema = {
  type: 'object',
  required: ['columns', 'pagination', 'sorting', 'features', 'rowKey'],
  properties: {
    columns: {
      type: 'array',
      items: columnConfigSchema
    },
    pagination: {
      type: 'object',
      required: ['pageSize', 'showSizeChanger', 'pageSizeOptions'],
      properties: {
        pageSize: { type: 'number', minimum: 1 },
        showSizeChanger: { type: 'boolean' },
        pageSizeOptions: {
          type: 'array',
          items: { type: 'number', minimum: 1 }
        },
        showQuickJumper: { type: 'boolean' },
        showTotal: { type: 'boolean' }
      }
    },
    sorting: {
      type: 'object',
      properties: {
        defaultSort: { type: 'string' },
        defaultOrder: {
          type: 'string',
          enum: ['asc', 'desc']
        },
        multiple: { type: 'boolean' }
      }
    },
    features: {
      type: 'object',
      required: ['selection', 'export', 'refresh'],
      properties: {
        selection: { type: 'boolean' },
        export: { type: 'boolean' },
        refresh: { type: 'boolean' },
        columnSettings: { type: 'boolean' },
        fullscreen: { type: 'boolean' }
      }
    },
    rowKey: { type: 'string' },
    size: {
      type: 'string',
      enum: ['small', 'medium', 'large']
    },
    bordered: { type: 'boolean' },
    striped: { type: 'boolean' }
  }
}

export const exportConfigSchema = {
  type: 'object',
  required: ['enabled', 'api', 'filename', 'columns'],
  properties: {
    enabled: { type: 'boolean' },
    api: {
      type: 'object',
      required: ['url', 'method'],
      properties: {
        url: { type: 'string', minLength: 1 },
        method: {
          type: 'string',
          enum: ['GET', 'POST']
        },
        headers: {
          type: 'object',
          additionalProperties: { type: 'string' }
        }
      }
    },
    filename: { type: 'string' },
    columns: {
      type: 'array',
      items: { type: 'string' }
    }
  }
}

export const lowCodeConfigSchema = {
  type: 'object',
  required: ['id', 'name', 'dataSource', 'searchConfig', 'tableConfig', 'exportConfig', 'metadata'],
  properties: {
    id: { type: 'string' },
    name: { type: 'string', minLength: 1 },
    description: { type: 'string' },
    dataSource: dataSourceConfigSchema,
    searchConfig: {
      type: 'array',
      items: searchFieldConfigSchema
    },
    tableConfig: tableConfigSchema,
    exportConfig: exportConfigSchema,
    metadata: {
      type: 'object',
      required: ['createdBy', 'createdAt', 'updatedBy', 'updatedAt'],
      properties: {
        createdBy: { type: 'string' },
        createdAt: { type: 'string' },
        updatedBy: { type: 'string' },
        updatedAt: { type: 'string' }
      }
    }
  }
}

// 配置模板Schema
export const configTemplateSchema = {
  type: 'object',
  required: ['id', 'name', 'description', 'category', 'config'],
  properties: {
    id: { type: 'string' },
    name: { type: 'string' },
    description: { type: 'string' },
    category: { type: 'string' },
    config: {
      type: 'object'
      // 这里是部分配置，所以不强制要求所有字段
    },
    preview: { type: 'string' }
  }
}
