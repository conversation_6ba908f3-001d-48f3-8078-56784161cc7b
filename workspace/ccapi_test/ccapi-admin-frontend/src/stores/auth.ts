import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { UserInfo, LoginRequest, RefreshTokenRequest } from '@/types'
import * as authApi from '@/api/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>('')
  const refreshToken = ref<string>('')
  const user = ref<UserInfo | null>(null)
  const isLoggedIn = computed(() => !!token.value)

  // 初始化：从localStorage恢复状态
  const initAuth = () => {
    const savedToken = localStorage.getItem('token')
    const savedRefreshToken = localStorage.getItem('refreshToken')
    const savedUser = localStorage.getItem('user')

    if (savedToken) {
      token.value = savedToken
    }
    if (savedRefreshToken) {
      refreshToken.value = savedRefreshToken
    }
    if (savedUser) {
      try {
        user.value = JSON.parse(savedUser)
      } catch (e) {
        console.error('Failed to parse saved user info:', e)
      }
    }
  }

  // 保存认证信息到localStorage
  const saveAuth = (tokenValue: string, refreshTokenValue: string, userInfo: UserInfo) => {
    token.value = tokenValue
    refreshToken.value = refreshTokenValue
    user.value = userInfo

    localStorage.setItem('token', tokenValue)
    localStorage.setItem('refreshToken', refreshTokenValue)
    localStorage.setItem('user', JSON.stringify(userInfo))
  }

  // 清除认证信息
  const clearAuth = () => {
    token.value = ''
    refreshToken.value = ''
    user.value = null

    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('user')
  }

  // 登录
  const login = async (loginData: LoginRequest) => {
    try {
      const response = await authApi.login(loginData)
      if (response.token && response.refreshToken && response.user) {
        saveAuth(response.token, response.refreshToken, response.user)
        return { success: true, data: response }
      } else {
        throw new Error('登录响应数据不完整')
      }
    } catch (error: any) {
      clearAuth()
      throw error
    }
  }

  // 刷新访问令牌
  const refreshAccessToken = async () => {
    if (!refreshToken.value) {
      throw new Error('No refresh token available')
    }

    try {
      const response = await authApi.refreshToken({ refreshToken: refreshToken.value })
      if (response.token && response.refreshToken) {
        token.value = response.token
        refreshToken.value = response.refreshToken

        localStorage.setItem('token', response.token)
        localStorage.setItem('refreshToken', response.refreshToken)

        return { success: true, data: response }
      } else {
        throw new Error('Token刷新失败')
      }
    } catch (error) {
      clearAuth()
      throw error
    }
  }

  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('Logout API call failed:', error)
    } finally {
      clearAuth()
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const userInfo = await authApi.getUserInfo()
      user.value = userInfo
      localStorage.setItem('user', JSON.stringify(userInfo))
      return userInfo
    } catch (error) {
      console.error('Failed to fetch user info:', error)
      throw error
    }
  }

  return {
    // 状态
    token,
    refreshToken,
    user,
    isLoggedIn,

    // 方法
    initAuth,
    login,
    logout,
    refreshAccessToken,
    fetchUserInfo,
    clearAuth
  }
})
