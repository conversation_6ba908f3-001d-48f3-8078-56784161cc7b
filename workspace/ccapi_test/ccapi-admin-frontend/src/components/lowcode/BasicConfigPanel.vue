<template>
  <div class="space-y-6">
    <!-- 基本信息 -->
    <div class="card bg-base-200 p-4">
      <h4 class="font-semibold mb-4 flex items-center gap-2">
        <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        基本信息
      </h4>
      
      <div class="space-y-4">
        <div class="form-control">
          <label class="label">
            <span class="label-text font-medium">配置名称 *</span>
          </label>
          <input
            v-model="localConfig.name"
            type="text"
            class="input input-bordered"
            placeholder="请输入配置名称"
            required
          />
        </div>

        <div class="form-control">
          <label class="label">
            <span class="label-text font-medium">描述</span>
          </label>
          <textarea
            v-model="localConfig.description"
            class="textarea textarea-bordered"
            placeholder="请输入配置描述"
            rows="3"
          ></textarea>
        </div>
      </div>
    </div>

    <!-- 数据源配置 -->
    <div class="card bg-base-200 p-4">
      <h4 class="font-semibold mb-4 flex items-center gap-2">
        <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
        </svg>
        数据源配置
      </h4>
      
      <div class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">API地址 *</span>
            </label>
            <input
              v-model="localConfig.dataSource.url"
              type="text"
              class="input input-bordered"
              placeholder="/api/data/list"
              required
            />
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">请求方法</span>
            </label>
            <select
              v-model="localConfig.dataSource.method"
              class="select select-bordered"
            >
              <option value="GET">GET</option>
              <option value="POST">POST</option>
            </select>
          </div>
        </div>

        <!-- 响应数据映射 -->
        <div class="collapse collapse-arrow bg-base-100">
          <input type="checkbox" />
          <div class="collapse-title text-sm font-medium">
            响应数据映射配置
          </div>
          <div class="collapse-content">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text">数据路径</span>
                  <span class="label-text-alt">如: data.list</span>
                </label>
                <input
                  v-model="dataSourceMapping.dataPath"
                  type="text"
                  class="input input-bordered input-sm"
                  placeholder="data.list"
                />
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text">总数路径</span>
                  <span class="label-text-alt">如: data.total</span>
                </label>
                <input
                  v-model="dataSourceMapping.totalPath"
                  type="text"
                  class="input input-bordered input-sm"
                  placeholder="data.total"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 默认请求参数 -->
        <div class="collapse collapse-arrow bg-base-100">
          <input type="checkbox" />
          <div class="collapse-title text-sm font-medium">
            默认请求参数
            <div class="badge badge-info badge-sm ml-2">支持动态参数</div>
          </div>
          <div class="collapse-content">
            <DynamicParamsConfig
              v-model="dynamicParams"
              @update:modelValue="updateDynamicParams"
            />
          </div>
        </div>

        <!-- 请求头 -->
        <div class="collapse collapse-arrow bg-base-100">
          <input type="checkbox" />
          <div class="collapse-title text-sm font-medium">
            请求头配置
          </div>
          <div class="collapse-content">
            <div class="space-y-2">
              <div
                v-for="(header, index) in defaultHeaders"
                :key="index"
                class="flex gap-2 items-center"
              >
                <input
                  v-model="header.key"
                  type="text"
                  class="input input-bordered input-sm flex-1"
                  placeholder="Header名称"
                />
                <input
                  v-model="header.value"
                  type="text"
                  class="input input-bordered input-sm flex-1"
                  placeholder="Header值"
                />
                <button
                  class="btn btn-ghost btn-sm text-error"
                  @click="removeHeader(index)"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                  </svg>
                </button>
              </div>

              <button
                class="btn btn-outline btn-sm"
                @click="addHeader"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                添加请求头
              </button>
            </div>
          </div>
        </div>








      </div>
    </div>




  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import type { LowCodeConfig, DynamicParamConfig } from '@/types/lowcode'
import DynamicParamsConfig from './DynamicParamsConfig.vue'

// Props
const props = defineProps<{
  modelValue: LowCodeConfig
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: LowCodeConfig]
}>()

// 本地状态
const localConfig = ref({ ...props.modelValue })
const defaultHeaders = ref<Array<{ key: string; value: string }>>([])
const dynamicParams = ref<DynamicParamConfig[]>([])

// 初始化默认值
const initializeDefaults = () => {
  // 确保 responseMapping 存在
  if (!localConfig.value.dataSource.responseMapping) {
    localConfig.value.dataSource.responseMapping = {
      dataPath: 'data.list',
      totalPath: 'data.total'
    }
  }

  // 确保 headers 存在
  if (!localConfig.value.dataSource.headers) {
    localConfig.value.dataSource.headers = {}
  }

  // 确保 dynamicParams 存在
  if (!localConfig.value.dataSource.dynamicParams) {
    localConfig.value.dataSource.dynamicParams = []
  }
}

// 计算属性
const dataSourceMapping = computed({
  get: () => localConfig.value.dataSource.responseMapping || { dataPath: 'data.list', totalPath: 'data.total' },
  set: (value) => {
    if (!localConfig.value.dataSource.responseMapping) {
      localConfig.value.dataSource.responseMapping = { dataPath: 'data.list' }
    }
    Object.assign(localConfig.value.dataSource.responseMapping, value)
  }
})

// 初始化
initializeDefaults()



// 监听变化并同步到父组件
watch(localConfig, (newValue) => {
  emit('update:modelValue', newValue)
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  // 避免递归更新：只有当值真正不同时才更新
  if (JSON.stringify(newValue) !== JSON.stringify(localConfig.value)) {
    localConfig.value = { ...newValue }
    initializeDefaults()

    // 初始化动态参数
    if (newValue.dataSource?.dynamicParams) {
      dynamicParams.value = [...newValue.dataSource.dynamicParams]
    } else {
      dynamicParams.value = []
    }

    // 初始化请求头
    if (newValue.dataSource?.headers) {
      defaultHeaders.value = Object.entries(newValue.dataSource.headers).map(([key, value]) => ({
        key,
        value: String(value)
      }))
    } else {
      defaultHeaders.value = []
    }
  }
}, { immediate: true })

// 方法
const updateDynamicParams = (params: DynamicParamConfig[]) => {
  dynamicParams.value = params
  localConfig.value.dataSource.dynamicParams = params

  // 清除旧的静态参数配置，因为现在统一使用动态参数
  if (localConfig.value.dataSource.params) {
    delete localConfig.value.dataSource.params
  }
}

const addHeader = () => {
  defaultHeaders.value.push({ key: '', value: '' })
}

const removeHeader = (index: number) => {
  defaultHeaders.value.splice(index, 1)
  updateHeaders()
}

const updateHeaders = () => {
  const headers: Record<string, string> = {}
  defaultHeaders.value.forEach(header => {
    if (header.key && header.value) {
      headers[header.key] = header.value
    }
  })
  localConfig.value.dataSource.headers = headers
}

// 监听请求头变化
watch(defaultHeaders, updateHeaders, { deep: true })


</script>
