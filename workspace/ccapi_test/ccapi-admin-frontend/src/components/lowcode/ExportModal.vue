<template>
  <div v-if="show" class="modal modal-open">
    <div class="modal-box w-11/12 max-w-4xl h-[90vh] bg-base-100 flex flex-col overflow-hidden">
      <!-- 标题栏 -->
      <div class="flex items-center justify-between mb-6">
        <h3 class="font-bold text-lg">自定义导出</h3>
        <button class="btn btn-sm btn-circle btn-ghost" @click="close">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <!-- 选择字段 -->
      <div class="mb-6">
        <h4 class="text-base font-semibold mb-4">选择字段</h4>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <!-- 可选字段 -->
          <div class="border border-base-300 rounded-lg p-4">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center gap-2">
                <input
                  type="checkbox"
                  class="checkbox checkbox-sm"
                  :checked="availableFields.length === 0"
                  :indeterminate="availableFields.length > 0 && selectedFields.length > 0"
                  @change="selectAllAvailable"
                />
                <span class="font-medium">可选字段</span>
                <span class="text-sm text-base-content/60">{{ availableFields.length }}</span>
              </div>
            </div>
            <div class="form-control mb-3">
              <input
                v-model="availableSearch"
                type="text"
                placeholder="筛选"
                class="input input-bordered input-sm"
              />
            </div>
            <div class="space-y-2 max-h-64 overflow-y-auto">
              <Draggable
                v-model="availableFields"
                item-key="id"
                :group="{
                  name: 'fields',
                  pull: true,
                  put: true,
                  sort: false
                }"
                @add="onAvailableAdd"
                @remove="onAvailableRemove"
                class="space-y-2 min-h-[2rem]"
                :animation="150"
                ghost-class="drag-ghost"
                chosen-class="drag-chosen"
                drag-class="drag-dragging"
              >
                <template #item="{ element: column }">
                  <div
                    class="flex items-center gap-2 p-2 hover:bg-base-200 rounded cursor-pointer transition-colors"
                    @click="moveToSelected(column)"
                  >
                    <svg class="w-4 h-4 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    <span class="text-sm">{{ column.label }}</span>
                  </div>
                </template>
              </Draggable>
            </div>
          </div>

          <!-- 已选字段 -->
          <div class="border border-base-300 rounded-lg p-4">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center gap-2">
                <input
                  type="checkbox"
                  class="checkbox checkbox-sm"
                  :checked="selectedFields.length === allColumns.length"
                  :indeterminate="selectedFields.length > 0 && selectedFields.length < allColumns.length"
                  @change="toggleAllSelected"
                />
                <span class="font-medium">已选字段</span>
                <span class="text-sm text-base-content/60">{{ selectedFields.length }}</span>
              </div>
            </div>
            <div class="form-control mb-3">
              <input
                v-model="selectedSearch"
                type="text"
                placeholder="筛选"
                class="input input-bordered input-sm"
              />
            </div>
            <div class="space-y-2 max-h-64 overflow-y-auto">
              <Draggable
                v-model="selectedFields"
                item-key="id"
                handle=".drag-handle"
                :group="{
                  name: 'fields',
                  pull: true,
                  put: true,
                  sort: true
                }"
                @end="onDragEnd"
                @start="onDragStart"
                @move="onDragMove"
                @add="onSelectedAdd"
                @remove="onSelectedRemove"
                class="space-y-2 min-h-[2rem]"
                :animation="150"
                ghost-class="drag-ghost"
                chosen-class="drag-chosen"
                drag-class="drag-dragging"
                :force-fallback="false"
                :fallback-tolerance="0"
                :scroll-sensitivity="100"
                :scroll-speed="20"
                :bubble-scroll="true"
              >
                <template #item="{ element: field, index }">
                  <div class="relative">
                    <div class="flex items-center gap-2 p-2 bg-base-100 border border-base-300 rounded group hover:border-primary/30 transition-colors">
                      <div class="drag-handle cursor-move text-base-content/40 hover:text-primary transition-colors">
                        <svg
                          class="w-4 h-4"
                          fill="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path d="M8 6h2v2H8V6zm6 0h2v2h-2V6zM8 10h2v2H8v-2zm6 0h2v2h-2v-2zM8 14h2v2H8v-2zm6 0h2v2h-2v-2z"/>
                        </svg>
                      </div>
                      <input
                        type="checkbox"
                        class="checkbox checkbox-sm"
                        :checked="true"
                        @change="removeFromSelected(field)"
                      />
                      <span class="text-sm flex-1">{{ field.label }}</span>
                      <div class="flex gap-1">
                        <button
                          class="btn btn-ghost btn-sm"
                          :disabled="index === 0"
                          @click="moveUp(index)"
                          title="上移"
                        >
                          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M7 14l5-5 5 5z"/>
                          </svg>
                        </button>
                        <button
                          class="btn btn-ghost btn-sm"
                          :disabled="index === selectedFields.length - 1"
                          @click="moveDown(index)"
                          title="下移"
                        >
                          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M7 10l5 5 5-5z"/>
                          </svg>
                        </button>
                        <button
                          class="btn btn-ghost btn-sm text-error hover:bg-error/10"
                          @click="removeFromSelected(field)"
                          title="移除"
                        >
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </template>
              </Draggable>
            </div>
          </div>
        </div>
      </div>

      <!-- 移动按钮 -->
      <div class="flex justify-center gap-4 mt-4">
        <button
          class="btn btn-ghost btn-sm"
          @click="moveAllToSelected"
          :disabled="availableFields.length === 0"
          title="全部添加到已选"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5l7 7-7 7"/>
          </svg>
        </button>
        <button
          class="btn btn-ghost btn-sm"
          @click="moveAllToAvailable"
          :disabled="selectedFields.length === 0"
          title="全部移除"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19l-7-7 7-7"/>
          </svg>
        </button>
      </div>

      <!-- 选择排列方式 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div class="form-control">
          <label class="label">
            <span class="label-text font-medium">选择排列方式</span>
          </label>
          <select v-model="sortField" class="select select-bordered">
            <option value="">不排序</option>
            <option v-for="column in selectedFields" :key="column.id" :value="column.key">
              {{ column.label }}
            </option>
          </select>
        </div>
        <div class="form-control">
          <label class="label">
            <span class="label-text font-medium">排序方向</span>
          </label>
          <select v-model="sortOrder" class="select select-bordered" :disabled="!sortField">
            <option value="asc">升序</option>
            <option value="desc">降序</option>
          </select>
        </div>
      </div>

      <!-- 选择文件格式 -->
      <div class="mb-6">
        <label class="label">
          <span class="label-text font-medium">选择文件格式</span>
        </label>
        <select v-model="fileFormat" class="select select-bordered w-full max-w-xs">
          <option v-for="format in availableFormats" :key="format" :value="format">
            .{{ format }}
          </option>
        </select>
      </div>

      <!-- 操作按钮 -->
      <div class="modal-action">
        <button class="btn btn-ghost" @click="close">取消</button>
        <button
          class="btn btn-primary"
          @click="confirmExport"
          :disabled="selectedFields.length === 0"
        >
          确定
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import Draggable from 'vuedraggable'
import type { ColumnConfig } from '@/types/lowcode'

// Props
const props = defineProps<{
  show: boolean
  columns: ColumnConfig[]
  availableFormats: string[]
}>()

// Emits
const emit = defineEmits<{
  'update:show': [value: boolean]
  'export': [config: {
    columns: ColumnConfig[]
    sortField?: string
    sortOrder?: 'asc' | 'desc'
    format: string
  }]
}>()

// 本地状态
const selectedFields = ref<ColumnConfig[]>([])
const availableFields = ref<ColumnConfig[]>([])
const availableSearch = ref('')
const selectedSearch = ref('')
const sortField = ref('')
const sortOrder = ref<'asc' | 'desc'>('asc')
const fileFormat = ref('xlsx')
const selectedContainer = ref<HTMLElement>()

// 拖拽状态
const dragState = ref({
  isDragging: false,
  draggedIndex: -1,
  targetIndex: -1
})

// 拖拽开始
const onDragStart = (evt: any) => {
  dragState.value.isDragging = true
  dragState.value.draggedIndex = evt.oldIndex
  console.log('开始拖拽:', evt.oldIndex)
}

// 拖拽移动
const onDragMove = (evt: any) => {
  if (evt.draggedContext && evt.draggedContext.futureIndex !== undefined) {
    dragState.value.targetIndex = evt.draggedContext.futureIndex
    console.log('拖拽到位置:', evt.draggedContext.futureIndex)
  }
  return true // 允许拖拽
}

// 拖拽结束处理
const onDragEnd = (evt: any) => {
  dragState.value.isDragging = false
  dragState.value.draggedIndex = -1
  dragState.value.targetIndex = -1
  console.log('拖拽结束，从', evt.oldIndex, '移动到', evt.newIndex)
  console.log('新顺序:', selectedFields.value.map(f => f.label))
}



// 可选字段添加处理（从已选拖拽回来）
const onAvailableAdd = (evt: any) => {
  console.log('字段添加到可选列表:', evt)
}

// 可选字段移除处理（拖拽到已选区域）
const onAvailableRemove = (evt: any) => {
  console.log('字段从可选列表移除:', evt)
}

// 已选字段添加处理（从可选拖拽过来）
const onSelectedAdd = (evt: any) => {
  console.log('字段添加到已选列表:', evt)
}

// 已选字段移除处理（拖拽到可选区域）
const onSelectedRemove = (evt: any) => {
  console.log('字段从已选列表移除:', evt)
}

// 计算属性
const allColumns = computed(() => props.columns.filter(col => col.enabled))

const filteredAvailableColumns = computed(() => {
  if (!availableSearch.value) return availableFields.value
  return availableFields.value.filter(col => 
    col.label.toLowerCase().includes(availableSearch.value.toLowerCase())
  )
})

const filteredSelectedColumns = computed(() => {
  if (!selectedSearch.value) return selectedFields.value
  return selectedFields.value.filter(col => 
    col.label.toLowerCase().includes(selectedSearch.value.toLowerCase())
  )
})

// 监听props变化
watch(() => props.show, (newShow) => {
  if (newShow) {
    // 默认全部选中
    selectedFields.value = [...allColumns.value]
    availableFields.value = []
    fileFormat.value = props.availableFormats[0] || 'xlsx'
  }
}, { immediate: true })

// 方法
const close = () => {
  emit('update:show', false)
}

const moveToSelected = (column: ColumnConfig) => {
  const index = availableFields.value.findIndex(col => col.id === column.id)
  if (index >= 0) {
    availableFields.value.splice(index, 1)
    selectedFields.value.push(column)
  }
}

const removeFromSelected = (column: ColumnConfig) => {
  const index = selectedFields.value.findIndex(col => col.id === column.id)
  if (index >= 0) {
    selectedFields.value.splice(index, 1)
    availableFields.value.push(column)
  }
}

const moveAllToSelected = () => {
  selectedFields.value.push(...availableFields.value)
  availableFields.value = []
}

const moveAllToAvailable = () => {
  availableFields.value.push(...selectedFields.value)
  selectedFields.value = []
}

const toggleAllAvailable = () => {
  if (availableFields.value.length > 0) {
    moveAllToSelected()
  }
}

const toggleAllSelected = () => {
  if (selectedFields.value.length > 0) {
    moveAllToAvailable()
  }
}

const moveUp = (index: number) => {
  if (index > 0) {
    const item = selectedFields.value.splice(index, 1)[0]
    selectedFields.value.splice(index - 1, 0, item)
  }
}

const moveDown = (index: number) => {
  if (index < selectedFields.value.length - 1) {
    const item = selectedFields.value.splice(index, 1)[0]
    selectedFields.value.splice(index + 1, 0, item)
  }
}

const confirmExport = () => {
  emit('export', {
    columns: selectedFields.value,
    sortField: sortField.value || undefined,
    sortOrder: sortField.value ? sortOrder.value : undefined,
    format: fileFormat.value
  })
  close()
}

</script>

<style scoped>
/* 固定模态框尺寸，防止拖拽时抖动 */
.modal-box {
  min-height: 90vh;
  max-height: 90vh;
}

/* 拖拽样式 - 减少变形效果 */
.drag-ghost {
  @apply opacity-40 bg-primary/10 border-2 border-primary border-dashed rounded-lg;
  /* 移除旋转和缩放，避免影响布局 */
}

.drag-chosen {
  @apply bg-primary/15 border-2 border-primary/40 shadow-lg;
  /* 移除缩放效果 */
  transition: all 0.2s ease;
}

.drag-dragging {
  @apply opacity-80 shadow-xl;
  /* 移除旋转和缩放，只保留阴影 */
  z-index: 1000;
  /* 确保拖拽元素不会超出容器 */
  max-width: 100%;
  overflow: hidden;
}

/* 拖拽区域最小高度和空状态样式 */
.min-h-\[2rem\] {
  min-height: 2rem;
}

/* 空拖拽区域的视觉提示 */
.space-y-2:empty::before {
  content: "拖拽字段到此处";
  @apply text-base-content/40 text-sm italic flex items-center justify-center h-8;
}

/* 拖拽手柄悬停效果 */
.drag-handle:hover {
  @apply text-primary;
  /* 移除缩放效果 */
  transition: color 0.2s ease;
}

/* 拖拽时的容器样式 */
.dragging-container {
  @apply bg-base-200/30 rounded-lg;
  transition: background-color 0.3s ease;
  /* 确保容器尺寸固定 */
  min-height: inherit;
}

/* 插入指示线动画 */
@keyframes insertLine {
  0%, 100% {
    opacity: 0.6;
    transform: scaleX(0.8);
  }
  50% {
    opacity: 1;
    transform: scaleX(1);
  }
}

.h-1.bg-primary.animate-pulse {
  animation: insertLine 1s ease-in-out infinite;
}

/* 防止拖拽时内容溢出 */
.relative {
  contain: layout style;
}
</style>
