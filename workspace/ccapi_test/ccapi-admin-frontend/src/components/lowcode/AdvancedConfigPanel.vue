<template>
  <div class="space-y-6">
    <!-- 自定义样式 -->
    <div class="card bg-base-200 p-4">
      <h4 class="font-semibold mb-4 flex items-center gap-2">
        <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"/>
        </svg>
        自定义样式
      </h4>
      
      <div class="form-control">
        <label class="label">
          <span class="label-text">自定义CSS</span>
          <span class="label-text-alt">将应用到生成的页面中</span>
        </label>
        <textarea 
          v-model="localAdvanced.customCSS"
          class="textarea textarea-bordered font-mono text-sm"
          rows="8"
          placeholder="/* 自定义CSS样式 */
.my-table {
  border-radius: 8px;
}

.my-search-form {
  background: #f5f5f5;
  padding: 16px;
}"
        ></textarea>
      </div>
    </div>

    <!-- 自定义脚本 -->
    <div class="card bg-base-200 p-4">
      <h4 class="font-semibold mb-4 flex items-center gap-2">
        <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
        </svg>
        自定义脚本
      </h4>
      
      <div class="form-control">
        <label class="label">
          <span class="label-text">自定义JavaScript</span>
          <span class="label-text-alt">用于扩展功能，请谨慎使用</span>
        </label>
        <textarea 
          v-model="localAdvanced.customJS"
          class="textarea textarea-bordered font-mono text-sm"
          rows="8"
          placeholder="// 自定义JavaScript代码
// 可以访问全局变量: tableData, searchParams, config

function customFormatter(value, row, column) {
  // 自定义格式化函数
  return value;
}

function beforeSearch(params) {
  // 搜索前的钩子函数
  console.log('搜索参数:', params);
  return params;
}"
        ></textarea>
      </div>
    </div>

    <!-- 事件钩子 -->
    <div class="card bg-base-200 p-4">
      <h4 class="font-semibold mb-4 flex items-center gap-2">
        <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
        </svg>
        事件钩子
      </h4>
      
      <div class="space-y-4">
        <div class="form-control">
          <label class="label">
            <span class="label-text">搜索前钩子</span>
            <span class="label-text-alt">在发起搜索请求前执行</span>
          </label>
          <textarea 
            v-model="localAdvanced.hooks.beforeSearch"
            class="textarea textarea-bordered font-mono text-sm"
            rows="4"
            placeholder="// 搜索前钩子函数
function beforeSearch(searchParams) {
  // 可以修改搜索参数
  console.log('搜索参数:', searchParams);
  return searchParams;
}"
          ></textarea>
        </div>

        <div class="form-control">
          <label class="label">
            <span class="label-text">搜索后钩子</span>
            <span class="label-text-alt">在搜索请求完成后执行</span>
          </label>
          <textarea 
            v-model="localAdvanced.hooks.afterSearch"
            class="textarea textarea-bordered font-mono text-sm"
            rows="4"
            placeholder="// 搜索后钩子函数
function afterSearch(data, searchParams) {
  // 可以处理返回的数据
  console.log('搜索结果:', data);
  return data;
}"
          ></textarea>
        </div>

        <div class="form-control">
          <label class="label">
            <span class="label-text">导出前钩子</span>
            <span class="label-text-alt">在导出数据前执行</span>
          </label>
          <textarea 
            v-model="localAdvanced.hooks.beforeExport"
            class="textarea textarea-bordered font-mono text-sm"
            rows="4"
            placeholder="// 导出前钩子函数
function beforeExport(data, exportConfig) {
  // 可以修改导出数据
  console.log('导出数据:', data);
  return data;
}"
          ></textarea>
        </div>

        <div class="form-control">
          <label class="label">
            <span class="label-text">导出后钩子</span>
            <span class="label-text-alt">在导出完成后执行</span>
          </label>
          <textarea 
            v-model="localAdvanced.hooks.afterExport"
            class="textarea textarea-bordered font-mono text-sm"
            rows="4"
            placeholder="// 导出后钩子函数
function afterExport(result, exportConfig) {
  // 导出完成后的处理
  console.log('导出完成:', result);
}"
          ></textarea>
        </div>
      </div>
    </div>

    <!-- 代码预览 -->
    <div class="card bg-base-200 p-4">
      <h4 class="font-semibold mb-4 flex items-center gap-2">
        <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
        </svg>
        代码预览
      </h4>
      
      <div class="space-y-4">
        <div class="flex gap-2">
          <button 
            class="btn btn-outline btn-sm"
            @click="validateCode"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            验证代码
          </button>
          
          <button 
            class="btn btn-outline btn-sm"
            @click="formatCode"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
            格式化代码
          </button>
          
          <button 
            class="btn btn-outline btn-sm"
            @click="clearCode"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
            </svg>
            清空代码
          </button>
        </div>

        <div v-if="validationResult" class="alert" :class="validationResult.success ? 'alert-success' : 'alert-error'">
          <svg v-if="validationResult.success" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          <svg v-else class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          <span>{{ validationResult.message }}</span>
        </div>
      </div>
    </div>

    <!-- 安全提示 -->
    <div class="alert alert-warning">
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
      </svg>
      <div>
        <h3 class="font-bold">安全提示</h3>
        <div class="text-xs">
          自定义代码将在客户端执行，请确保代码安全可靠。避免执行不受信任的代码，以防止XSS攻击。
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

// Props
const props = defineProps<{
  modelValue?: {
    customCSS?: string
    customJS?: string
    hooks?: {
      beforeSearch?: string
      afterSearch?: string
      beforeExport?: string
      afterExport?: string
    }
  }
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: any]
}>()

// 本地状态
const localAdvanced = ref({
  customCSS: '',
  customJS: '',
  hooks: {
    beforeSearch: '',
    afterSearch: '',
    beforeExport: '',
    afterExport: ''
  },
  ...props.modelValue
})

const validationResult = ref<{ success: boolean; message: string } | null>(null)

// 监听变化
watch(localAdvanced, (newValue) => {
  emit('update:modelValue', newValue)
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    const newAdvanced = {
      customCSS: '',
      customJS: '',
      hooks: {
        beforeSearch: '',
        afterSearch: '',
        beforeExport: '',
        afterExport: ''
      },
      ...newValue
    }
    // 避免递归更新：只有当值真正不同时才更新
    if (JSON.stringify(newAdvanced) !== JSON.stringify(localAdvanced.value)) {
      localAdvanced.value = newAdvanced
    }
  }
}, { deep: true })

// 方法
const validateCode = () => {
  try {
    // 简单的JavaScript语法验证
    if (localAdvanced.value.customJS) {
      new Function(localAdvanced.value.customJS)
    }
    
    // 验证钩子函数
    Object.values(localAdvanced.value.hooks).forEach(hook => {
      if (hook) {
        new Function(hook)
      }
    })
    
    validationResult.value = {
      success: true,
      message: '代码验证通过'
    }
  } catch (error) {
    validationResult.value = {
      success: false,
      message: `代码验证失败: ${(error as Error).message}`
    }
  }
  
  // 3秒后清除验证结果
  setTimeout(() => {
    validationResult.value = null
  }, 3000)
}

const formatCode = () => {
  // 简单的代码格式化（实际项目中可以使用prettier等工具）
  try {
    if (localAdvanced.value.customJS) {
      // 这里可以集成代码格式化工具
      console.log('格式化JavaScript代码')
    }
    
    if (localAdvanced.value.customCSS) {
      // 这里可以集成CSS格式化工具
      console.log('格式化CSS代码')
    }
    
    validationResult.value = {
      success: true,
      message: '代码格式化完成'
    }
  } catch (error) {
    validationResult.value = {
      success: false,
      message: '代码格式化失败'
    }
  }
  
  setTimeout(() => {
    validationResult.value = null
  }, 3000)
}

const clearCode = () => {
  if (confirm('确定要清空所有自定义代码吗？此操作不可恢复。')) {
    localAdvanced.value = {
      customCSS: '',
      customJS: '',
      hooks: {
        beforeSearch: '',
        afterSearch: '',
        beforeExport: '',
        afterExport: ''
      }
    }
  }
}
</script>
