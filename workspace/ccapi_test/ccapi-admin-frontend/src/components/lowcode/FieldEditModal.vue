<template>
  <div v-if="show" class="modal modal-open">
    <div class="modal-box w-11/12 max-w-2xl">
      <h3 class="font-bold text-lg mb-4">编辑搜索字段</h3>
      
      <div v-if="localField" class="space-y-4">
        <!-- 基本信息 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text">字段标识 *</span>
            </label>
            <input 
              v-model="localField.key"
              type="text" 
              class="input input-bordered"
              placeholder="fieldKey"
              required
            />
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">显示名称 *</span>
            </label>
            <input 
              v-model="localField.label"
              type="text" 
              class="input input-bordered"
              placeholder="字段名称"
              required
            />
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text">字段类型</span>
            </label>
            <select 
              v-model="localField.type"
              class="select select-bordered"
            >
              <option value="input">文本输入</option>
              <option value="select">下拉选择</option>
              <option value="date">日期选择</option>
              <option value="daterange">日期范围</option>
              <option value="number">数字输入</option>
              <option value="textarea">多行文本</option>
            </select>
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">占位符</span>
            </label>
            <input 
              v-model="localField.placeholder"
              type="text" 
              class="input input-bordered"
              placeholder="请输入..."
            />
          </div>
        </div>

        <!-- 选项配置（仅下拉选择类型） -->
        <div v-if="localField.type === 'select'" class="form-control">
          <label class="label">
            <span class="label-text">选项配置</span>
          </label>
          <div class="space-y-2">
            <div 
              v-for="(option, index) in localField.options || []" 
              :key="index"
              class="flex gap-2"
            >
              <input 
                v-model="option.label"
                type="text" 
                class="input input-bordered input-sm flex-1"
                placeholder="显示文本"
              />
              <input 
                v-model="option.value"
                type="text" 
                class="input input-bordered input-sm flex-1"
                placeholder="选项值"
              />
              <button 
                class="btn btn-ghost btn-sm text-error"
                @click="removeOption(index)"
              >
                删除
              </button>
            </div>
            
            <button 
              class="btn btn-outline btn-sm"
              @click="addOption"
            >
              添加选项
            </button>
          </div>
        </div>

        <!-- 验证规则 -->
        <div class="form-control">
          <label class="label">
            <span class="label-text">验证规则</span>
          </label>
          <div class="flex flex-wrap gap-4">
            <label class="label cursor-pointer">
              <input 
                v-model="isRequired"
                type="checkbox" 
                class="checkbox checkbox-sm"
              />
              <span class="label-text ml-2">必填</span>
            </label>
            
            <label class="label cursor-pointer">
              <input 
                v-model="localField.clearable"
                type="checkbox" 
                class="checkbox checkbox-sm"
              />
              <span class="label-text ml-2">可清空</span>
            </label>
            
            <label v-if="localField.type === 'select'" class="label cursor-pointer">
              <input 
                v-model="localField.multiple"
                type="checkbox" 
                class="checkbox checkbox-sm"
              />
              <span class="label-text ml-2">多选</span>
            </label>
          </div>
        </div>

        <!-- 默认值 -->
        <div class="form-control">
          <label class="label">
            <span class="label-text">默认值</span>
          </label>
          <input 
            v-model="localField.defaultValue"
            type="text" 
            class="input input-bordered"
            placeholder="默认值"
          />
        </div>

        <!-- 宽度设置 -->
        <div class="space-y-4">
          <h5 class="font-medium">宽度配置</h5>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">字段宽度</span>
              </label>
              <select
                v-model="localField.width"
                class="select select-bordered"
                @change="onPresetWidthChange"
              >
                <option value="">自动</option>
                <option value="120px">120px</option>
                <option value="150px">150px</option>
                <option value="180px">180px</option>
                <option value="200px">200px</option>
                <option value="250px">250px</option>
                <option value="300px">300px</option>
                <option value="350px">350px</option>
                <option value="400px">400px</option>
                <option value="20%">20%</option>
                <option value="25%">25%</option>
                <option value="30%">30%</option>
                <option value="33.33%">33.33%</option>
                <option value="40%">40%</option>
                <option value="50%">50%</option>
                <option value="100%">100%</option>
              </select>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text">自定义宽度</span>
              </label>
              <input
                v-model="customWidth"
                type="text"
                class="input input-bordered"
                placeholder="如：250px, 30%, 15rem"
                @input="onCustomWidthInput"
                @blur="applyCustomWidth"
              />
              <div class="label">
                <span class="label-text-alt">支持 px、%、rem、em 等单位</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-action">
        <button 
          class="btn btn-primary"
          @click="save"
          :disabled="!canSave"
        >
          保存
        </button>
        <button 
          class="btn btn-ghost"
          @click="cancel"
        >
          取消
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { SearchFieldConfig } from '@/types/lowcode'

// Props
const props = defineProps<{
  show: boolean
  field: SearchFieldConfig | null
}>()

// Emits
const emit = defineEmits<{
  'update:show': [value: boolean]
  'update:field': [value: SearchFieldConfig | null]
  'save': [field: SearchFieldConfig]
}>()

// 本地状态
const localField = ref<SearchFieldConfig | null>(null)
const customWidth = ref('')

// 计算属性
const isRequired = computed({
  get: () => {
    return localField.value?.validation?.some(rule => rule.type === 'required') || false
  },
  set: (value: boolean) => {
    if (!localField.value) return
    
    if (value) {
      if (!localField.value.validation) {
        localField.value.validation = []
      }
      if (!localField.value.validation.some(rule => rule.type === 'required')) {
        localField.value.validation.push({
          type: 'required',
          message: '此字段为必填项'
        })
      }
    } else {
      if (localField.value.validation) {
        localField.value.validation = localField.value.validation.filter(rule => rule.type !== 'required')
      }
    }
  }
})

const canSave = computed(() => {
  return localField.value?.key && localField.value?.label
})

// 监听props变化
watch(() => props.field, (newField) => {
  if (newField) {
    localField.value = { ...newField }
    // 确保options数组存在
    if (localField.value.type === 'select' && !localField.value.options) {
      localField.value.options = []
    }

    // 智能分配宽度到预设或自定义
    const width = newField.width || ''
    const presetOptions = ['', '120px', '150px', '180px', '200px', '250px', '300px', '350px', '400px', '20%', '25%', '30%', '33.33%', '40%', '50%', '100%']

    if (presetOptions.includes(width)) {
      // 如果是预设选项，设置到预设选择框，清空自定义输入
      localField.value.width = width
      customWidth.value = ''
    } else {
      // 如果是自定义值，设置到自定义输入框，清空预设选择
      localField.value.width = ''
      customWidth.value = width
    }
  } else {
    localField.value = null
    customWidth.value = ''
  }
}, { immediate: true })

// 方法
const addOption = () => {
  if (localField.value && localField.value.type === 'select') {
    if (!localField.value.options) {
      localField.value.options = []
    }
    localField.value.options.push({ label: '', value: '' })
  }
}

const removeOption = (index: number) => {
  if (localField.value?.options) {
    localField.value.options.splice(index, 1)
  }
}

// 预设宽度选择变化时，清空自定义宽度
const onPresetWidthChange = () => {
  if (localField.value?.width) {
    customWidth.value = ''
  }
}

// 自定义宽度输入时，清空预设宽度选择
const onCustomWidthInput = () => {
  if (customWidth.value && localField.value) {
    localField.value.width = ''
  }
}

const applyCustomWidth = () => {
  if (customWidth.value && localField.value) {
    // 验证自定义宽度格式
    const widthPattern = /^(\d+(\.\d+)?)(px|%|rem|em|vw|vh|auto)$/
    if (widthPattern.test(customWidth.value.trim()) || customWidth.value.trim() === 'auto') {
      localField.value.width = customWidth.value.trim()
      // 不清空输入框，保持用户输入的值
    }
  }
}

const save = () => {
  if (localField.value && canSave.value) {
    emit('save', localField.value)
  }
}

const cancel = () => {
  emit('update:show', false)
}
</script>
