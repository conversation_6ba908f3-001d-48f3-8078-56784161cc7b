<template>
  <div class="lowcode-renderer h-full flex flex-col overflow-visible">
    <!-- 搜索表单 -->
    <div v-if="enabledSearchFields.length > 0" class="search-form bg-base-100 p-4 rounded-lg shadow-sm mb-4">
      <form @submit.prevent="handleSearch" class="space-y-4">
        <div
          :class="searchContainerClass"
          :style="searchContainerStyle"
        >
          <div
            v-for="field in enabledSearchFields"
            :key="field.id"
            class="form-control"
            :style="getFieldStyle(field)"
          >
            <label v-if="field.label && showLabels" class="label">
              <span class="label-text">
                {{ field.label }}
                <span v-if="isRequired(field)" class="text-error">*</span>
              </span>
            </label>
            
            <!-- 文本输入 -->
            <input
              v-if="field.type === 'input'"
              v-model="searchForm[field.key]"
              type="text"
              class="input input-bordered input-sm"
              :placeholder="field.placeholder"
              :required="isRequired(field)"
            />
            
            <!-- 下拉选择 -->
            <select
              v-else-if="field.type === 'select'"
              v-model="searchForm[field.key]"
              class="select select-bordered select-sm"
              :multiple="field.multiple"
              :required="isRequired(field)"
            >
              <option value="">{{ field.placeholder || '请选择' }}</option>
              <option 
                v-for="option in field.options" 
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </option>
            </select>
            
            <!-- 日期选择 -->
            <input
              v-else-if="field.type === 'date'"
              v-model="searchForm[field.key]"
              type="date"
              class="input input-bordered input-sm"
              :required="isRequired(field)"
            />
            
            <!-- 日期范围 -->
            <div v-else-if="field.type === 'daterange'" class="flex gap-2">
              <input
                v-model="searchForm[field.key + 'Begin']"
                type="date"
                class="input input-bordered input-sm flex-1"
                placeholder="开始日期"
              />
              <input
                v-model="searchForm[field.key + 'End']"
                type="date"
                class="input input-bordered input-sm flex-1"
                placeholder="结束日期"
              />
            </div>
            
            <!-- 数字输入 -->
            <input
              v-else-if="field.type === 'number'"
              v-model.number="searchForm[field.key]"
              type="number"
              class="input input-bordered input-sm"
              :placeholder="field.placeholder"
              :required="isRequired(field)"
            />
            
            <!-- 多行文本 -->
            <textarea
              v-else-if="field.type === 'textarea'"
              v-model="searchForm[field.key]"
              class="textarea textarea-bordered textarea-sm"
              :placeholder="field.placeholder"
              :required="isRequired(field)"
              rows="3"
            ></textarea>
          </div>

          <!-- 按钮区域 -->
          <div
            v-if="searchLayoutConfig.buttonConfig?.position === 'inline'"
            :class="buttonContainerClass"
            :style="buttonContainerStyle"
          >
            <button
              v-for="button in enabledButtons"
              :key="button.type"
              :type="getButtonType(button)"
              :class="getButtonClass(button)"
              :disabled="loading && button.type === 'search'"
              :style="getButtonStyle(button)"
              @click="handleButtonClick(button)"
            >
              <span v-if="loading && button.type === 'search'" class="loading loading-spinner loading-sm"></span>
              <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="getButtonIconPath(button)"/>
              </svg>
              {{ getButtonLabel(button.type) }}
            </button>
          </div>
        </div>

        <!-- 独立按钮行 -->
        <div
          v-if="searchLayoutConfig.buttonConfig?.position === 'newline'"
          class="flex gap-2 pt-2"
          :style="{ justifyContent: getButtonAlignment() }"
        >
          <button
            v-for="button in enabledButtons"
            :key="button.type"
            :type="getButtonType(button)"
            :class="getButtonClass(button)"
            :disabled="loading && button.type === 'search'"
            :style="getButtonStyle(button)"
            @click="handleButtonClick(button)"
          >
            <span v-if="loading && button.type === 'search'" class="loading loading-spinner loading-sm"></span>
            <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="getButtonIconPath(button)"/>
            </svg>
            {{ getButtonLabel(button.type) }}
          </button>
        </div>
      </form>
    </div>

    <!-- 表格工具栏 -->
    <div class="table-toolbar bg-base-100 p-4 rounded-lg shadow-sm mb-4 flex items-center justify-between relative">
      <div class="flex items-center gap-4">
        <div v-if="config.tableConfig.features.selection && selectedRows.length > 0" class="flex items-center gap-2">
          <span class="text-sm text-base-content/60">
            已选择 {{ selectedRows.length }} 项
          </span>
          <button 
            class="btn btn-ghost btn-sm"
            @click="clearSelection"
          >
            清空选择
          </button>
        </div>
        
        <div class="text-sm text-base-content/60">
          共 {{ total }} 条记录
        </div>
      </div>
      
      <div class="flex items-center gap-2">
        <button 
          v-if="config.tableConfig.features.refresh"
          class="btn btn-ghost btn-sm"
          @click="handleRefresh"
          :disabled="loading"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          刷新
        </button>
        
        <button
          v-if="config.exportConfig.enabled"
          class="btn btn-ghost btn-sm"
          @click="showExportModal = true"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          导出
        </button>
        

      </div>
    </div>

    <!-- 数据表格 -->
    <div class="flex-1 bg-base-100 rounded-lg shadow-sm overflow-hidden">
      <div class="overflow-x-auto h-full">
        <table
          class="table w-full"
          :class="tableClasses"
          :style="tableStyles"
        >
          <thead class="sticky top-0 bg-base-200 z-10">
            <tr>
              <th v-if="config.tableConfig.features.selection" class="w-12">
                <input 
                  type="checkbox" 
                  class="checkbox"
                  :checked="isAllSelected"
                  :indeterminate="isIndeterminate"
                  @change="toggleSelectAll"
                />
              </th>
              <th 
                v-for="column in visibleColumns" 
                :key="column.id"
                :style="getColumnStyle(column)"
                :class="getColumnClass(column)"
                @click="handleSort(column)"
              >
                <div class="flex items-center gap-2">
                  {{ column.label }}
                  <div v-if="column.sortable" class="flex flex-col">
                    <svg 
                      class="w-3 h-3"
                      :class="getSortIconClass(column, 'asc')"
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
                    </svg>
                    <svg 
                      class="w-3 h-3 -mt-1"
                      :class="getSortIconClass(column, 'desc')"
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                    </svg>
                  </div>
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="loading">
              <td :colspan="totalColumns" class="text-center py-8">
                <div class="flex items-center justify-center gap-2">
                  <span class="loading loading-spinner loading-md"></span>
                  <span>加载中...</span>
                </div>
              </td>
            </tr>
            
            <tr v-else-if="data.length === 0">
              <td :colspan="totalColumns" class="text-center py-8">
                <div class="text-base-content/60">
                  <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2M4 13h2m0 0V9a2 2 0 012-2h8a2 2 0 012 2v4M6 13h12"/>
                  </svg>
                  <p>暂无数据</p>
                </div>
              </td>
            </tr>
            
            <tr 
              v-else
              v-for="(row, index) in data" 
              :key="getRowKey(row, index)"
              class="hover:bg-base-200/50 transition-colors"
            >
              <td v-if="config.tableConfig.features.selection">
                <input 
                  type="checkbox" 
                  class="checkbox"
                  :checked="isRowSelected(row)"
                  @change="toggleRowSelection(row)"
                />
              </td>
              <td 
                v-for="column in visibleColumns" 
                :key="column.id"
                :class="getColumnClass(column)"
              >
                <span>{{ formatValue(getCellValue(row, column), column.formatter) }}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="config.tableConfig.pagination && total > 0" class="pagination-wrapper bg-base-100 p-4 rounded-lg shadow-sm mt-4">
      <div class="flex items-center justify-between">
        <div class="text-sm text-base-content/60">
          显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, total) }} 条，共 {{ total }} 条
        </div>
        <div class="join">
          <button
            class="join-item btn btn-sm"
            :disabled="currentPage <= 1"
            @click="handlePrevPage"
          >
            上一页
          </button>
          <button class="join-item btn btn-sm btn-active">{{ currentPage }}</button>
          <button
            class="join-item btn btn-sm"
            :disabled="currentPage >= Math.ceil(total / pageSize)"
            @click="handleNextPage"
          >
            下一页
          </button>
        </div>
      </div>
    </div>

    <!-- 导出配置弹窗 -->
    <ExportModal
      v-model:show="showExportModal"
      :columns="props.config.tableConfig.columns"
      :available-formats="['xlsx', 'csv', 'pdf']"
      @export="handleCustomExport"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import type { LowCodeConfig, RuntimeState, SearchLayoutConfig, ButtonConfig } from '@/types/lowcode'
import {
  getNestedValue,
  formatValue,
  buildSearchParams,
  buildSortParams,
  generateExportFilename
} from '@/utils/lowcode'
import ExportModal from './ExportModal.vue'
// import LowCodePagination from './LowCodePagination.vue'
// import DefaultCell from './cells/DefaultCell.vue'

// Props
const props = defineProps<{
  config: LowCodeConfig
  runtimeState?: RuntimeState
}>()

// Emits
const emit = defineEmits<{
  'search': [params: Record<string, any>]
  'export': [format: string, data: any[], config?: any]
  'update:runtimeState': [state: Partial<RuntimeState>]
  'customAction': [payload: { action: string; button: ButtonConfig; searchParams: Record<string, any> }]
}>()

// 本地状态
const searchForm = ref<Record<string, any>>({})
const showExportModal = ref(false)

// 默认搜索布局配置
const defaultSearchLayoutConfig: SearchLayoutConfig = {
  gap: '16px',
  labelPosition: 'top',
  buttonConfig: {
    position: 'newline',
    alignment: 'start',
    buttons: [
      {
        type: 'search',
        enabled: true
      },
      {
        type: 'reset',
        enabled: true
      },
      {
        type: 'export',
        enabled: true
      }
    ]
  }
}

// 计算属性
const enabledSearchFields = computed(() => {
  return props.config.searchConfig.filter(field => field.enabled)
})

const visibleColumns = computed(() => {
  return props.config.tableConfig.columns.filter(column => column.enabled)
})

const enabledButtons = computed(() => {
  const buttons = searchLayoutConfig.value.buttonConfig?.buttons || []
  return buttons.filter(button => button.enabled)
})

const searchLayoutConfig = computed(() => {
  return { ...defaultSearchLayoutConfig, ...props.config.searchLayoutConfig }
})

const searchContainerClass = computed(() => {
  return 'flex flex-wrap items-end'
})

const searchContainerStyle = computed(() => {
  const layout = searchLayoutConfig.value
  return {
    gap: layout.gap || '16px'
  }
})

const buttonContainerClass = computed(() => {
  return 'flex gap-2 items-end'
})

const buttonContainerStyle = computed(() => {
  return {}
})

const getButtonAlignment = () => {
  const alignment = searchLayoutConfig.value.buttonConfig?.alignment || 'start'
  const alignmentMap = {
    start: 'flex-start',
    center: 'center',
    end: 'flex-end'
  }
  return alignmentMap[alignment]
}

const showLabels = computed(() => {
  return searchLayoutConfig.value.labelPosition !== 'none'
})

const tableClasses = computed(() => {
  const classes = []

  if (props.config.tableConfig.size === 'small') classes.push('table-xs')
  else if (props.config.tableConfig.size === 'large') classes.push('table-lg')

  if (props.config.tableConfig.striped) classes.push('table-zebra')
  if (props.config.tableConfig.bordered) classes.push('table-bordered')

  return classes.join(' ')
})

const tableStyles = computed(() => {
  // 根据表格尺寸设置不同的行高
  const sizeConfig = {
    small: { rowHeight: '2.5rem', maxRowHeight: '3rem' },
    medium: { rowHeight: '3rem', maxRowHeight: '4rem' },
    large: { rowHeight: '3.5rem', maxRowHeight: '5rem' }
  }

  const size = props.config.tableConfig.size || 'medium'
  const config = sizeConfig[size]

  return {
    '--table-row-height': config.rowHeight,
    '--table-max-row-height': config.maxRowHeight
  }
})

const totalColumns = computed(() => {
  let count = visibleColumns.value.length
  if (props.config.tableConfig.features.selection) count++
  return count
})

// 运行时状态
const loading = computed(() => props.runtimeState?.loading || false)
const data = computed(() => props.runtimeState?.data || [])
const total = computed(() => props.runtimeState?.total || 0)
const currentPage = computed({
  get: () => props.runtimeState?.currentPage || 1,
  set: (value) => updateRuntimeState({ currentPage: value })
})
const pageSize = computed({
  get: () => props.runtimeState?.pageSize || props.config.tableConfig.pagination.pageSize,
  set: (value) => updateRuntimeState({ pageSize: value })
})
const selectedRows = computed(() => props.runtimeState?.selectedRows || [])
const sortField = computed(() => props.runtimeState?.sortField)
const sortOrder = computed(() => props.runtimeState?.sortOrder)

// 选择相关计算属性
const isAllSelected = computed(() => {
  return data.value.length > 0 && selectedRows.value.length === data.value.length
})

const isIndeterminate = computed(() => {
  return selectedRows.value.length > 0 && selectedRows.value.length < data.value.length
})

// 方法
const updateRuntimeState = (updates: Partial<RuntimeState>) => {
  emit('update:runtimeState', updates)
}

const isRequired = (field: any) => {
  return field.validation?.some((rule: any) => rule.type === 'required') || false
}

const getFieldStyle = (field: any) => {
  const style: any = {}

  // 设置宽度
  if (field.width) {
    style.width = field.width
  }

  // 确保不会收缩
  style.flexShrink = 0

  return style
}

// 按钮相关方法
const getButtonType = (button: ButtonConfig) => {
  return button.type === 'search' ? 'submit' : 'button'
}

const getButtonClass = (button: ButtonConfig) => {
  const classMap = {
    search: 'btn btn-primary btn-sm',
    reset: 'btn btn-ghost btn-sm',
    export: 'btn btn-secondary btn-sm'
  }
  return classMap[button.type] || 'btn btn-ghost btn-sm'
}

const getButtonLabel = (type: ButtonType) => {
  const labelMap = {
    search: '查询',
    reset: '重置',
    export: '自定义导出'
  }
  return labelMap[type]
}

const getButtonStyle = (button: ButtonConfig) => {
  const style: any = {}
  if (button.width) {
    style.width = button.width
  }
  style.flexShrink = 0
  return style
}

const getButtonIconPath = (button: ButtonConfig) => {
  // 按钮图标路径映射
  const iconPaths = {
    search: 'M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z',
    reset: 'M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15',
    export: 'M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
  }

  return iconPaths[button.type] || iconPaths.search
}

const handleButtonClick = (button: ButtonConfig) => {
  switch (button.type) {
    case 'search':
      handleSearch()
      break
    case 'reset':
      handleReset()
      break
    case 'export':
      handleExport('xlsx')
      break
    case 'refresh':
      handleRefresh()
      break
    default:
      console.log(`按钮点击: ${button.type}`, button)
  }
}

const handleCustomAction = (button: ButtonConfig) => {
  // 触发自定义按钮事件
  emit('customAction', {
    action: button.type,
    button: button,
    searchParams: searchParams.value
  })
}

const handleRefresh = () => {
  // 触发刷新事件
  handleSearch()
}

const handleSearch = () => {
  const params = buildSearchParams(searchForm.value, enabledSearchFields.value)
  emit('search', params)
}

const handleReset = () => {
  searchForm.value = {}
  // 设置默认值
  enabledSearchFields.value.forEach(field => {
    if (field.defaultValue !== undefined) {
      searchForm.value[field.key] = field.defaultValue
    }
  })
  handleSearch()
}

const handleExport = (format: string) => {
  const exportData = selectedRows.value.length > 0 ? selectedRows.value : data.value
  emit('export', format, exportData)
}

const handleCustomExport = (config: any) => {
  // 根据配置处理导出数据
  let exportData = selectedRows.value.length > 0 ? selectedRows.value : data.value

  // 如果有排序配置，对数据进行排序
  if (config.sortField && config.sortOrder) {
    exportData = [...exportData].sort((a, b) => {
      const aValue = getNestedValue(a, config.sortField)
      const bValue = getNestedValue(b, config.sortField)

      if (config.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })
  }

  // 只导出选中的列
  const filteredData = exportData.map(row => {
    const filteredRow: any = {}
    config.columns.forEach((column: any) => {
      filteredRow[column.key] = getNestedValue(row, column.key)
    })
    return filteredRow
  })

  // 发送导出事件，包含配置信息
  emit('export', config.format, filteredData, {
    columns: config.columns,
    sortField: config.sortField,
    sortOrder: config.sortOrder
  })
}

const handleSort = (column: any) => {
  if (!column.sortable) return
  
  let newOrder: 'asc' | 'desc' = 'asc'
  
  if (sortField.value === column.key) {
    newOrder = sortOrder.value === 'asc' ? 'desc' : 'asc'
  }
  
  updateRuntimeState({
    sortField: column.key,
    sortOrder: newOrder
  })
  
  handleSearch()
}

const handlePageChange = () => {
  handleSearch()
}

const handlePrevPage = () => {
  if (currentPage.value > 1) {
    updateRuntimeState({ currentPage: currentPage.value - 1 })
    handlePageChange()
  }
}

const handleNextPage = () => {
  const maxPage = Math.ceil(total.value / pageSize.value)
  if (currentPage.value < maxPage) {
    updateRuntimeState({ currentPage: currentPage.value + 1 })
    handlePageChange()
  }
}

const toggleSelectAll = () => {
  if (isAllSelected.value) {
    updateRuntimeState({ selectedRows: [] })
  } else {
    updateRuntimeState({ selectedRows: [...data.value] })
  }
}

const toggleRowSelection = (row: any) => {
  const newSelection = [...selectedRows.value]
  const index = newSelection.findIndex(item => getRowKey(item) === getRowKey(row))
  
  if (index >= 0) {
    newSelection.splice(index, 1)
  } else {
    newSelection.push(row)
  }
  
  updateRuntimeState({ selectedRows: newSelection })
}

const clearSelection = () => {
  updateRuntimeState({ selectedRows: [] })
}

const isRowSelected = (row: any) => {
  return selectedRows.value.some(item => getRowKey(item) === getRowKey(row))
}

const getRowKey = (row: any, index?: number) => {
  const keyField = props.config.tableConfig.rowKey
  return getNestedValue(row, keyField) || index || Math.random()
}

const getColumnStyle = (column: any) => {
  const styles: Record<string, string> = {}
  
  if (column.width) styles.width = `${column.width}px`
  if (column.minWidth) styles.minWidth = `${column.minWidth}px`
  
  return styles
}

const getColumnClass = (column: any) => {
  const classes = []
  
  if (column.align) classes.push(`text-${column.align}`)
  if (column.fixed) classes.push('sticky', column.fixed === 'left' ? 'left-0' : 'right-0')
  
  return classes.join(' ')
}

const getSortIconClass = (column: any, direction: 'asc' | 'desc') => {
  const isActive = sortField.value === column.key && sortOrder.value === direction
  return isActive ? 'text-primary' : 'text-base-content/30'
}

const getCellValue = (row: any, column: any) => {
  return getNestedValue(row, column.key)
}

const getCellComponent = (column: any) => {
  // 这里可以根据列类型返回不同的单元格组件
  return DefaultCell
}

// 初始化
onMounted(() => {
  // 设置搜索表单默认值
  enabledSearchFields.value.forEach(field => {
    if (field.defaultValue !== undefined) {
      searchForm.value[field.key] = field.defaultValue
    }
  })
  
  // 自动执行首次搜索
  handleSearch()
})
</script>

<style scoped>
.lowcode-renderer {
  --table-row-height: 3rem;
  --table-max-row-height: 4rem;
}

/* 表格行高度控制 */
.table tbody tr {
  height: var(--table-row-height);
  max-height: var(--table-max-row-height);
}

.table th {
  position: sticky;
  top: 0;
  z-index: 10;
  height: var(--table-row-height);
  padding: 0.75rem 1rem;
}

.table td {
  vertical-align: middle;
  height: var(--table-row-height);
  max-height: var(--table-max-row-height);
  padding: 0.75rem 1rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 确保表格不会被拉伸填充容器 */
.table {
  table-layout: fixed;
}

/* 当数据较少时，表格容器不会拉伸 */
.overflow-x-auto {
  min-height: auto;
}

/* 工具栏下拉菜单样式优化 */
.table-toolbar {
  overflow: visible;
  z-index: 10;
}

.table-toolbar .dropdown {
  position: relative;
}

.table-toolbar .dropdown-content {
  position: absolute;
  right: 0;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 向下展开的下拉菜单 */
.table-toolbar .dropdown:not(.dropdown-top) .dropdown-content {
  top: 100%;
  margin-top: 0.5rem;
}

/* 向上展开的下拉菜单 */
.table-toolbar .dropdown.dropdown-top .dropdown-content {
  bottom: 100%;
  margin-bottom: 0.5rem;
}

/* 针对不同尺寸的特殊处理 */
.table-xs tbody tr,
.table-xs th,
.table-xs td {
  height: 2.5rem;
  max-height: 3rem;
  padding: 0.5rem 0.75rem;
}

.table-lg tbody tr,
.table-lg th,
.table-lg td {
  height: 3.5rem;
  max-height: 5rem;
  padding: 1rem 1.25rem;
}
</style>
