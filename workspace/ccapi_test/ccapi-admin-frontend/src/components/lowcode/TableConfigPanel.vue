<template>
  <div class="space-y-6">
    <!-- 表格基础配置 -->
    <div class="card bg-base-200 p-4">
      <h4 class="font-semibold mb-4 flex items-center gap-2">
        <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 6h18m-9 8h9"/>
        </svg>
        表格基础配置
      </h4>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="form-control">
          <label class="label">
            <span class="label-text">表格尺寸</span>
          </label>
          <select 
            v-model="localTableConfig.size"
            class="select select-bordered"
          >
            <option value="small">紧凑</option>
            <option value="medium">标准</option>
            <option value="large">宽松</option>
          </select>
        </div>
        
        <div class="form-control">
          <label class="label">
            <span class="label-text">行唯一标识</span>
          </label>
          <input 
            v-model="localTableConfig.rowKey"
            type="text" 
            class="input input-bordered"
            placeholder="id"
          />
        </div>
        
        <div class="form-control">
          <label class="label">
            <span class="label-text">每页显示</span>
          </label>
          <select 
            v-model="localTableConfig.pagination.pageSize"
            class="select select-bordered"
          >
            <option :value="10">10条/页</option>
            <option :value="20">20条/页</option>
            <option :value="50">50条/页</option>
            <option :value="100">100条/页</option>
          </select>
        </div>
      </div>
      
      <div class="flex flex-wrap gap-4 mt-4">
        <label class="label cursor-pointer">
          <input 
            v-model="localTableConfig.bordered"
            type="checkbox" 
            class="checkbox checkbox-sm"
          />
          <span class="label-text ml-2">显示边框</span>
        </label>
        
        <label class="label cursor-pointer">
          <input 
            v-model="localTableConfig.striped"
            type="checkbox" 
            class="checkbox checkbox-sm"
          />
          <span class="label-text ml-2">斑马纹</span>
        </label>
        
        <label class="label cursor-pointer">
          <input 
            v-model="localTableConfig.features.selection"
            type="checkbox" 
            class="checkbox checkbox-sm"
          />
          <span class="label-text ml-2">行选择</span>
        </label>
        
        <label class="label cursor-pointer">
          <input 
            v-model="localTableConfig.features.export"
            type="checkbox" 
            class="checkbox checkbox-sm"
          />
          <span class="label-text ml-2">导出功能</span>
        </label>
        
        <label class="label cursor-pointer">
          <input 
            v-model="localTableConfig.features.refresh"
            type="checkbox" 
            class="checkbox checkbox-sm"
          />
          <span class="label-text ml-2">刷新按钮</span>
        </label>
        

      </div>
    </div>

    <!-- 列配置 -->
    <div class="card bg-base-200 p-4">
      <div class="flex items-center justify-between mb-4">
        <h4 class="font-semibold flex items-center gap-2">
          <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2h2a2 2 0 002-2z"/>
          </svg>
          表格列配置
          <span class="badge badge-primary badge-sm">{{ enabledColumnsCount }}</span>
        </h4>
        
        <button 
          class="btn btn-outline btn-sm"
          @click="addColumn"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          添加列
        </button>
      </div>
      
      <div v-if="localTableConfig.columns.length === 0" class="text-center py-8">
        <svg class="w-12 h-12 mx-auto text-base-content/30 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2h2a2 2 0 002-2z"/>
        </svg>
        <p class="text-base-content/60">暂无表格列，点击"添加列"开始配置</p>
      </div>

      <div v-else class="space-y-3">
        <Draggable 
          v-model="localTableConfig.columns" 
          item-key="id"
          handle=".drag-handle"
          @end="onDragEnd"
        >
          <template #item="{ element: column, index }">
            <div 
              class="bg-base-100 rounded-lg p-4 border"
              :class="{ 'border-primary': column.enabled, 'border-base-300 opacity-60': !column.enabled }"
            >
              <div class="flex items-center gap-4">
                <!-- 拖拽手柄 -->
                <div class="drag-handle cursor-move text-base-content/40 hover:text-primary">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"/>
                  </svg>
                </div>

                <!-- 启用开关 -->
                <input 
                  v-model="column.enabled"
                  type="checkbox" 
                  class="toggle toggle-primary toggle-sm"
                />

                <!-- 列信息 -->
                <div class="flex-1 grid grid-cols-1 md:grid-cols-5 gap-4">
                  <div class="form-control">
                    <label class="label label-text-alt">字段名</label>
                    <input 
                      v-model="column.key"
                      type="text" 
                      class="input input-bordered input-sm"
                      placeholder="fieldKey"
                    />
                  </div>
                  
                  <div class="form-control">
                    <label class="label label-text-alt">列标题</label>
                    <input 
                      v-model="column.label"
                      type="text" 
                      class="input input-bordered input-sm"
                      placeholder="列标题"
                    />
                  </div>
                  
                  <div class="form-control">
                    <label class="label label-text-alt">数据类型</label>
                    <select 
                      v-model="column.type"
                      class="select select-bordered select-sm"
                    >
                      <option value="text">文本</option>
                      <option value="number">数字</option>
                      <option value="date">日期</option>
                      <option value="datetime">日期时间</option>
                      <option value="enum">枚举</option>
                      <option value="boolean">布尔值</option>
                    </select>
                  </div>
                  
                  <div class="form-control">
                    <label class="label label-text-alt">宽度(px)</label>
                    <input 
                      v-model.number="column.width"
                      type="number" 
                      class="input input-bordered input-sm"
                      placeholder="120"
                      min="50"
                    />
                  </div>
                  
                  <div class="form-control">
                    <label class="label label-text-alt">对齐方式</label>
                    <select 
                      v-model="column.align"
                      class="select select-bordered select-sm"
                    >
                      <option value="left">左对齐</option>
                      <option value="center">居中</option>
                      <option value="right">右对齐</option>
                    </select>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex gap-2">
                  <button 
                    class="btn btn-ghost btn-sm"
                    @click="editColumn(index)"
                    title="编辑"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                    </svg>
                  </button>
                  
                  <button 
                    class="btn btn-ghost btn-sm text-error"
                    @click="removeColumn(index)"
                    title="删除"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                    </svg>
                  </button>
                </div>
              </div>

              <!-- 功能选项 -->
              <div class="mt-4 pt-4 border-t flex flex-wrap gap-4">
                <label class="label cursor-pointer">
                  <input 
                    v-model="column.sortable"
                    type="checkbox" 
                    class="checkbox checkbox-sm"
                  />
                  <span class="label-text ml-2">可排序</span>
                </label>
                
                <label class="label cursor-pointer">
                  <input 
                    v-model="column.filterable"
                    type="checkbox" 
                    class="checkbox checkbox-sm"
                  />
                  <span class="label-text ml-2">可筛选</span>
                </label>
                
                <label class="label cursor-pointer">
                  <input 
                    v-model="column.resizable"
                    type="checkbox" 
                    class="checkbox checkbox-sm"
                  />
                  <span class="label-text ml-2">可调整大小</span>
                </label>
              </div>
            </div>
          </template>
        </Draggable>
      </div>
    </div>

    <!-- 分页配置 -->
    <div class="card bg-base-200 p-4">
      <h4 class="font-semibold mb-4">分页配置</h4>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="form-control">
          <label class="label">
            <span class="label-text">页码选项</span>
          </label>
          <input 
            v-model="pageSizeOptionsText"
            type="text" 
            class="input input-bordered"
            placeholder="10,20,50,100"
            @blur="updatePageSizeOptions"
          />
        </div>
        
        <div class="flex flex-wrap gap-4 items-center">
          <label class="label cursor-pointer">
            <input 
              v-model="localTableConfig.pagination.showSizeChanger"
              type="checkbox" 
              class="checkbox checkbox-sm"
            />
            <span class="label-text ml-2">显示页码选择器</span>
          </label>
          
          <label class="label cursor-pointer">
            <input 
              v-model="localTableConfig.pagination.showQuickJumper"
              type="checkbox" 
              class="checkbox checkbox-sm"
            />
            <span class="label-text ml-2">快速跳转</span>
          </label>
          
          <label class="label cursor-pointer">
            <input 
              v-model="localTableConfig.pagination.showTotal"
              type="checkbox" 
              class="checkbox checkbox-sm"
            />
            <span class="label-text ml-2">显示总数</span>
          </label>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import Draggable from 'vuedraggable'
import type { TableConfig, ColumnConfig } from '@/types/lowcode'
import { generateUniqueId } from '@/utils/lowcode'

// Props
const props = defineProps<{
  modelValue: TableConfig
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: TableConfig]
}>()

// 本地状态
const localTableConfig = ref<TableConfig>({ ...props.modelValue })
const pageSizeOptionsText = ref('')

// 计算属性
const enabledColumnsCount = computed(() => {
  return localTableConfig.value.columns.filter(column => column.enabled).length
})

// 监听变化
watch(localTableConfig, (newValue) => {
  emit('update:modelValue', newValue)
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  // 避免递归更新：只有当值真正不同时才更新
  if (JSON.stringify(newValue) !== JSON.stringify(localTableConfig.value)) {
    localTableConfig.value = { ...newValue }
    pageSizeOptionsText.value = newValue.pagination.pageSizeOptions.join(',')
  }
}, { deep: true, immediate: true })

// 方法
const addColumn = () => {
  const newColumn: ColumnConfig = {
    id: generateUniqueId('column'),
    key: '',
    label: '',
    type: 'text',
    width: 120,
    sortable: false,
    filterable: false,
    resizable: true,
    enabled: true,
    order: localTableConfig.value.columns.length,
    align: 'left'
  }
  
  localTableConfig.value.columns.push(newColumn)
}

const removeColumn = (index: number) => {
  if (confirm('确定要删除这个列吗？')) {
    localTableConfig.value.columns.splice(index, 1)
    updateOrder()
  }
}

const editColumn = (index: number) => {
  // 这里可以打开列编辑模态框
  console.log('编辑列:', index)
}

const onDragEnd = () => {
  updateOrder()
}

const updateOrder = () => {
  localTableConfig.value.columns.forEach((column, index) => {
    column.order = index
  })
}

const updatePageSizeOptions = () => {
  if (pageSizeOptionsText.value) {
    const options = pageSizeOptionsText.value
      .split(',')
      .map(item => parseInt(item.trim()))
      .filter(item => !isNaN(item) && item > 0)
    
    if (options.length > 0) {
      localTableConfig.value.pagination.pageSizeOptions = options
    }
  }
}
</script>
