<template>
  <div class="space-y-6">
    <!-- 导出基础配置 -->
    <div class="card bg-base-200 p-4">
      <h4 class="font-semibold mb-4 flex items-center gap-2">
        <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
        </svg>
        导出基础配置
      </h4>
      
      <div class="space-y-4">
        <div class="form-control">
          <label class="label cursor-pointer">
            <span class="label-text">启用导出功能</span>
            <input 
              v-model="localExportConfig.enabled"
              type="checkbox" 
              class="toggle toggle-primary"
            />
          </label>
        </div>

        <div v-if="localExportConfig.enabled" class="space-y-4">
          <!-- 支持的导出格式（固定显示） -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">支持的导出格式</span>
            </label>
            <div class="flex flex-wrap gap-4">
              <div class="badge badge-primary">Excel (.xlsx)</div>
              <div class="badge badge-primary">CSV (.csv)</div>
              <div class="badge badge-primary">PDF (.pdf)</div>
            </div>
            <div class="label">
              <span class="label-text-alt">固定支持三种格式，无需配置</span>
            </div>
          </div>

          <!-- 导出接口配置 -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">导出接口地址 *</span>
            </label>
            <input
              v-model="apiConfig.url"
              type="text"
              class="input input-bordered"
              placeholder="/api/export"
              required
            />
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">请求方式</span>
            </label>
            <select
              v-model="apiConfig.method"
              class="select select-bordered"
            >
              <option value="GET">GET</option>
              <option value="POST">POST</option>
            </select>
          </div>

          <!-- 请求头配置 -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">请求头配置</span>
              <span class="label-text-alt">可选，格式：key=value，每行一个</span>
            </label>
            <textarea
              v-model="headersText"
              class="textarea textarea-bordered"
              placeholder="Content-Type=application/json&#10;Authorization=Bearer token"
              rows="3"
              @blur="parseHeaders"
            ></textarea>
          </div>

          <!-- 文件名模板 -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">文件名模板</span>
              <span class="label-text-alt">支持变量: {name}, {timestamp}, {date}</span>
            </label>
            <input 
              v-model="localExportConfig.filename"
              type="text" 
              class="input input-bordered"
              placeholder="{name}_{timestamp}"
            />
            <div class="label">
              <span class="label-text-alt">预览: {{ filenamePreview }}</span>
            </div>
          </div>


        </div>
      </div>
    </div>

    <!-- 导出列配置 -->
    <div v-if="localExportConfig.enabled" class="card bg-base-200 p-4">
      <h4 class="font-semibold mb-4">导出列配置</h4>
      
      <div class="space-y-3">
        <div class="flex items-center justify-between">
          <span class="text-sm">选择要导出的列</span>
          <div class="flex gap-2">
            <button 
              class="btn btn-outline btn-xs"
              @click="selectAllColumns"
            >
              全选
            </button>
            <button 
              class="btn btn-outline btn-xs"
              @click="clearAllColumns"
            >
              清空
            </button>
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
          <label 
            v-for="column in availableColumns" 
            :key="column.id"
            class="label cursor-pointer justify-start"
          >
            <input 
              v-model="localExportConfig.columns"
              type="checkbox" 
              class="checkbox checkbox-sm"
              :value="column.id"
            />
            <span class="label-text ml-2">{{ column.label }}</span>
          </label>
        </div>
      </div>
    </div>



    <!-- 导出预览 -->
    <div v-if="localExportConfig.enabled" class="card bg-base-200 p-4">
      <h4 class="font-semibold mb-4">导出预览</h4>
      
      <div class="space-y-2">
        <div class="text-sm">
          <span class="font-medium">导出格式:</span>
          <span class="ml-2">xlsx, csv, pdf</span>
        </div>

        <div class="text-sm">
          <span class="font-medium">导出接口:</span>
          <span class="ml-2">{{ localExportConfig.api?.method || 'POST' }} {{ localExportConfig.api?.url || '/api/export' }}</span>
        </div>

        <div class="text-sm">
          <span class="font-medium">导出列数:</span>
          <span class="ml-2">{{ localExportConfig.columns.length }} 列</span>
        </div>

        <div class="text-sm">
          <span class="font-medium">文件名:</span>
          <span class="ml-2">{{ filenamePreview }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { ExportConfig, ColumnConfig } from '@/types/lowcode'
import { formatDate } from '@/utils/lowcode'

// Props
const props = defineProps<{
  modelValue: ExportConfig
  availableColumns?: ColumnConfig[]
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: ExportConfig]
}>()

// 本地状态
const localExportConfig = ref<ExportConfig>({ ...props.modelValue })
const headersText = ref('')

// API配置
const apiConfig = computed({
  get: () => localExportConfig.value.api || { url: '/api/export', method: 'POST' },
  set: (value) => {
    localExportConfig.value.api = value
  }
})



// 计算属性
const availableColumns = computed(() => {
  return props.availableColumns || []
})

const filenamePreview = computed(() => {
  const now = new Date()
  const timestamp = formatDate(now, 'YYYYMMDDHHmmss')
  const date = formatDate(now, 'YYYY-MM-DD')
  
  return localExportConfig.value.filename
    .replace('{name}', '示例配置')
    .replace('{timestamp}', timestamp)
    .replace('{date}', date)
})

// 监听变化
watch(localExportConfig, (newValue) => {
  emit('update:modelValue', newValue)
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  // 避免递归更新：只有当值真正不同时才更新
  if (JSON.stringify(newValue) !== JSON.stringify(localExportConfig.value)) {
    localExportConfig.value = { ...newValue }
  }
}, { deep: true })



// 监听API配置变化
watch(apiConfig, (newValue) => {
  localExportConfig.value.api = newValue
}, { deep: true })

// 初始化请求头文本
watch(() => props.modelValue, (newValue) => {
  if (newValue.api?.headers) {
    headersText.value = formatHeadersToText(newValue.api.headers)
  }
}, { immediate: true })

// 方法
const selectAllColumns = () => {
  localExportConfig.value.columns = availableColumns.value.map(col => col.id)
}

const clearAllColumns = () => {
  localExportConfig.value.columns = []
}

// 解析请求头文本
const parseHeaders = () => {
  const headers: Record<string, string> = {}
  if (headersText.value.trim()) {
    const lines = headersText.value.split('\n')
    lines.forEach(line => {
      const trimmedLine = line.trim()
      if (trimmedLine && trimmedLine.includes('=')) {
        const [key, ...valueParts] = trimmedLine.split('=')
        const value = valueParts.join('=') // 处理值中包含=的情况
        if (key.trim() && value.trim()) {
          headers[key.trim()] = value.trim()
        }
      }
    })
  }
  apiConfig.value = { ...apiConfig.value, headers }
}

// 格式化请求头为文本
const formatHeadersToText = (headers?: Record<string, string>) => {
  if (!headers || Object.keys(headers).length === 0) return ''
  return Object.entries(headers)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n')
}
</script>
