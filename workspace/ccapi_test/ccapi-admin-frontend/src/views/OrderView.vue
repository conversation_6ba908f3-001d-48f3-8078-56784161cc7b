<template>
  <div class="h-full flex flex-col p-4">
    <!-- 顶部筛选条件 -->
    <div class="bg-base-100 p-3 rounded-lg shadow-sm flex-shrink-0">
      <div class="flex gap-3 items-center flex-wrap">
        <!-- 订单号 -->
        <input
          v-model="searchParams.orderNo"
          type="text"
          placeholder="请输入订单号"
          class="input input-sm input-bordered w-40"
        />

        <!-- 日期范围 -->
        <div class="flex gap-1 items-center">
          <input
            v-model="searchParams.createdTimeBegin"
            type="date"
            class="input input-sm input-bordered w-36"
          />
          <span class="text-xs text-base-content/60">—</span>
          <input
            v-model="searchParams.createdTimeEnd"
            type="date"
            class="input input-sm input-bordered w-36"
          />
        </div>

        <!-- 状态 -->
        <select v-model="searchParams.orderStatus" class="select select-sm select-bordered w-24">
          <option value="-1">全部</option>
          <option value="0">待确认</option>
          <option value="1">已确认</option>
          <option value="2">已取消</option>
          <option value="3">已完成</option>
          <option value="4">已退款</option>
        </select>

        <!-- 路线名称 -->
        <input
          v-model="searchParams.routeName"
          type="text"
          placeholder="路线名称"
          class="input input-sm input-bordered w-32"
        />

        <!-- 车辆 -->
        <select
          v-model="searchParams.vehicleId"
          class="select select-sm select-bordered w-24"
          :disabled="deviceLoading"
        >
          <option value="-1">车辆</option>
          <option v-if="deviceLoading" disabled>加载中...</option>
          <option
            v-for="device in deviceList"
            :key="device.id"
            :value="device.id"
          >
            {{ device.name || device.imsi }}
          </option>
        </select>

        <!-- 起点 -->
        <input
          v-model="searchParams.begin"
          type="text"
          placeholder="起点站"
          class="input input-sm input-bordered w-24"
        />

        <!-- 终点 -->
        <input
          v-model="searchParams.end"
          type="text"
          placeholder="终点站"
          class="input input-sm input-bordered w-24"
        />

        <!-- 操作按钮 -->
        <div class="flex gap-2 ml-auto">
          <button @click="handleSearch" class="btn btn-primary btn-sm">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            查询
          </button>
          <button @click="handleReset" class="btn btn-ghost btn-sm">重置</button>
          <div class="dropdown dropdown-end">
            <div tabindex="0" role="button" class="btn btn-secondary btn-sm">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              导出数据
            </div>
            <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-40">
              <li><a @click="handleExport('excel')">Excel</a></li>
              <li><a @click="handleExport('csv')">CSV</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div v-if="orderStats" class="bg-base-100 p-2 rounded-lg shadow-sm flex-shrink-0">
      <div class="flex items-center gap-8 text-sm">
        <span>订单总数: <strong class="text-primary">{{ orderStats.orderCount }}</strong></span>
        <span>乘客总数: <strong class="text-secondary">{{ orderStats.passengerCount }}</strong></span>
        <span>已核销: <strong class="text-success">{{ (orderStats.verifiedFee / 100).toFixed(2) }}元</strong></span>
        <span>待核销: <strong class="text-warning">{{ (orderStats.pendingVerificationFee / 100).toFixed(2) }}元</strong></span>
        <span>已退款: <strong class="text-error">{{ (orderStats.refundedFee / 100).toFixed(2) }}元</strong></span>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="bg-base-100 rounded-lg shadow-sm flex-1 flex flex-col min-h-0">
      <!-- 表格头部 -->
      <div class="overflow-auto flex-1">
        <table class="table table-sm table-pin-rows w-full" style="min-width: 1800px;">
          <thead>
            <tr class="bg-base-200">
              <th style="width: 60px; min-width: 60px;">编号</th>
              <th style="width: 180px; min-width: 180px;">订单号</th>
              <th style="width: 120px; min-width: 120px;">路线名称</th>
              <th style="width: 80px; min-width: 80px;">起点</th>
              <th style="width: 80px; min-width: 80px;">终点</th>
              <th style="width: 80px; min-width: 80px;">乘客</th>
              <th style="width: 60px; min-width: 60px;">人数</th>
              <th style="width: 100px; min-width: 100px;">车票名称</th>
              <th style="width: 80px; min-width: 80px;">车票价格</th>
              <th style="width: 100px; min-width: 100px;">车辆</th>
              <th style="width: 140px; min-width: 140px;">VIN码</th>
              <th style="width: 80px; min-width: 80px;">订单状态</th>
              <th style="width: 80px; min-width: 80px;">总价</th>
              <th style="width: 100px; min-width: 100px;">优惠券编号</th>
              <th style="width: 80px; min-width: 80px;">优惠金额</th>
              <th style="width: 80px; min-width: 80px;">实付</th>
              <th style="width: 140px; min-width: 140px;">预约车次</th>
              <th style="width: 140px; min-width: 140px;">创建时间</th>
              <th style="width: 140px; min-width: 140px;">付款时间</th>
              <th style="width: 140px; min-width: 140px;">取消时间</th>
              <th style="width: 80px; min-width: 80px;">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="loading" class="text-center">
              <td colspan="21" class="py-8">
                <span class="loading loading-spinner loading-md"></span>
                <span class="ml-2">加载中...</span>
              </td>
            </tr>
            <tr v-else-if="orderList.length === 0" class="text-center">
              <td colspan="21" class="py-8 text-base-content/60">暂无数据</td>
            </tr>
            <tr v-else v-for="(item, index) in orderList" :key="item.orderNo" class="hover">
              <td>{{ (pagination.page - 1) * pagination.pageSize + index + 1 }}</td>
              <td class="font-mono text-xs">{{ item.orderNo }}</td>
              <td>{{ item.routeName }}</td>
              <td>{{ item.begin }}</td>
              <td>{{ item.end }}</td>
              <td>{{ item.passengerName || '--' }}</td>
              <td class="text-center">{{ item.passengerCount }}</td>
              <td>{{ item.ticketName || '--' }}</td>
              <td class="text-right">¥{{ ((item.ticketPrice || 0) / 100).toFixed(2) }}</td>
              <td>{{ item.vehicleName || '--' }}</td>
              <td class="font-mono text-xs">{{ item.vinCode || '--' }}</td>
              <td>
                <div class="badge badge-sm" :class="getStatusBadgeClass(item.status)">
                  {{ getStatusText(item.status) }}
                </div>
              </td>
              <td class="text-right">¥{{ (item.totalFee / 100).toFixed(2) }}</td>
              <td>{{ item.couponCode || '--' }}</td>
              <td class="text-right">¥{{ ((item.discountAmount || 0) / 100).toFixed(2) }}</td>
              <td class="text-right">¥{{ ((item.actualAmount || item.totalFee) / 100).toFixed(2) }}</td>
              <td>{{ item.reservedTime || '--' }}</td>
              <td class="text-xs">{{ formatTime(item.createdTime) }}</td>
              <td class="text-xs">{{ item.paymentTime ? formatTime(item.paymentTime) : '--' }}</td>
              <td class="text-xs">{{ item.cancelTime ? formatTime(item.cancelTime) : '--' }}</td>
              <td>
                <div class="flex gap-1">
                  <button class="btn btn-ghost btn-xs" @click="viewOrder(item)">详情</button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="flex justify-between items-center p-4 border-t">
        <div class="text-sm text-base-content/60">
          共 {{ pagination.total }} 条记录，第 {{ pagination.page }} / {{ Math.ceil(pagination.total / pagination.pageSize) }} 页
        </div>
        <div class="join">
          <button
            class="join-item btn btn-sm"
            :disabled="pagination.page <= 1"
            @click="handlePageChange(pagination.page - 1)"
          >
            «
          </button>
          <button
            v-for="page in getPageNumbers()"
            :key="page"
            class="join-item btn btn-sm"
            :class="{ 'btn-active': page === pagination.page }"
            @click="handlePageChange(page)"
          >
            {{ page }}
          </button>
          <button
            class="join-item btn btn-sm"
            :disabled="pagination.page >= Math.ceil(pagination.total / pagination.pageSize)"
            @click="handlePageChange(pagination.page + 1)"
          >
            »
          </button>
        </div>
        <div class="flex items-center gap-2">
          <span class="text-sm">每页</span>
          <select
            v-model="pagination.pageSize"
            @change="handlePageSizeChange(pagination.pageSize)"
            class="select select-sm select-bordered"
          >
            <option value="20">20</option>
            <option value="50">50</option>
            <option value="100">100</option>
          </select>
          <span class="text-sm">条</span>
        </div>
      </div>
    </div>

    <!-- 导出进度提示 -->
    <div v-if="exporting" class="toast toast-top toast-end">
      <div class="alert alert-info">
        <span class="loading loading-spinner"></span>
        正在导出数据，请稍候...
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import dayjs from 'dayjs'
import * as orderApi from '@/api/order'
import * as deviceApi from '@/api/device'
import type {
  OrderListQuery,
  OrderResult,
  OrderPageResult
} from '@/types'
import type { Device } from '@/api/device'

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const orderList = ref<OrderResult[]>([])
const orderStats = ref<OrderPageResult | null>(null)
const deviceList = ref<Device[]>([])
const deviceLoading = ref(false)

// 搜索参数
const searchParams = reactive<OrderListQuery>({
  orderNo: '',
  routeName: '',
  begin: '',
  end: '',
  orderStatus: -1,
  createdTimeBegin: undefined,
  createdTimeEnd: undefined,
  vehicleId: -1,
  sortField: 'createdTime',
  sortOrder: 'desc',
  page: 1,
  pageSize: 20
})

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 辅助函数
const getStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: '待确认',
    1: '已确认',
    2: '已取消',
    3: '已完成',
    4: '已退款'
  }
  return statusMap[status] || '未知'
}

const getStatusBadgeClass = (status: number): string => {
  const classMap: Record<number, string> = {
    0: 'badge-warning',
    1: 'badge-success',
    2: 'badge-error',
    3: 'badge-info',
    4: 'badge-ghost'
  }
  return classMap[status] || 'badge-ghost'
}

const formatTime = (timestamp: number): string => {
  return dayjs(timestamp).format('MM-DD HH:mm')
}

const getPageNumbers = (): number[] => {
  const total = Math.ceil(pagination.total / pagination.pageSize)
  const current = pagination.page
  const pages: number[] = []

  // 显示当前页前后2页
  const start = Math.max(1, current - 2)
  const end = Math.min(total, current + 2)

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
}

// 获取设备列表
const fetchDeviceList = async () => {
  deviceLoading.value = true
  try {
    console.log('获取设备列表...')
    const devices = await deviceApi.getAllDevices()
    console.log('设备列表响应:', devices)
    deviceList.value = devices
  } catch (error: any) {
    console.error('获取设备列表失败:', error)
    deviceList.value = []
  } finally {
    deviceLoading.value = false
  }
}

// 获取订单列表
const fetchOrderList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchParams,
      page: pagination.page,
      pageSize: pagination.pageSize
    }

    // 过滤空值
    Object.keys(params).forEach(key => {
      const value = params[key as keyof typeof params]
      if (value === '' || value === -1 || value === undefined) {
        delete params[key as keyof typeof params]
      }
    })

    console.log('获取订单列表参数:', params)
    const response = await orderApi.getOrderList(params)
    console.log('订单列表响应:', response)

    orderList.value = response.data || []
    orderStats.value = response
    pagination.total = response.orderCount || 0
  } catch (error: any) {
    console.error('获取订单列表失败:', error)
    // 设置默认值避免错误
    orderList.value = []
    orderStats.value = null
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  fetchOrderList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchParams, {
    orderNo: '',
    routeName: '',
    begin: '',
    end: '',
    orderStatus: -1,
    createdTimeBegin: undefined,
    createdTimeEnd: undefined,
    vehicleId: -1,
    sortField: 'createdTime',
    sortOrder: 'desc'
  })
  pagination.page = 1
  fetchOrderList()
}

// 分页处理
const handlePageChange = (page: number) => {
  pagination.page = page
  fetchOrderList()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  fetchOrderList()
}

// 导出处理
const handleExport = async (fileType: 'excel' | 'csv') => {
  exporting.value = true
  try {
    const exportData = { ...searchParams, fileType }

    // 过滤空值
    Object.keys(exportData).forEach(key => {
      const value = exportData[key as keyof typeof exportData]
      if (value === '' || value === -1 || value === undefined) {
        delete exportData[key as keyof typeof exportData]
      }
    })

    console.log('导出数据参数:', exportData)
    const response = await orderApi.exportOrders(exportData)

    // 创建下载链接
    const url = window.URL.createObjectURL(response.data)
    const link = document.createElement('a')
    link.href = url
    link.download = response.filename || `orders_${dayjs().format('YYYY-MM-DD')}.${fileType}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    console.log('导出成功')
  } catch (error: any) {
    console.error('导出失败:', error)
    alert('导出失败: ' + (error.message || '未知错误'))
  } finally {
    exporting.value = false
  }
}

// 查看订单
const viewOrder = (order: OrderResult) => {
  console.log('查看订单:', order)
  // 这里可以打开订单详情弹窗或跳转到详情页
}

// 编辑订单
const editOrder = (order: OrderResult) => {
  console.log('编辑订单:', order)
  // 这里可以打开编辑弹窗或跳转到编辑页
}

// 组件挂载时获取数据
onMounted(() => {
  fetchDeviceList()  // 获取设备列表
  fetchOrderList()   // 获取订单列表
})
</script>

<style scoped>
/* 增加表格行间距 */
.table tbody tr td {
  padding-top: 0.28rem;
  padding-bottom: 0.28rem;
}

.table thead tr th {
  padding-top: 0.27rem;
  padding-bottom: 0.27rem;
}
</style>
