<template>
  <div class="container mx-auto p-6">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">任务执行管理</h1>
      
      <!-- 导出按钮 -->
      <div class="dropdown dropdown-end">
        <div tabindex="0" role="button" class="btn btn-primary">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          导出数据
        </div>
        <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
          <li><a @click="handleExport('excel')">导出为 Excel</a></li>
          <li><a @click="handleExport('pdf')">导出为 PDF</a></li>
          <li><a @click="handleExport('csv')">导出为 CSV</a></li>
        </ul>
      </div>
    </div>

    <!-- 搜索表单 -->
    <SearchForm
      v-model="searchParams"
      :fields="searchFields"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <div class="card bg-base-100 shadow-sm">
      <div class="card-body">
        <DataTable
          :columns="tableColumns"
          :data="taskList"
          :loading="loading"
          :sort-field="searchParams.sortField"
          :sort-order="searchParams.sortOrder"
          @sort="handleSort"
        >
          <template #actions="{ item }">
            <div class="flex gap-2">
              <button class="btn btn-ghost btn-xs" @click="viewTask(item)">
                查看
              </button>
              <button class="btn btn-ghost btn-xs" @click="editTask(item)">
                编辑
              </button>
            </div>
          </template>
        </DataTable>

        <!-- 分页 -->
        <Pagination
          :current-page="pagination.page"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 导出进度提示 -->
    <div v-if="exporting" class="toast toast-top toast-end">
      <div class="alert alert-info">
        <span class="loading loading-spinner"></span>
        正在导出数据，请稍候...
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import dayjs from 'dayjs'
import SearchForm from '@/components/SearchForm.vue'
import DataTable from '@/components/DataTable.vue'
import Pagination from '@/components/Pagination.vue'
import * as taskApi from '@/api/task'
import type {
  TaskExecutionListQuery,
  TaskExecutionResult,
  TaskExecutionPageResult,
  ExecutionStatus,
  ExecutionStatusLabels,
  TaskType,
  TableColumn,
  FormField,
  ExportField
} from '@/types'

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const taskList = ref<TaskExecutionResult[]>([])

// 搜索参数
const searchParams = reactive<TaskExecutionListQuery>({
  taskName: '',
  imsi: '',
  vehiclePlate: '',
  creator: '',
  taskType: -1,
  status: -1,
  taskId: -1,
  childId: -1,
  departureTimeBegin: undefined,
  departureTimeEnd: undefined,
  endTimeBegin: undefined,
  endTimeEnd: undefined,
  createdTimeBegin: undefined,
  createdTimeEnd: undefined,
  sortField: 'createdTime',
  sortOrder: 'desc',
  page: 1,
  pageSize: 20
})

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 搜索表单字段配置
const searchFields: FormField[] = [
  {
    key: 'taskName',
    label: '任务名称',
    type: 'input',
    placeholder: '请输入任务名称'
  },
  {
    key: 'imsi',
    label: 'IMSI',
    type: 'input',
    placeholder: '请输入IMSI'
  },
  {
    key: 'vehiclePlate',
    label: '车牌号',
    type: 'input',
    placeholder: '请输入车牌号'
  },
  {
    key: 'creator',
    label: '创建者',
    type: 'input',
    placeholder: '请输入创建者'
  },
  {
    key: 'taskType',
    label: '任务类型',
    type: 'select',
    placeholder: '请选择任务类型',
    options: [
      { label: '全部', value: -1 },
      { label: '路线任务', value: 1 },
      { label: '维护任务', value: 2 },
      { label: '检查任务', value: 3 }
    ]
  },
  {
    key: 'status',
    label: '执行状态',
    type: 'select',
    placeholder: '请选择执行状态',
    options: [
      { label: '全部', value: -1 },
      { label: '不执行', value: 0 },
      { label: '将要执行', value: 1 },
      { label: '正在执行', value: 2 },
      { label: '已完成', value: 3 },
      { label: '已暂停', value: 4 },
      { label: '已取消', value: 5 },
      { label: '已终止', value: 6 },
      { label: '无法完成', value: 7 }
    ]
  },
  {
    key: 'taskId',
    label: '任务ID',
    type: 'number',
    placeholder: '请输入任务ID'
  },
  {
    key: 'childId',
    label: '子任务ID',
    type: 'number',
    placeholder: '请输入子任务ID'
  },
  {
    key: 'departureTime',
    label: '出发时间',
    type: 'daterange'
  },
  {
    key: 'endTime',
    label: '结束时间',
    type: 'daterange'
  },
  {
    key: 'createdTime',
    label: '创建时间',
    type: 'daterange'
  }
]

// 表格列配置
const tableColumns: TableColumn[] = [
  {
    key: 'executionNo',
    title: '执行编号',
    width: 120,
    sortable: true
  },
  {
    key: 'taskName',
    title: '任务名称',
    width: 150
  },
  {
    key: 'taskType',
    title: '任务类型',
    width: 100,
    render: (value: number) => {
      const typeMap: Record<number, string> = {
        1: '路线任务',
        2: '维护任务',
        3: '检查任务'
      }
      return typeMap[value] || '未知'
    }
  },
  {
    key: 'executionStatus',
    title: '执行状态',
    width: 100,
    render: (value: ExecutionStatus) => {
      return ExecutionStatusLabels[value] || '未知'
    }
  },
  {
    key: 'imsi',
    title: 'IMSI',
    width: 150
  },
  {
    key: 'vehiclePlate',
    title: '车牌号',
    width: 100
  },
  {
    key: 'actualSpeedKmh',
    title: '平均速度',
    width: 100,
    render: (value: number) => value ? `${value.toFixed(2)} km/h` : '-'
  },
  {
    key: 'creator',
    title: '创建者',
    width: 100
  },
  {
    key: 'departureTime',
    title: '出发时间',
    width: 160,
    sortable: true,
    render: (value: number) => value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-'
  },
  {
    key: 'endTime',
    title: '结束时间',
    width: 160,
    sortable: true,
    render: (value: number) => value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-'
  },
  {
    key: 'createdTime',
    title: '创建时间',
    width: 160,
    sortable: true,
    render: (value: number) => dayjs(value).format('YYYY-MM-DD HH:mm:ss')
  }
]

// 导出字段配置
const exportFields: ExportField[] = [
  { field: 'id', label: 'ID', width: 80 },
  { field: 'taskId', label: '任务ID', width: 80 },
  { field: 'childId', label: '子任务ID', width: 80 },
  { field: 'executionNo', label: '执行编号', width: 120 },
  { field: 'taskName', label: '任务名称', width: 150 },
  { field: 'taskType', label: '任务类型', width: 100 },
  { field: 'executionStatus', label: '执行状态', width: 100 },
  { field: 'status', label: '状态', width: 80 },
  { field: 'imsi', label: 'IMSI', width: 150 },
  { field: 'vehiclePlate', label: '车牌号', width: 100 },
  { field: 'actualSpeedKmh', label: '平均速度', width: 100 },
  { field: 'creator', label: '创建者', width: 100 },
  { field: 'departureTime', label: '出发时间', width: 160 },
  { field: 'endTime', label: '结束时间', width: 160 },
  { field: 'createdTime', label: '创建时间', width: 160 }
]

// 获取任务执行列表
const fetchTaskList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchParams,
      page: pagination.page,
      pageSize: pagination.pageSize
    }
    
    // 过滤空值
    Object.keys(params).forEach(key => {
      const value = params[key as keyof typeof params]
      if (value === '' || value === -1 || value === undefined) {
        delete params[key as keyof typeof params]
      }
    })
    
    const response = await taskApi.getTaskExecutionList(params)
    taskList.value = response.data
    pagination.total = response.total
  } catch (error: any) {
    console.error('获取任务执行列表失败:', error)
    // 这里可以添加错误提示
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = (searchData: Record<string, any>) => {
  Object.assign(searchParams, searchData)
  pagination.page = 1
  fetchTaskList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchParams, {
    taskName: '',
    imsi: '',
    vehiclePlate: '',
    creator: '',
    taskType: -1,
    status: -1,
    taskId: -1,
    childId: -1,
    departureTimeBegin: undefined,
    departureTimeEnd: undefined,
    endTimeBegin: undefined,
    endTimeEnd: undefined,
    createdTimeBegin: undefined,
    createdTimeEnd: undefined,
    sortField: 'createdTime',
    sortOrder: 'desc'
  })
  pagination.page = 1
  fetchTaskList()
}

// 排序处理
const handleSort = (field: string, order: 'asc' | 'desc') => {
  searchParams.sortField = field
  searchParams.sortOrder = order
  fetchTaskList()
}

// 分页处理
const handlePageChange = (page: number) => {
  pagination.page = page
  fetchTaskList()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  fetchTaskList()
}

// 导出处理
const handleExport = async (fileType: 'excel' | 'pdf' | 'csv') => {
  exporting.value = true
  try {
    const exportData = {
      ...searchParams,
      fields: exportFields,
      fileType
    }
    
    // 过滤空值
    Object.keys(exportData).forEach(key => {
      const value = exportData[key as keyof typeof exportData]
      if (value === '' || value === -1 || value === undefined) {
        delete exportData[key as keyof typeof exportData]
      }
    })
    
    const response = await taskApi.exportTaskExecutions(exportData)
    
    // 创建下载链接
    const url = window.URL.createObjectURL(response.data)
    const link = document.createElement('a')
    link.href = url
    link.download = response.filename || `task_executions.${fileType}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error: any) {
    console.error('导出失败:', error)
    // 这里可以添加错误提示
  } finally {
    exporting.value = false
  }
}

// 查看任务
const viewTask = (task: TaskExecutionResult) => {
  console.log('查看任务:', task)
  // 这里可以打开任务详情弹窗或跳转到详情页
}

// 编辑任务
const editTask = (task: TaskExecutionResult) => {
  console.log('编辑任务:', task)
  // 这里可以打开编辑弹窗或跳转到编辑页
}

// 组件挂载时获取数据
onMounted(() => {
  fetchTaskList()
})
</script>
