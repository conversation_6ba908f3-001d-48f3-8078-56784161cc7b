<template>
  <div class="h-full flex flex-col p-4">
    <!-- 顶部工具栏 -->
    <div class="bg-base-100 p-4 rounded-lg shadow-sm flex-shrink-0 mb-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
          <h1 class="text-2xl font-bold text-primary">{{ currentConfig?.name || '低代码页面' }}</h1>
          <div v-if="currentConfig?.description" class="text-sm text-base-content/60">
            {{ currentConfig.description }}
          </div>
        </div>
        
        <div class="flex items-center gap-3">
          <select 
            v-model="selectedConfigId"
            class="select select-bordered select-sm"
            @change="loadConfig"
          >
            <option value="">选择配置</option>
            <option 
              v-for="config in availableConfigs" 
              :key="config.id"
              :value="config.id"
            >
              {{ config.name }}
            </option>
          </select>
          
          <button 
            class="btn btn-outline btn-sm"
            @click="$router.push('/lowcode')"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
            配置管理
          </button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 min-h-0 overflow-visible">
      <div v-if="!currentConfig" class="h-full flex items-center justify-center">
        <div class="text-center">
          <svg class="w-16 h-16 mx-auto text-base-content/30 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          <p class="text-base-content/60 mb-4">请选择一个配置来开始</p>
          <button 
            class="btn btn-primary"
            @click="$router.push('/lowcode')"
          >
            创建配置
          </button>
        </div>
      </div>

      <LowCodeRenderer 
        v-else
        :config="currentConfig"
        :runtime-state="runtimeState"
        @search="handleSearch"
        @export="handleExport"
        @update:runtime-state="updateRuntimeState"
      />
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="toast toast-end">
      <div class="alert alert-error">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        <span>{{ error }}</span>
        <button class="btn btn-sm btn-ghost" @click="error = null">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import type { LowCodeConfig, RuntimeState } from '@/types/lowcode'
import { useLowCodeConfig } from '@/services/lowcode'
import { generateExportFilename, downloadFile } from '@/utils/lowcode'
import LowCodeRenderer from '@/components/lowcode/LowCodeRenderer.vue'
import request from '@/utils/request'
import { dynamicParamResolver } from '@/utils/dynamic-params'


// 路由
const route = useRoute()

// 低代码配置服务
const { getConfig, configs, refreshConfigs } = useLowCodeConfig()

// 本地状态
const selectedConfigId = ref<string>('')
const currentConfig = ref<LowCodeConfig | null>(null)
const error = ref<string | null>(null)

// 运行时状态
const runtimeState = reactive<RuntimeState>({
  loading: false,
  data: [],
  total: 0,
  currentPage: 1,
  pageSize: 20,
  searchParams: {},
  selectedRows: []
})

// 计算属性
const availableConfigs = computed(() => {
  // 从实际的配置列表获取
  return configs.value.map(config => ({
    id: config.id,
    name: config.name,
    description: config.description
  }))
})

// 方法
const loadConfig = async () => {
  if (!selectedConfigId.value) {
    currentConfig.value = null
    return
  }

  try {
    // 从实际的API获取配置
    const config = await getConfig(selectedConfigId.value)
    if (config) {
      currentConfig.value = config

      // 重置运行时状态
      Object.assign(runtimeState, {
        loading: false,
        data: [],
        total: 0,
        currentPage: 1,
        pageSize: config.tableConfig.pagination.pageSize,
        searchParams: {},
        selectedRows: []
      })
    } else {
      error.value = '配置不存在'
    }

  } catch (err) {
    error.value = '加载配置失败: ' + (err as Error).message
  }
}

const handleSearch = async (params: Record<string, any>) => {
  if (!currentConfig.value) return

  runtimeState.loading = true
  runtimeState.searchParams = params

  try {
    // 解析动态参数
    let dynamicParams = {}
    if (currentConfig.value.dataSource.dynamicParams) {
      try {
        dynamicParams = await dynamicParamResolver.resolveParams(
          currentConfig.value.dataSource.dynamicParams
        )
        console.log('解析的动态参数:', dynamicParams)
      } catch (error) {
        console.error('动态参数解析失败:', error)
      }
    }

    // 构建请求参数
    const requestParams = {
      ...currentConfig.value.dataSource.params, // 静态参数（兼容旧配置）
      ...dynamicParams, // 动态参数
      ...params, // 搜索参数
      page: runtimeState.currentPage,
      page_size: runtimeState.pageSize
    }

    // 根据配置的数据源调用真实API
    const dataSource = currentConfig.value.dataSource
    let response

    if (dataSource.method === 'POST') {
      response = await request.post(dataSource.url, requestParams)
    } else {
      response = await request.get(dataSource.url, { params: requestParams })
    }

    if (response.data.success) {
      // 根据配置的响应映射提取数据
      const responseMapping = dataSource.responseMapping || { dataPath: 'data.list', totalPath: 'data.total' }

      // 提取数据列表
      const dataPath = responseMapping.dataPath || 'data.list'
      const data = getNestedValue(response.data, dataPath) || []

      // 提取总数
      const totalPath = responseMapping.totalPath || 'data.total'
      const total = getNestedValue(response.data, totalPath) || data.length

      runtimeState.data = data
      runtimeState.total = total
    } else {
      throw new Error(response.data.message || '请求失败')
    }

  } catch (err) {
    console.error('API请求失败:', err)
    error.value = '搜索失败: ' + (err as Error).message

    // 如果API调用失败，回退到模拟数据（可选）
    if (import.meta.env.DEV) {
      console.warn('API调用失败，使用模拟数据')
      const mockData = generateMockData(currentConfig.value, params)
      runtimeState.data = mockData.items
      runtimeState.total = mockData.total
    }
  } finally {
    runtimeState.loading = false
  }
}

// 工具函数
const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined
  }, obj)
}

const generateMockData = (config: LowCodeConfig, params: Record<string, any>) => {
  const total = 1247 // 模拟总数
  const items = []
  
  for (let i = 0; i < runtimeState.pageSize; i++) {
    const item: Record<string, any> = {
      id: (runtimeState.currentPage - 1) * runtimeState.pageSize + i + 1
    }
    
    // 根据配置生成模拟数据
    config.tableConfig.columns.forEach(column => {
      switch (column.type) {
        case 'text':
          item[column.key] = `${column.label}${item.id}`
          break
        case 'number':
          item[column.key] = Math.floor(Math.random() * 10000) / 100
          break
        case 'enum':
          if (column.enumOptions) {
            const values = Object.keys(column.enumOptions)
            item[column.key] = values[Math.floor(Math.random() * values.length)]
          }
          break
        case 'datetime':
          item[column.key] = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
          break
        default:
          item[column.key] = `数据${item.id}`
      }
    })
  }
  
  return { items, total }
}

const handleExport = async (format: string, data: any[]) => {
  try {
    // 这里应该调用实际的导出API
    console.log('导出数据:', { format, data })
    
    // 模拟导出
    const filename = generateExportFilename(
      currentConfig.value!.exportConfig.filename,
      currentConfig.value!
    )
    
    // 简单的CSV导出示例
    if (format === 'csv') {
      const csv = convertToCSV(data, currentConfig.value!)
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
      downloadFile(blob, filename + '.csv')
    }
    
  } catch (err) {
    error.value = '导出失败: ' + (err as Error).message
  }
}

const convertToCSV = (data: any[], config: LowCodeConfig): string => {
  const columns = config.tableConfig.columns.filter(col => col.enabled)
  const headers = columns.map(col => col.label).join(',')
  const rows = data.map(row => 
    columns.map(col => {
      const value = row[col.key] || ''
      return `"${String(value).replace(/"/g, '""')}"`
    }).join(',')
  )
  
  return [headers, ...rows].join('\n')
}

const updateRuntimeState = (updates: Partial<RuntimeState>) => {
  Object.assign(runtimeState, updates)
}

// 监听配置列表变化，自动加载第一个配置
watch(configs, (newConfigs) => {
  // 如果当前没有选中配置，且有可用配置，自动选择第一个
  if (!selectedConfigId.value && newConfigs.length > 0) {
    selectedConfigId.value = newConfigs[0].id
    loadConfig()
  }
}, { immediate: true })

// 生命周期
onMounted(async () => {
  // 首先加载配置列表
  await refreshConfigs()

  // 从路由参数获取配置ID
  const configId = route.query.config as string
  if (configId) {
    selectedConfigId.value = configId
    await loadConfig()
  }
  // 注意：如果没有路由参数指定的配置，watch 监听器会自动处理
})
</script>
