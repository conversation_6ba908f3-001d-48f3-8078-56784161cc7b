import request from '@/utils/request'
import type {
  TaskExecutionListQuery,
  TaskExecutionPageResult,
  ExportTaskExecutionRequest,
  ApiResponse,
  DownloadResponse
} from '@/types'

// 获取任务执行列表
export const getTaskExecutionList = (params: TaskExecutionListQuery): Promise<TaskExecutionPageResult> => {
  return request.get<ApiResponse<TaskExecutionPageResult>>('/task-execution/list', { params })
    .then(response => response.data.data!)
}

// 导出任务执行数据
export const exportTaskExecutions = (data: ExportTaskExecutionRequest): Promise<DownloadResponse> => {
  return request.post('/task-execution/export', data, {
    responseType: 'blob'
  }).then(response => {
    // 从响应头获取文件名
    const contentDisposition = response.headers['content-disposition']
    let filename = 'task_executions'
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1].replace(/['"]/g, '')
      }
    }
    
    // 根据文件类型设置扩展名
    if (!filename.includes('.')) {
      const extension = data.fileType === 'excel' ? '.xlsx' : 
                      data.fileType === 'pdf' ? '.pdf' : '.csv'
      filename += extension
    }
    
    return {
      data: response.data,
      filename,
      contentType: response.headers['content-type']
    }
  })
}

// 获取任务执行详情
export const getTaskExecutionDetail = (executionId: number) => {
  return request.get<ApiResponse>(`/task-execution/${executionId}`)
    .then(response => response.data.data)
}
