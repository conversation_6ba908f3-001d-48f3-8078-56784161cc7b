import request from '@/utils/request'
import type {
  OrderListQuery,
  OrderPageResult,
  ExportOrderRequest,
  ApiResponse,
  DownloadResponse
} from '@/types'

// 获取订单列表
export const getOrderList = (params: OrderListQuery): Promise<OrderPageResult> => {
  return request.get<ApiResponse<OrderPageResult>>('/operation/order_list', { params })
    .then(response => response.data.data!)
}

// 导出订单数据
export const exportOrders = (data: ExportOrderRequest): Promise<DownloadResponse> => {
  return request.post('/operation/export_order', data, {
    responseType: 'blob'
  }).then(response => {
    // 从响应头获取文件名
    const contentDisposition = response.headers['content-disposition']
    let filename = 'orders'
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1].replace(/['"]/g, '')
      }
    }
    
    // 根据文件类型设置扩展名
    if (!filename.includes('.')) {
      const extension = data.fileType === 'excel' ? '.xlsx' : 
                      data.fileType === 'pdf' ? '.pdf' : '.csv'
      filename += extension
    }
    
    return {
      data: response.data,
      filename,
      contentType: response.headers['content-type']
    }
  })
}

// 获取订单详情
export const getOrderDetail = (orderId: number) => {
  return request.get<ApiResponse>(`/operation/order/${orderId}`)
    .then(response => response.data.data)
}
