package common

import (
	"ccapi/server/redis"
	"fmt"
	"image/color"
	"math/rand"
	"time"

	"github.com/mojocn/base64Captcha"
)

const (
	RedisCaptchaKey = "captcha:%s" // 验证码key
)

// VerifyCaptcha 验证图形验证码
func VerifyCaptcha(captchaId, captchaCode string) bool {
	result := redis.Get(fmt.Sprintf(RedisCaptchaKey, captchaId))
	return result == "1"
}

// CustomMathDriver 自定义的算术验证码驱动
type CustomMathDriver struct {
	base64Captcha.DriverMath
}

// GenerateIdQuestionAnswer 重写生成算式的方法，只生成加法
func (d *CustomMathDriver) GenerateIdQuestionAnswer() (id, q, a string) {
	id = base64Captcha.RandomId()
	// 生成两个1-9的随机数
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	num1 := r.Intn(9) + 1
	num2 := r.Intn(9) + 1
	question := fmt.Sprintf("%d+%d=?", num1, num2)
	answer := fmt.Sprintf("%d", num1+num2)
	return id, question, answer
}

// NewCustomMathDriver 创建自定义算术验证码驱动
func NewCustomMathDriver(height int, width int, noiseCount int, showLineOptions int, bgColor *color.RGBA, fonts []string) *CustomMathDriver {
	driver := &CustomMathDriver{
		DriverMath: base64Captcha.DriverMath{
			Height:          height,
			Width:           width,
			NoiseCount:      noiseCount,
			ShowLineOptions: showLineOptions,
			BgColor:         bgColor,
			Fonts:           fonts,
		},
	}
	return driver
}
