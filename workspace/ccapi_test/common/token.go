package common

import (
	log2 "ccapi/server/log"
	"ccapi/server/redis"
	"fmt"
	beego "github.com/beego/beego/v2/server/web"
)

// AddMultiToken 添加 TOKEN 与 UID 对应表
// func AddMultiToken(uid int64, token string) error {
//	// 以用户 ID 为字段，将令牌和到期时间戳分别储存到两个散列里面
//	//_ = delMultiUidToken(uid)
//	expiration, err := beego.AppConfig.Int("token_expiration")
//	// 若未设置,则默认 1 天
//	if expiration == 0 || err != nil {
//		expiration = 86400
//	}
//
//	redis.SetEx(fmt.Sprintf(RedisSessionMultiTokenUid, token), expiration, fmt.Sprintf("%d", uid))
//
//	return err
// }

// AddMultiToken 添加 TOKEN 与 UID 对应表
func AddMultiToken(uid int64, token string) error {
	log2.Info("开始写入token，用户ID: %d, token: %s", uid, token)

	// 获取token过期时间配置
	expiration, err := beego.AppConfig.Int("token_expiration")
	if err != nil {
		log2.Warning("获取token_expiration配置失败: %s, 使用默认值86400", err.Error())
	}
	// 若未设置,则默认 1 天
	if expiration == 0 || err != nil {
		expiration = 86400
	}

	// 构造Redis key
	key := fmt.Sprintf(RedisSessionMultiTokenUid, token)
	value := fmt.Sprintf("%d", uid)

	log2.Info("准备写入Redis - key: %s, value: %s, expiration: %d", key, value, expiration)

	// 写入Redis
	if err := redis.SetEx2(key, expiration, value); err != nil {
		log2.Error("Redis写入失败 - key: %s, error: %s", key, err.Error())
		return fmt.Errorf("token写入Redis失败: %v", err)
	}

	// 验证是否写入成功
	val := redis.Get(key)
	if val == "" {
		log2.Error("Redis写入验证失败 - key: %s, 预期值: %s, 实际值为空", key, value)
		return fmt.Errorf("token写入Redis验证失败")
	}

	if val != value {
		log2.Error("Redis写入验证失败 - key: %s, 预期值: %s, 实际值: %s", key, value, val)
		return fmt.Errorf("token写入Redis验证失败，值不匹配")
	}

	log2.Info("token写入Redis成功 - key: %s, value: %s", key, value)
	return nil
}

// WxToken 添加 TOKEN 与 UID 对应表（微信用户）
func WxToken(uid int64, token string) error {
	// 以用户 ID 为字段，将令牌和到期时间戳分别储存到两个散列里面
	// _ = delMultiUidToken(uid)
	expiration, err := beego.AppConfig.Int("token_expiration")
	// 若未设置,则默认 1 天
	if expiration == 0 || err != nil {
		expiration = 86400
	}

	redis.SetEx(fmt.Sprintf(WxRedisSessionMultiTokenUid, token), expiration, fmt.Sprintf("%d", uid))

	return err
}
