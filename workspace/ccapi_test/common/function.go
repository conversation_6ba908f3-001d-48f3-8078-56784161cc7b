package common

import (
	"bytes"
	"ccapi/models/dto"
	"ccapi/server/redis"
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"crypto/sha256"
	"crypto/tls"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dysmsapi20170525 "github.com/alibabacloud-go/dysmsapi-20170525/v3/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/beego/beego/v2/core/logs"
	"github.com/go-playground/validator"
	"github.com/lionsoul2014/ip2region/binding/golang/xdb"
	"github.com/satori/go.uuid"
	"github.com/tjfoc/gmsm/sm4"
	"hash"
	"io/ioutil"
	"log"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"
)

var (
	dbPath = "./ip2region.xdb"
	cBuff  []byte
)

type MyChannel struct {
	fields map[int]chan bool
}

var MyHash = MyChannel{
	fields: make(map[int]chan bool),
}

// HSet 添加或更新hash中的字段
func (h *MyChannel) HSet(key string, field int, value chan bool) {
	h.fields[field] = value
}

// HGet 从hash中获取字段的值
func (h *MyChannel) HGet(key string, field int) (chan bool, bool) {
	value, exists := h.fields[field]
	return value, exists
}

// Validate 参数校验
func Validate(data []byte, dto interface{}) error {
	var err error
	err = json.Unmarshal(data, dto)
	if err != nil {
		return err
	}
	valid := validator.New()
	err = valid.RegisterValidation("checkName", checkName)
	err = valid.RegisterValidation("checkPass", checkPass)
	if err != nil {
		return err
	}

	err = valid.Struct(dto)
	return err
}

func InitAddr() {
	var err error
	cBuff, err = xdb.LoadContentFromFile(dbPath)
	if err != nil {
		fmt.Printf("failed to load content from `%s`: %s\n", dbPath, err)
		return
	}
}

func GetAddr(ip string) (string, error) {
	searcher, err := xdb.NewWithBuffer(cBuff)
	if err != nil {
		return "", err
	}

	defer searcher.Close()

	region, err := searcher.SearchByStr(ip)
	if err != nil {
		return "", err
	}

	return region, nil
}

// 验证用户名
func checkName(f validator.FieldLevel) bool {
	name := f.Field().String()
	result, _ := regexp.MatchString("^[a-zA-Z]\\w{2,15}$", name)
	return result
}

// 验证密码
func checkPass(f validator.FieldLevel) bool {
	pass := f.Field().String()
	result, _ := regexp.MatchString("^[a-zA-Z]\\w{5,15}$", pass)
	return result
}

// PublicCheckPass 验证密码
func PublicCheckPass(password string) bool {
	result, _ := regexp.MatchString("^[a-zA-Z]\\w{5,15}$", password)
	return result
}

// MD5 字符串生成 MD5
func MD5(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// RandomString 随机字符串
func RandomString(flag string, length ...int) string {
	var pool string

	switch flag {
	case "basic":
		seededRand := rand.New(rand.NewSource(time.Now().UnixNano()))
		return strconv.Itoa(seededRand.Int())

	case "alnum", "numeric", "nozero", "alpha", "hex":
		switch flag {
		case "alpha":
			pool = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
			break
		case "alnum":
			pool = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
			break
		case "numeric":
			pool = "0123456789"
			break
		case "nozero":
			pool = "123456789"
			break
		case "hex":
			pool = "0123456789abcdefABCDEF"
			break
		}

		var result []byte
		bytes := []byte(pool)
		if len(length) == 0 {
			length[0] = 32
		}
		r := rand.New(rand.NewSource(time.Now().UnixNano()))
		for i := 0; i < length[0]; i++ {
			result = append(result, bytes[r.Intn(len(bytes))])
		}
		return string(result)

	case "md5", "sha1", "sha256":
		var h hash.Hash
		seededRand := rand.New(rand.NewSource(time.Now().UnixNano()))

		switch flag {
		case "sha1":
			h = sha1.New()
			break

		case "sha256":
			h = sha256.New()
			break

		case "md5":
		default:
			h = md5.New()
		}

		if h != nil {
			h.Write([]byte(strconv.Itoa(seededRand.Int())))
			return hex.EncodeToString(h.Sum(nil))
		}
	}

	return ""
}

// GetRoles 获取角色列表
func GetRoles(level UserLevel) []string {
	// var roles []string
	roles := make([]string, 0)
	// roles := []string{"public"}
	roles = append(roles, UserLevelArr[level])
	return roles
}

// CheckToken 验证登录
func CheckToken(token string) (int, error) {
	var (
		err error
		uid string
	)

	if token == "" {
		err = errors.New("令牌不存在")
		return 0, err
	}

	uid = redis.Get(fmt.Sprintf(RedisSessionMultiTokenUid, token))
	if uid == "" {
		err = errors.New("登录已失效, 请重新登录")
		return 0, err
	}
	id := ChangeStringToInt(uid)
	return id, nil
}

// ChangeStringToInt 改变字符串为 int
// str string 字符串入参
func ChangeStringToInt(str string) int {
	r, err := strconv.Atoi(str)
	if err != nil {
		return 0
	}
	return r
}

// DelOwnDevices 删除redis中记录的指定用户所拥有的所有设备
// uid: 0 删除所有用户的，非0 删除指定用户的
func DelOwnDevices(uid int64) {
	if uid > 0 {
		redis.Del(fmt.Sprintf("%s%d", RedisDeviceOwnerUID, uid))
	} else {
		keyVar := redis.GetRedis().Do("KEYS", fmt.Sprintf("%s*", RedisDeviceOwnerUID)).Val()

		for _, key := range keyVar.([]string) {
			redis.Del(key)
		}

	}
}

// IsShengJiLevel 是否是省级管理员或省级值班员
func IsShengJiLevel(level UserLevel) bool {
	return level < XGLevel
}

// IsEditableLevel 可写权限账号, 单数为具有管理权限
func IsEditableLevel(level UserLevel) bool {
	return level%2 == 1
}

// ChangeTimeToInt 把字符串时间变成时间戳
func ChangeTimeToInt(timeStr string) int64 {
	timeLayout := "2006-01-02 15:04:05"
	loc, _ := time.LoadLocation("Local")
	theTime, _ := time.ParseInLocation(timeLayout, timeStr, loc)
	return theTime.Unix()
}

// GetDateTimeOfWeek  获取第N周的第M天的此时
// differ 距离本周间隔, 0为本周, 正数为后n周, 负数为前n周
// weekday 获取该周的第M天
func GetDateTimeOfWeek(differ int, getWeekday time.Weekday) time.Time {
	now := time.Now()
	nowWeekday := now.Weekday()
	if nowWeekday == 0 {
		nowWeekday = 7
	}
	if getWeekday == 0 {
		getWeekday = 7
	}
	offset := int(getWeekday - nowWeekday)
	offset += 7 * differ
	return now.Local().AddDate(0, 0, offset)
}

// GetZeroTime 获取零点的时间
func GetZeroTime(t *time.Time) {
	*t = time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

// GetMondayTs 获取本周一的时间戳
func GetMondayTs() int64 {
	monday := GetDateTimeOfWeek(0, time.Monday)
	GetZeroTime(&monday)
	return monday.Unix()
}

// PathExists 判断文件是否存在
func PathExists(path string) bool {

	_, err := os.Stat(path)
	if err == nil {
		return true
	}
	if os.IsNotExist(err) {
		return false
	}
	return false
}

// InArray 判断字符串是否在数组中
func InArray(need string, haystack []string) bool {
	for _, item := range haystack {
		if item == need {
			return true
		}
	}
	return false
}

// ECB模式解密
func ECBDecrypt(crypted, key []byte) ([]byte, error) {
	if !validKey(key) {
		return nil, fmt.Errorf("秘钥长度错误,当前传入长度为 %d", len(key))
	}
	if len(crypted) < 1 {
		return nil, fmt.Errorf("源数据长度不能为0")
	}
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	if len(crypted)%block.BlockSize() != 0 {
		return nil, fmt.Errorf("源数据长度必须是 %d 的整数倍，当前长度为：%d", block.BlockSize(), len(crypted))
	}
	var dst []byte
	tmpData := make([]byte, block.BlockSize())

	for index := 0; index < len(crypted); index += block.BlockSize() {
		block.Decrypt(tmpData, crypted[index:index+block.BlockSize()])
		dst = append(dst, tmpData...)
	}
	dst, err = PKCS5UnPadding(dst)
	if err != nil {
		return nil, err
	}
	return dst, nil
}

// ECB模式加密
func ECBEncrypt(src, key []byte) ([]byte, error) {
	if !validKey(key) {
		return nil, fmt.Errorf("秘钥长度错误, 当前传入长度为 %d", len(key))
	}
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	if len(src) < 1 {
		return nil, fmt.Errorf("源数据长度不能为0")
	}
	src = PKCS5Padding(src, block.BlockSize())
	if len(src)%block.BlockSize() != 0 {
		return nil, fmt.Errorf("源数据长度必须是 %d 的整数倍，当前长度为：%d", block.BlockSize(), len(src))
	}
	var dst []byte
	tmpData := make([]byte, block.BlockSize())
	for index := 0; index < len(src); index += block.BlockSize() {
		block.Encrypt(tmpData, src[index:index+block.BlockSize()])
		dst = append(dst, tmpData...)
	}
	return dst, nil
}

// PKCS5填充
func PKCS5Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

// 去除PKCS5填充
func PKCS5UnPadding(origData []byte) ([]byte, error) {
	length := len(origData)
	unpadding := int(origData[length-1])

	if length < unpadding {
		return nil, fmt.Errorf("invalid unpadding length")
	}
	return origData[:(length - unpadding)], nil
}

// 秘钥长度验证
func validKey(key []byte) bool {
	k := len(key)
	switch k {
	default:
		return false
	case 16, 24, 32:
		return true
	}
}

// GetFirstDateOfWeek 获取本周周一的日期
func GetFirstDateOfWeek(t time.Time) time.Time {

	offset := int(time.Monday - t.Weekday())
	if offset > 0 {
		offset = -6
	}

	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local).
		AddDate(0, 0, offset)

}

// GetLastDateOfWeek 获取某个时间
func GetLastDateOfWeek(t time.Time, offset int) time.Time {

	return GetFirstDateOfWeek(t).
		AddDate(0, 0, offset)

}

// GetFirstDateOfMonth 获取传入的时间所在月份的第一天，即某月第一天的0点。如传入time.Now(), 返回当前月份的第一天0点时间。
func GetFirstDateOfMonth(d time.Time) time.Time {
	d = d.AddDate(0, 0, -d.Day()+1)
	return GetZeroTime2(d)
}

// GetZeroTime2 获取某一天的0点时间
func GetZeroTime2(d time.Time) time.Time {
	return time.Date(d.Year(), d.Month(), d.Day(), 0, 0, 0, 0, d.Location())
}

// GetZeroTime3 获取某一天的某点时间
func GetZeroTime3(d time.Time, hour int) time.Time {
	return time.Date(d.Year(), d.Month(), d.Day(), hour, 0, 0, 0, d.Location())
}

// pkcs5填充
func pkcs5Padding(src []byte, blockSize int) []byte {
	padding := blockSize - len(src)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(src, padtext...)
}

func Sm4Encrypt(key, iv, plainText []byte) ([]byte, error) {
	block, err := sm4.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockSize := block.BlockSize()
	origData := pkcs5Padding(plainText, blockSize)
	blockMode := cipher.NewCBCEncrypter(block, iv)
	cryted := make([]byte, len(origData))
	blockMode.CryptBlocks(cryted, origData)
	return cryted, nil
}

func pkcs5UnPadding(src []byte) []byte {
	length := len(src)
	unpadding := int(src[length-1])
	return src[:(length - unpadding)]
}

func Sm4Decrypt(key, iv, cipherText []byte) ([]byte, error) {
	block, err := sm4.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockMode := cipher.NewCBCDecrypter(block, iv)
	origData := make([]byte, len(cipherText))
	blockMode.CryptBlocks(origData, cipherText)
	origData = pkcs5UnPadding(origData)
	return origData, nil
}

func BuildRequestBody(sender, receiver, templateId, templateParas, statusCallBack, signature string) string {
	param := "from=" + url.QueryEscape(sender) + "&to=" + url.QueryEscape(receiver) + "&templateId=" + url.QueryEscape(templateId)
	if templateParas != "" {
		param += "&templateParas=" + url.QueryEscape(templateParas)
	}
	if statusCallBack != "" {
		param += "&statusCallback=" + url.QueryEscape(statusCallBack)
	}
	if signature != "" {
		param += "&signature=" + url.QueryEscape(signature)
	}
	return param
}

func HUAWEIPost(url string, param []byte, headers map[string]string) (string, error) {
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(param))
	if err != nil {
		return "", err
	}
	for key, header := range headers {
		req.Header.Set(key, header)
	}

	resp, err := client.Do(req)
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	return string(body), nil
}

func BuildWsseHeader(appKey, appSecret string) string {
	var cTime = time.Now().Format("2006-01-02T15:04:05Z")
	var nonce = uuid.NewV4().String()
	nonce = strings.ReplaceAll(nonce, "-", "")

	h := sha256.New()
	h.Write([]byte(nonce + cTime + appSecret))
	passwordDigestBase64Str := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return fmt.Sprintf("UsernameToken Username=\"%s\",PasswordDigest=\"%s\",Nonce=\"%s\",Created=\"%s\"", appKey, passwordDigestBase64Str, nonce, cTime)
}

func HttpGet(url string) []byte {
	resp, err := http.Get(url)
	if err != nil {
		log.Fatal(err)
	}

	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Fatal(err)
	}

	return body
}

// DecryptWXOpenData 微信小程序解密算法 AES-128-CBC
func DecryptWXOpenData(appid string, sessionKey string, encryptData string, iv string) (map[string]interface{}, error) {
	decodeBytes, err := base64.StdEncoding.DecodeString(encryptData)
	if err != nil {
		return nil, err
	}
	sessionKeyBytes, errKey := base64.StdEncoding.DecodeString(sessionKey)
	if errKey != nil {
		return nil, errKey
	}
	ivBytes, errIv := base64.StdEncoding.DecodeString(iv)
	if errIv != nil {
		return nil, errIv
	}
	dataBytes, errData := aesDecrypt(decodeBytes, sessionKeyBytes, ivBytes)
	if errData != nil {
		return nil, errData
	}

	var result map[string]interface{}
	errResult := json.Unmarshal(dataBytes, &result)

	watermark := result["watermark"].(map[string]interface{})
	if watermark["appid"] != appid {
		return nil, errors.New("invalid appid data")
	}
	return result, errResult
}

// AES 解密
func aesDecrypt(crypt []byte, key []byte, iv []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockMode := cipher.NewCBCDecrypter(block, iv)
	// 原始数据
	origData := make([]byte, len(crypt))
	blockMode.CryptBlocks(origData, crypt)

	// 去除填充  --- 数据尾端有'/x0e'占位符,去除它
	length := len(origData)
	unp := int(origData[length-1])
	return origData[:(length - unp)], nil
}

/**
 * 使用AK&SK初始化账号Client
 * @param accessKeyId
 * @param accessKeySecret
 * @return Client
 * @throws Exception
 */
func CreateClient(accessKeyId *string, accessKeySecret *string) (_result *dysmsapi20170525.Client, _err error) {
	config := &openapi.Config{
		// 必填，您的 AccessKey ID
		AccessKeyId: accessKeyId,
		// 必填，您的 AccessKey Secret
		AccessKeySecret: accessKeySecret,
	}
	// Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
	config.Endpoint = tea.String("dysmsapi.aliyuncs.com")
	_result = &dysmsapi20170525.Client{}
	_result, _err = dysmsapi20170525.NewClient(config)
	return _result, _err
}

func GenerateHmacSHA256Signature(key string, message string) string {
	// 将密钥转换为字节数组
	keyBytes := []byte(key)

	// 创建HMAC-SHA256哈希对象
	myHash := hmac.New(sha256.New, keyBytes)

	// 写入要签名的消息
	myHash.Write([]byte(message))

	// 计算签名并转换为十六进制字符串
	signature := hex.EncodeToString(myHash.Sum(nil))

	return signature
}

// PostData 杭州项目
func PostData(body dto.ReturnMap, url string) error {
	stringBody, _ := json.Marshal(body)

	signature := GenerateHmacSHA256Signature("8WzjFegQ5xJlaHF3", string(stringBody))

	// 创建一个新的请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(stringBody))
	if err != nil {
		logs.Error("Error creating request:", err)
		return err
	}

	// 添加自定义Header
	req.Header.Set("Content-Type", "application/json;charset=utf-8")
	req.Header.Set("x-auth-key", "9zjn")
	req.Header.Set("x-auth-signature", signature)

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("Error sending request:", err)
		return err
	}
	defer resp.Body.Close()

	// 读取响应数据
	responseBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logs.Error("Error reading response:", err)
		return err
	}

	// 打印响应数据
	logs.Info("Response:", string(responseBody))
	return nil
}

func GetRedisHeartbeatTs(deviceId string) (int64, error) {
	result, err := redis.HGet(RedisDeviceHeartbeatTS, deviceId)
	if err != nil {
		return 0, err
	}

	// 将字符串转换为 int64
	ts, err := strconv.ParseInt(result, 10, 64)
	if err != nil {
		return 0, err
	}

	return ts, nil
}
