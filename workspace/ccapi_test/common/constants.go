package common

const (
	// Redis key 前缀
	RedisKeyPrefix = "ccapi:"

	// 用户Token相关
	RedisUserTokenKey     = RedisKeyPrefix + "user:token:%d:%s"   // 用户Token key (uid, sessionId)
	RedisUserRefreshToken = RedisKeyPrefix + "user:refresh:%d:%s" // 用户刷新Token key (uid, sessionId)

	// 设备运行模式常量定义
	// DeviceModeUnknown 未知状态
	DeviceModeUnknown = -1
	// DeviceModeStandby 待机状态
	DeviceModeStandby = 0
	// DeviceModeRemoteControl 遥控模式
	DeviceModeRemoteControl = 1
	// DeviceModeAutoPilotStandby 自驾待命状态(非遥控、自动驾驶、无自动驾驶相关任务)
	DeviceModeAutoPilotStandby = 2
	// DeviceModeAutoPilotExecuting 自驾执行状态
	DeviceModeAutoPilotExecuting = 3
	// DeviceModeRemoteDriving 远程驾驶状态
	DeviceModeRemoteDriving = 4
	// DeviceModeEmergencyStop 紧急停止状态
	DeviceModeEmergencyStop = 5
)
