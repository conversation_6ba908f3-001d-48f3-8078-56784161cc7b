# 数据库配置 - 使用统一的 db.{connectionName}.{property} 格式

# 默认数据库（主数据库）
db.default.host = "************"
db.default.port = "3306"
db.default.user = "root"
db.default.pass = "qwer323w@8"
db.default.name = "pixmoving"
db.default.ssl = false

# XXL Job 数据库
db.xxlJob.host = "************"
db.xxlJob.port = "3306"
db.xxlJob.user = "root"
db.xxlJob.pass = "qwer323w@8"
db.xxlJob.name = "xxl_job"
db.xxlJob.ssl = false


# TDengine 数据库
db.tdengine.host = "127.0.0.1"
db.tdengine.port = "6041"
db.tdengine.user = "root"
db.tdengine.pass = "Pixm2022"
db.tdengine.name = "pixmoving"
db.tdengine.ssl = false

# Robobus 微信数据库
db.robobusWX.host = "**************"
db.robobusWX.port = "3306"
db.robobusWX.user = "root"
db.robobusWX.pass = "pix@6688"
db.robobusWX.name = "pixmoving"
db.robobusWX.ssl = false

# 无人车数据库
db.wurenche.host = "************"
db.wurenche.port = "3306"
db.wurenche.user = "root"
db.wurenche.pass = "qwer323w@8"
db.wurenche.name = "wurenche"
db.wurenche.ssl = false


redis_host = "************:6379"
redis_db = 2
redis_pass = "pix@6688"

kafka_host = "127.0.0.1:9092"
kafka_topic = "device-cmd"

token_expiration = 86400

offline_interval = 1800
lan_offline_interval = 300

endpoint = "oss-accelerate.aliyuncs.com"
accessKeyId = "LTAI5tKnqGSPn3shdAEqNyJx"
accessKeySecret = "******************************"
bucketName = "pix0922"
firmwareOss = 1

disk_name = "/dev/vda"

ossUrl = "https://oss2.pixmoving.city/"

server_dir = "/mnt/data/ccserver/"

certKey = "78fe3379a291ece5"

es_pass = "pix@6688"

vin_pass = "a65581299ee379ae9c9fb8cec3db5996"

HangZhouUserId = 223

WebSocketAddr = "wss://testpix.pixmoving.city/ws"