# ==================== 数据库配置 ====================
# 使用统一格式: db.{连接名}.{配置项}
# 系统会自动发现所有 db.*.host 配置并创建对应的数据库连接

# 主数据库 (连接名: default，对应原来的 main)
db.default.host = "**************"
db.default.port = 3306
db.default.user = "root"
db.default.pass = "pix@6688"
db.default.name = "pixmoving-test"


# xxlJob数据库 (连接名: xxlJob)
db.xxlJob.host = "**************"
db.xxlJob.port = 3306
db.xxlJob.user = "root"
db.xxlJob.pass = "pix@6688"
db.xxlJob.name = "xxl_job"

# robobusWX数据库 (连接名: robobusWX)
db.robobusWX.host = "**************"
db.robobusWX.port = 3306
db.robobusWX.user = "root"
db.robobusWX.pass = "pix@6688"
db.robobusWX.name = "pixmoving-test"

# wurenche数据库 (连接名: wurenche)
db.wurenche.host = "**************"
db.wurenche.port = 3306
db.wurenche.user = "root"
db.wurenche.pass = "pix@6688"
db.wurenche.name = "pixmoving-dev"




# ==================== 其他服务配置 ====================

# TDengine配置
; tdengineHost = "*************"
; tdenginePort = "6041"
; tdengineUser = "root"
; tdenginePass = "Pixm2022"
; tdengineDB = "pixmoving"

tdengineHost = "************"
tdenginePort = "6041"
tdengineUser = "root"
tdenginePass = "Pixm2022"
tdengineDB = "pixmoving"
tdengineEnableSQLLog = true


redis_host = "**************:6379"
redis_db = 3
redis_pass = "Pix@121cc"

kafka_host = "**************:9092"
kafka_topic = "device-cmd"

token_expiration = 86400

offline_interval = 1800
lan_offline_interval = 300

endpoint = "oss-accelerate.aliyuncs.com"
accessKeyId = "LTAI5tKnqGSPn3shdAEqNyJx"
accessKeySecret = "******************************"
bucketName = "pix0922"
firmwareOss = 0

disk_name = "/dev/nvme0n1p3"

ossUrl = "https://oss2.pixmoving.city/"

server_dir = "/data/ccapi/"

certKey = "78fe3379a291ece5"

es_pass = "pix@6688"

vin_pass = "a65581299ee379ae9c9fb8cec3db5996"

HangZhouUserId = 223

WebSocketAddr = "ws://**************:3900/ws"



