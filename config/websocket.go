package config

import "time"

// WebSocketConfig WebSocket 连接配置
type WebSocketConfig struct {
	// 读取超时时间
	ReadTimeout time.Duration
	// 写入超时时间  
	WriteTimeout time.Duration
	// 心跳间隔
	HeartbeatInterval time.Duration
	// 握手超时时间
	HandshakeTimeout time.Duration
	// 最大重连次数
	MaxReconnectAttempts int
	// 重连间隔
	ReconnectInterval time.Duration
}

// DefaultWebSocketConfig 默认WebSocket配置
var DefaultWebSocketConfig = WebSocketConfig{
	ReadTimeout:          10 * time.Minute, // 10分钟读取超时
	WriteTimeout:         30 * time.Second, // 30秒写入超时
	HeartbeatInterval:    2 * time.Minute,  // 2分钟心跳间隔
	HandshakeTimeout:     30 * time.Second, // 30秒握手超时
	MaxReconnectAttempts: 5,                // 最大重连5次
	ReconnectInterval:    3 * time.Second,  // 3秒重连间隔
}

// UploadConfig 上传配置
type UploadConfig struct {
	// 是否使用进度显示
	ShowProgress bool
	// 是否使用rsync替代scp
	UseRsync bool
	// 上传超时时间
	UploadTimeout time.Duration
}

// DefaultUploadConfig 默认上传配置
var DefaultUploadConfig = UploadConfig{
	ShowProgress:  true,               // 默认显示进度
	UseRsync:      true,               // 默认使用rsync
	UploadTimeout: 30 * time.Minute,   // 30分钟上传超时
}
