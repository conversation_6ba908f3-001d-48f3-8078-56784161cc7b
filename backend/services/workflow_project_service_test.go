package services

import (
	"testing"

	"GoShip/backend/models"
	"github.com/stretchr/testify/assert"
)

// TestNewWorkflowProjectService 需要数据库连接，暂时跳过
// func TestNewWorkflowProjectService(t *testing.T) {
// 	service := NewWorkflowProjectService()
// 	assert.NotNil(t, service)
// 	assert.NotNil(t, service.o)
// }

func TestWorkflowProjectService_ValidateFilter(t *testing.T) {
	// 测试过滤器的默认值处理
	filter := &models.WorkflowProjectFilter{
		Page: 0,
		Size: 0,
	}

	// 在实际使用中，这些值会在控制器层设置默认值
	if filter.Page <= 0 {
		filter.Page = 1
	}
	if filter.Size <= 0 {
		filter.Size = 20
	}

	assert.Equal(t, 1, filter.Page)
	assert.Equal(t, 20, filter.Size)
}

func TestWorkflowProjectService_FilterValidation(t *testing.T) {
	tests := []struct {
		name   string
		filter models.WorkflowProjectFilter
		valid  bool
	}{
		{
			name: "有效过滤器",
			filter: models.WorkflowProjectFilter{
				Language:    "go",
				Environment: "development",
				ProjectType: "backend",
				Status:      "active",
				Page:        1,
				Size:        20,
			},
			valid: true,
		},
		{
			name: "空过滤器",
			filter: models.WorkflowProjectFilter{
				Page: 1,
				Size: 20,
			},
			valid: true,
		},
		{
			name: "带搜索的过滤器",
			filter: models.WorkflowProjectFilter{
				Search: "测试项目",
				Page:   1,
				Size:   10,
			},
			valid: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 验证过滤器字段的有效性
			if tt.filter.Language != "" {
				validLanguages := map[string]bool{
					"go": true, "python": true, "vue": true,
					"java": true, "nodejs": true, "react": true,
				}
				assert.True(t, validLanguages[tt.filter.Language], "无效的语言: %s", tt.filter.Language)
			}

			if tt.filter.Environment != "" {
				validEnvironments := map[string]bool{
					"development": true, "test": true, "production": true,
				}
				assert.True(t, validEnvironments[tt.filter.Environment], "无效的环境: %s", tt.filter.Environment)
			}

			if tt.filter.ProjectType != "" {
				validProjectTypes := map[string]bool{
					"frontend": true, "backend": true, "fullstack": true,
				}
				assert.True(t, validProjectTypes[tt.filter.ProjectType], "无效的项目类型: %s", tt.filter.ProjectType)
			}

			if tt.filter.Status != "" {
				validStatuses := map[string]bool{
					"active": true, "inactive": true, "archived": true,
				}
				assert.True(t, validStatuses[tt.filter.Status], "无效的状态: %s", tt.filter.Status)
			}

			assert.True(t, tt.filter.Page > 0, "页码必须大于0")
			assert.True(t, tt.filter.Size > 0, "页面大小必须大于0")
		})
	}
}

func TestWorkflowProjectService_RequestValidation(t *testing.T) {
	tests := []struct {
		name    string
		request models.WorkflowProjectRequest
		valid   bool
	}{
		{
			name: "有效请求",
			request: models.WorkflowProjectRequest{
				Name:        "测试项目",
				Environment: "development",
				Language:    "go",
				ProjectType: "backend",
				Description: "这是一个测试项目",
			},
			valid: true,
		},
		{
			name: "最小有效请求",
			request: models.WorkflowProjectRequest{
				Name:        "项目",
				Environment: "test",
				Language:    "python",
				ProjectType: "frontend",
			},
			valid: true,
		},
		{
			name: "空名称",
			request: models.WorkflowProjectRequest{
				Name:        "",
				Environment: "development",
				Language:    "go",
				ProjectType: "backend",
			},
			valid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 验证必填字段
			if tt.valid {
				assert.NotEmpty(t, tt.request.Name, "项目名称不能为空")
				assert.NotEmpty(t, tt.request.Environment, "环境不能为空")
				assert.NotEmpty(t, tt.request.Language, "语言不能为空")
				assert.NotEmpty(t, tt.request.ProjectType, "项目类型不能为空")
			}

			// 验证枚举值
			if tt.request.Environment != "" {
				validEnvironments := []string{"development", "test", "production"}
				assert.Contains(t, validEnvironments, tt.request.Environment)
			}

			if tt.request.Language != "" {
				validLanguages := []string{"go", "python", "vue", "java", "nodejs", "react"}
				assert.Contains(t, validLanguages, tt.request.Language)
			}

			if tt.request.ProjectType != "" {
				validProjectTypes := []string{"frontend", "backend", "fullstack"}
				assert.Contains(t, validProjectTypes, tt.request.ProjectType)
			}
		})
	}
}

// 测试业务逻辑函数（不依赖数据库）
func TestWorkflowProjectService_BusinessLogic(t *testing.T) {
	t.Run("项目名称重复检查逻辑", func(t *testing.T) {
		// 模拟项目名称重复检查的逻辑
		existingNames := map[string]bool{
			"已存在的项目": true,
			"另一个项目":  true,
		}

		newName := "新项目"
		_, exists := existingNames[newName]
		assert.False(t, exists, "新项目名称不应该重复")

		duplicateName := "已存在的项目"
		_, exists = existingNames[duplicateName]
		assert.True(t, exists, "重复的项目名称应该被检测到")
	})

	t.Run("分页计算逻辑", func(t *testing.T) {
		page := 2
		size := 10
		expectedOffset := (page - 1) * size

		actualOffset := (page - 1) * size
		assert.Equal(t, expectedOffset, actualOffset, "分页偏移量计算错误")
		assert.Equal(t, 10, actualOffset, "第2页，每页10条，偏移量应该是10")
	})

	t.Run("状态转换逻辑", func(t *testing.T) {
		// 测试项目状态转换
		validTransitions := map[string][]string{
			"active":   {"inactive", "archived"},
			"inactive": {"active", "archived"},
			"archived": {}, // 归档状态不能转换到其他状态
		}

		// 测试有效转换
		currentStatus := "active"
		newStatus := "inactive"
		validTargets := validTransitions[currentStatus]
		
		found := false
		for _, target := range validTargets {
			if target == newStatus {
				found = true
				break
			}
		}
		assert.True(t, found, "从active到inactive应该是有效的状态转换")

		// 测试无效转换
		currentStatus = "archived"
		newStatus = "active"
		validTargets = validTransitions[currentStatus]
		
		found = false
		for _, target := range validTargets {
			if target == newStatus {
				found = true
				break
			}
		}
		assert.False(t, found, "从archived到active应该是无效的状态转换")
	})
}

func TestWorkflowProjectService_ConfigHandling(t *testing.T) {
	t.Run("工作流配置验证", func(t *testing.T) {
		// 测试有效的工作流配置
		validConfig := map[string]interface{}{
			"nodes": []interface{}{
				map[string]interface{}{
					"id":   "1",
					"type": "start",
					"position": map[string]interface{}{
						"x": 100,
						"y": 100,
					},
				},
			},
			"edges": []interface{}{},
		}

		// 验证配置结构
		assert.Contains(t, validConfig, "nodes", "配置应该包含nodes字段")
		assert.Contains(t, validConfig, "edges", "配置应该包含edges字段")

		nodes, ok := validConfig["nodes"].([]interface{})
		assert.True(t, ok, "nodes应该是数组类型")
		assert.Len(t, nodes, 1, "应该有一个节点")

		edges, ok := validConfig["edges"].([]interface{})
		assert.True(t, ok, "edges应该是数组类型")
		assert.Len(t, edges, 0, "应该没有连接")
	})

	t.Run("空配置处理", func(t *testing.T) {
		// 测试空配置的默认值
		emptyConfig := map[string]interface{}{
			"nodes": []interface{}{},
			"edges": []interface{}{},
		}

		assert.NotNil(t, emptyConfig["nodes"], "空配置应该有默认的nodes数组")
		assert.NotNil(t, emptyConfig["edges"], "空配置应该有默认的edges数组")
	})
}
