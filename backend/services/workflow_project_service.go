package services

import (
	"fmt"
	"time"

	"GoShip/backend/models"
	"github.com/beego/beego/v2/client/orm"
)

// WorkflowProjectService 工作流项目服务
type WorkflowProjectService struct {
	o orm.Ormer
}

// NewWorkflowProjectService 创建工作流项目服务
func NewWorkflowProjectService() *WorkflowProjectService {
	return &WorkflowProjectService{
		o: orm.NewOrm(),
	}
}

// GetProjects 获取工作流项目列表
func (s *WorkflowProjectService) GetProjects(filter *models.WorkflowProjectFilter, userID uint64) (*models.PaginatedWorkflowProjects, error) {
	var projects []*models.WorkflowProject
	qs := s.o.QueryTable(new(models.WorkflowProject))

	// 只显示用户有权限的项目
	qs = qs.Filter("created_by", userID)

	// 应用过滤条件
	if filter.Language != "" {
		qs = qs.Filter("language", filter.Language)
	}
	if filter.Environment != "" {
		qs = qs.Filter("environment", filter.Environment)
	}
	if filter.ProjectType != "" {
		qs = qs.Filter("project_type", filter.ProjectType)
	}
	if filter.Status != "" {
		qs = qs.Filter("status", filter.Status)
	}
	if filter.Search != "" {
		qs = qs.Filter("name__icontains", filter.Search)
	}

	// 计算总数
	total, err := qs.Count()
	if err != nil {
		return nil, fmt.Errorf("计算项目总数失败: %v", err)
	}

	// 分页查询
	offset := (filter.Page - 1) * filter.Size
	_, err = qs.OrderBy("-created_at").Limit(filter.Size, offset).All(&projects)
	if err != nil {
		return nil, fmt.Errorf("查询项目列表失败: %v", err)
	}

	// 转换为响应格式
	items := make([]models.WorkflowProjectResponse, len(projects))
	for i, project := range projects {
		response := project.ToResponse()
		
		// 获取执行统计信息
		executionCount, _ := s.o.QueryTable(new(models.WorkflowExecution)).
			Filter("project_id", project.ID).Count()
		response.ExecutionCount = executionCount

		// 获取最后执行时间
		var lastExecution models.WorkflowExecution
		err := s.o.QueryTable(new(models.WorkflowExecution)).
			Filter("project_id", project.ID).
			OrderBy("-created_at").
			Limit(1).
			One(&lastExecution)
		if err == nil {
			response.LastExecutionAt = &lastExecution.CreatedAt
		}

		items[i] = response
	}

	return &models.PaginatedWorkflowProjects{
		Items: items,
		Total: total,
		Page:  filter.Page,
		Size:  filter.Size,
	}, nil
}

// GetProject 获取单个工作流项目
func (s *WorkflowProjectService) GetProject(id uint64, userID uint64) (*models.WorkflowProject, error) {
	project := &models.WorkflowProject{}
	err := s.o.QueryTable(new(models.WorkflowProject)).
		Filter("id", id).
		Filter("created_by", userID).
		One(project)
	
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, fmt.Errorf("项目不存在或无权限访问")
		}
		return nil, fmt.Errorf("查询项目失败: %v", err)
	}

	return project, nil
}

// CreateProject 创建工作流项目
func (s *WorkflowProjectService) CreateProject(req *models.WorkflowProjectRequest, userID uint64) (*models.WorkflowProject, error) {
	// 检查项目名称是否重复
	count, err := s.o.QueryTable(new(models.WorkflowProject)).
		Filter("name", req.Name).
		Filter("created_by", userID).
		Count()
	if err != nil {
		return nil, fmt.Errorf("检查项目名称失败: %v", err)
	}
	if count > 0 {
		return nil, fmt.Errorf("项目名称已存在")
	}

	project := &models.WorkflowProject{
		Name:        req.Name,
		Environment: req.Environment,
		Language:    req.Language,
		ProjectType: req.ProjectType,
		Description: req.Description,
		Status:      "active",
		CreatedBy:   userID,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	id, err := s.o.Insert(project)
	if err != nil {
		return nil, fmt.Errorf("创建项目失败: %v", err)
	}

	project.ID = uint64(id)
	return project, nil
}

// UpdateProject 更新工作流项目
func (s *WorkflowProjectService) UpdateProject(id uint64, req *models.WorkflowProjectRequest, userID uint64) (*models.WorkflowProject, error) {
	// 检查项目是否存在且有权限
	project, err := s.GetProject(id, userID)
	if err != nil {
		return nil, err
	}

	// 检查项目名称是否重复（排除自己）
	count, err := s.o.QueryTable(new(models.WorkflowProject)).
		Filter("name", req.Name).
		Filter("created_by", userID).
		Exclude("id", id).
		Count()
	if err != nil {
		return nil, fmt.Errorf("检查项目名称失败: %v", err)
	}
	if count > 0 {
		return nil, fmt.Errorf("项目名称已存在")
	}

	// 更新项目信息
	project.Name = req.Name
	project.Environment = req.Environment
	project.Language = req.Language
	project.ProjectType = req.ProjectType
	project.Description = req.Description
	project.UpdatedAt = time.Now()

	_, err = s.o.Update(project)
	if err != nil {
		return nil, fmt.Errorf("更新项目失败: %v", err)
	}

	return project, nil
}

// DeleteProject 删除工作流项目
func (s *WorkflowProjectService) DeleteProject(id uint64, userID uint64) error {
	// 检查项目是否存在且有权限
	project, err := s.GetProject(id, userID)
	if err != nil {
		return err
	}

	// 软删除（更新状态为archived）
	project.Status = "archived"
	project.UpdatedAt = time.Now()

	_, err = s.o.Update(project, "status", "updated_at")
	if err != nil {
		return fmt.Errorf("删除项目失败: %v", err)
	}

	return nil
}

// GetWorkflowConfig 获取工作流配置
func (s *WorkflowProjectService) GetWorkflowConfig(id uint64, userID uint64) (map[string]interface{}, error) {
	project, err := s.GetProject(id, userID)
	if err != nil {
		return nil, err
	}

	return project.GetWorkflowConfig()
}

// SaveWorkflowConfig 保存工作流配置
func (s *WorkflowProjectService) SaveWorkflowConfig(id uint64, config map[string]interface{}, userID uint64) error {
	project, err := s.GetProject(id, userID)
	if err != nil {
		return err
	}

	err = project.SetWorkflowConfig(config)
	if err != nil {
		return fmt.Errorf("设置工作流配置失败: %v", err)
	}

	project.UpdatedAt = time.Now()
	_, err = s.o.Update(project, "workflow_config", "updated_at")
	if err != nil {
		return fmt.Errorf("保存工作流配置失败: %v", err)
	}

	return nil
}

// ExecuteWorkflow 执行工作流
func (s *WorkflowProjectService) ExecuteWorkflow(id uint64, userID uint64) (*models.WorkflowExecution, error) {
	project, err := s.GetProject(id, userID)
	if err != nil {
		return nil, err
	}

	if project.WorkflowConfig == "" {
		return nil, fmt.Errorf("项目尚未配置工作流")
	}

	// 创建执行记录
	execution := &models.WorkflowExecution{
		ProjectID:        id,
		ExecutorID:       userID,
		Status:           "pending",
		WorkflowSnapshot: project.WorkflowConfig,
		CreatedAt:        time.Now(),
	}

	executionID, err := s.o.Insert(execution)
	if err != nil {
		return nil, fmt.Errorf("创建执行记录失败: %v", err)
	}

	execution.ID = uint64(executionID)

	// TODO: 这里应该启动实际的工作流执行逻辑
	// 目前只是创建记录，实际执行逻辑需要后续实现

	return execution, nil
}
