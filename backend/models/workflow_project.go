package models

import (
	"encoding/json"
	"time"

	"github.com/beego/beego/v2/client/orm"
)

// WorkflowProject 工作流项目模型
type WorkflowProject struct {
	ID             uint64    `json:"id" orm:"auto;pk"`
	Name           string    `json:"name" orm:"size(255);null(false)" validate:"required,min=1,max=255"`
	Environment    string    `json:"environment" orm:"size(20);null(false)" validate:"required,oneof=development test production"`
	Language       string    `json:"language" orm:"size(20);null(false)" validate:"required,oneof=go python vue java nodejs react"`
	ProjectType    string    `json:"project_type" orm:"size(20);null(false)" validate:"required,oneof=frontend backend fullstack"`
	Description    string    `json:"description" orm:"type(text)"`
	WorkflowConfig string    `json:"workflow_config" orm:"type(text)"`
	Status         string    `json:"status" orm:"size(20);default(active)"`
	CreatedBy      uint64    `json:"created_by"`
	CreatedAt      time.Time `json:"created_at" orm:"auto_now_add;type(datetime)"`
	UpdatedAt      time.Time `json:"updated_at" orm:"auto_now;type(datetime)"`

	// 关联关系
	Permissions []*WorkflowProjectPermission `json:"permissions,omitempty" orm:"reverse(many)"`
	Executions  []*WorkflowExecution         `json:"executions,omitempty" orm:"reverse(many)"`
}

// WorkflowProjectPermission 工作流项目权限模型
type WorkflowProjectPermission struct {
	ID        uint64    `json:"id" orm:"auto;pk"`
	UserID    uint64    `json:"user_id" orm:"null(false)"`
	ProjectID uint64    `json:"project_id" orm:"null(false)"`
	Role      string    `json:"role" orm:"size(20);null(false)" validate:"required,oneof=owner editor viewer"`
	CreatedAt time.Time `json:"created_at" orm:"auto_now_add;type(datetime)"`
	UpdatedAt time.Time `json:"updated_at" orm:"auto_now;type(datetime)"`

	// 关联关系
	Project *WorkflowProject `json:"project,omitempty" orm:"rel(fk)"`
}

// WorkflowExecution 工作流执行历史模型
type WorkflowExecution struct {
	ID               uint64     `json:"id" orm:"auto;pk"`
	ProjectID        uint64     `json:"project_id" orm:"null(false)"`
	ExecutorID       uint64     `json:"executor_id" orm:"null(false)"`
	Status           string     `json:"status" orm:"size(20);null(false)" validate:"required,oneof=pending running success failed cancelled"`
	StartTime        *time.Time `json:"start_time" orm:"type(datetime);null"`
	EndTime          *time.Time `json:"end_time" orm:"type(datetime);null"`
	Duration         int        `json:"duration"` // 执行时长（秒）
	Logs             string     `json:"logs" orm:"type(text)"`
	ErrorMessage     string     `json:"error_message" orm:"type(text)"`
	WorkflowSnapshot string     `json:"workflow_snapshot" orm:"type(text)"`
	CreatedAt        time.Time  `json:"created_at" orm:"auto_now_add;type(datetime)"`

	// 关联关系
	Project *WorkflowProject `json:"project,omitempty" orm:"rel(fk)"`
}

// WorkflowProjectRequest 创建/更新工作流项目请求
type WorkflowProjectRequest struct {
	Name        string `json:"name" validate:"required,min=1,max=255"`
	Environment string `json:"environment" validate:"required,oneof=development test production"`
	Language    string `json:"language" validate:"required,oneof=go python vue java nodejs react"`
	ProjectType string `json:"project_type" validate:"required,oneof=frontend backend fullstack"`
	Description string `json:"description"`
}

// WorkflowProjectResponse 工作流项目响应
type WorkflowProjectResponse struct {
	ID          uint64    `json:"id"`
	Name        string    `json:"name"`
	Environment string    `json:"environment"`
	Language    string    `json:"language"`
	ProjectType string    `json:"project_type"`
	Description string    `json:"description"`
	Status      string    `json:"status"`
	CreatedBy   uint64    `json:"created_by"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	
	// 扩展信息
	HasWorkflow     bool `json:"has_workflow"`
	LastExecutionAt *time.Time `json:"last_execution_at,omitempty"`
	ExecutionCount  int64 `json:"execution_count"`
}

// WorkflowConfigRequest 工作流配置请求
type WorkflowConfigRequest struct {
	Config json.RawMessage `json:"config" validate:"required"`
}

// PaginatedWorkflowProjects 分页的工作流项目列表
type PaginatedWorkflowProjects struct {
	Items []WorkflowProjectResponse `json:"items"`
	Total int64                     `json:"total"`
	Page  int                       `json:"page"`
	Size  int                       `json:"size"`
}

// WorkflowProjectFilter 工作流项目过滤器
type WorkflowProjectFilter struct {
	Language    string `form:"language"`
	Environment string `form:"environment"`
	ProjectType string `form:"project_type"`
	Status      string `form:"status"`
	Search      string `form:"search"`
	Page        int    `form:"page,default=1"`
	Size        int    `form:"size,default=20"`
}

// TableName 指定表名
func (u *WorkflowProject) TableName() string {
	return "workflow_projects"
}

func (u *WorkflowProjectPermission) TableName() string {
	return "workflow_project_permissions"
}

func (u *WorkflowExecution) TableName() string {
	return "workflow_executions"
}

// init 注册模型到ORM
func init() {
	orm.RegisterModel(new(WorkflowProject))
	orm.RegisterModel(new(WorkflowProjectPermission))
	orm.RegisterModel(new(WorkflowExecution))
}

// ToResponse 转换为响应格式
func (wp *WorkflowProject) ToResponse() WorkflowProjectResponse {
	return WorkflowProjectResponse{
		ID:          wp.ID,
		Name:        wp.Name,
		Environment: wp.Environment,
		Language:    wp.Language,
		ProjectType: wp.ProjectType,
		Description: wp.Description,
		Status:      wp.Status,
		CreatedBy:   wp.CreatedBy,
		CreatedAt:   wp.CreatedAt,
		UpdatedAt:   wp.UpdatedAt,
		HasWorkflow: wp.WorkflowConfig != "",
	}
}

// GetWorkflowConfig 获取工作流配置
func (wp *WorkflowProject) GetWorkflowConfig() (map[string]interface{}, error) {
	if wp.WorkflowConfig == "" {
		return nil, nil
	}
	
	var config map[string]interface{}
	err := json.Unmarshal([]byte(wp.WorkflowConfig), &config)
	if err != nil {
		return nil, err
	}
	
	return config, nil
}

// SetWorkflowConfig 设置工作流配置
func (wp *WorkflowProject) SetWorkflowConfig(config interface{}) error {
	configBytes, err := json.Marshal(config)
	if err != nil {
		return err
	}
	
	wp.WorkflowConfig = string(configBytes)
	return nil
}

// IsOwner 检查用户是否为项目所有者
func (wp *WorkflowProject) IsOwner(userID uint64) bool {
	return wp.CreatedBy == userID
}

// HasPermission 检查用户是否有指定权限
func (wp *WorkflowProject) HasPermission(userID uint64, requiredRole string) bool {
	// 创建者默认拥有所有权限
	if wp.CreatedBy == userID {
		return true
	}
	
	// 检查权限表
	for _, perm := range wp.Permissions {
		if perm.UserID == userID {
			return hasRolePermission(perm.Role, requiredRole)
		}
	}
	
	return false
}

// hasRolePermission 检查角色权限
func hasRolePermission(userRole, requiredRole string) bool {
	roleLevel := map[string]int{
		"viewer": 1,
		"editor": 2,
		"owner":  3,
	}
	
	return roleLevel[userRole] >= roleLevel[requiredRole]
}
