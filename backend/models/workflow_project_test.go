package models

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestWorkflowProject_ToResponse(t *testing.T) {
	// 准备测试数据
	project := &WorkflowProject{
		ID:          1,
		Name:        "测试项目",
		Environment: "development",
		Language:    "go",
		ProjectType: "backend",
		Description: "这是一个测试项目",
		Status:      "active",
		CreatedBy:   100,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		WorkflowConfig: `{"nodes": [], "edges": []}`,
	}

	// 执行转换
	response := project.ToResponse()

	// 验证结果
	assert.Equal(t, project.ID, response.ID)
	assert.Equal(t, project.Name, response.Name)
	assert.Equal(t, project.Environment, response.Environment)
	assert.Equal(t, project.Language, response.Language)
	assert.Equal(t, project.ProjectType, response.ProjectType)
	assert.Equal(t, project.Description, response.Description)
	assert.Equal(t, project.Status, response.Status)
	assert.Equal(t, project.CreatedBy, response.CreatedBy)
	assert.True(t, response.HasWorkflow) // 因为有工作流配置
}

func TestWorkflowProject_GetWorkflowConfig(t *testing.T) {
	tests := []struct {
		name           string
		workflowConfig string
		expectError    bool
		expectNil      bool
	}{
		{
			name:           "空配置",
			workflowConfig: "",
			expectError:    false,
			expectNil:      true,
		},
		{
			name:           "有效JSON配置",
			workflowConfig: `{"nodes": [{"id": "1", "type": "start"}], "edges": []}`,
			expectError:    false,
			expectNil:      false,
		},
		{
			name:           "无效JSON配置",
			workflowConfig: `{"nodes": [invalid json}`,
			expectError:    true,
			expectNil:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			project := &WorkflowProject{
				WorkflowConfig: tt.workflowConfig,
			}

			config, err := project.GetWorkflowConfig()

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			if tt.expectNil {
				assert.Nil(t, config)
			} else if !tt.expectError {
				assert.NotNil(t, config)
				assert.Contains(t, config, "nodes")
			}
		})
	}
}

func TestWorkflowProject_SetWorkflowConfig(t *testing.T) {
	project := &WorkflowProject{}

	// 测试设置有效配置
	config := map[string]interface{}{
		"nodes": []interface{}{
			map[string]interface{}{"id": "1", "type": "start", "position": map[string]interface{}{"x": float64(100), "y": float64(100)}},
		},
		"edges": []interface{}{},
	}

	err := project.SetWorkflowConfig(config)
	assert.NoError(t, err)
	assert.NotEmpty(t, project.WorkflowConfig)

	// 验证可以正确解析回来
	parsedConfig, err := project.GetWorkflowConfig()
	assert.NoError(t, err)
	assert.Equal(t, config, parsedConfig)
}

func TestWorkflowProject_IsOwner(t *testing.T) {
	project := &WorkflowProject{
		CreatedBy: 100,
	}

	assert.True(t, project.IsOwner(100))
	assert.False(t, project.IsOwner(200))
}

func TestWorkflowProject_HasPermission(t *testing.T) {
	project := &WorkflowProject{
		CreatedBy: 100,
		Permissions: []*WorkflowProjectPermission{
			{UserID: 200, Role: "editor"},
			{UserID: 300, Role: "viewer"},
		},
	}

	// 测试创建者权限
	assert.True(t, project.HasPermission(100, "owner"))
	assert.True(t, project.HasPermission(100, "editor"))
	assert.True(t, project.HasPermission(100, "viewer"))

	// 测试编辑者权限
	assert.False(t, project.HasPermission(200, "owner"))
	assert.True(t, project.HasPermission(200, "editor"))
	assert.True(t, project.HasPermission(200, "viewer"))

	// 测试查看者权限
	assert.False(t, project.HasPermission(300, "owner"))
	assert.False(t, project.HasPermission(300, "editor"))
	assert.True(t, project.HasPermission(300, "viewer"))

	// 测试无权限用户
	assert.False(t, project.HasPermission(400, "viewer"))
}

func TestHasRolePermission(t *testing.T) {
	tests := []struct {
		userRole     string
		requiredRole string
		expected     bool
	}{
		{"owner", "owner", true},
		{"owner", "editor", true},
		{"owner", "viewer", true},
		{"editor", "owner", false},
		{"editor", "editor", true},
		{"editor", "viewer", true},
		{"viewer", "owner", false},
		{"viewer", "editor", false},
		{"viewer", "viewer", true},
	}

	for _, tt := range tests {
		t.Run(tt.userRole+"_"+tt.requiredRole, func(t *testing.T) {
			result := hasRolePermission(tt.userRole, tt.requiredRole)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestWorkflowProjectRequest_Validation(t *testing.T) {
	// 这里可以添加验证测试，需要集成验证库
	validRequest := WorkflowProjectRequest{
		Name:        "测试项目",
		Environment: "development",
		Language:    "go",
		ProjectType: "backend",
		Description: "测试描述",
	}

	// 基本验证：确保结构体字段正确
	assert.Equal(t, "测试项目", validRequest.Name)
	assert.Equal(t, "development", validRequest.Environment)
	assert.Equal(t, "go", validRequest.Language)
	assert.Equal(t, "backend", validRequest.ProjectType)
}

func TestWorkflowProjectFilter_DefaultValues(t *testing.T) {
	filter := WorkflowProjectFilter{}
	
	// 测试默认值（这需要在实际使用时通过框架设置）
	assert.Equal(t, "", filter.Language)
	assert.Equal(t, "", filter.Environment)
	assert.Equal(t, 0, filter.Page) // 默认值会在绑定时设置
	assert.Equal(t, 0, filter.Size)
}

func TestTableNames(t *testing.T) {
	// 测试表名设置
	project := WorkflowProject{}
	permission := WorkflowProjectPermission{}
	execution := WorkflowExecution{}

	assert.Equal(t, "workflow_projects", project.TableName())
	assert.Equal(t, "workflow_project_permissions", permission.TableName())
	assert.Equal(t, "workflow_executions", execution.TableName())
}

func TestWorkflowConfigRequest_JSONHandling(t *testing.T) {
	// 测试JSON配置请求
	configData := map[string]interface{}{
		"nodes": []interface{}{
			map[string]interface{}{"id": "1", "type": "start"},
		},
		"edges": []interface{}{},
	}

	configBytes, err := json.Marshal(configData)
	assert.NoError(t, err)

	request := WorkflowConfigRequest{
		Config: json.RawMessage(configBytes),
	}

	// 验证可以正确解析
	var parsedConfig map[string]interface{}
	err = json.Unmarshal(request.Config, &parsedConfig)
	assert.NoError(t, err)
	assert.Equal(t, configData, parsedConfig)
}
