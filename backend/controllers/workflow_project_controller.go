package controllers

import (
	"encoding/json"
	"fmt"
	"strconv"

	"GoShip/backend/models"
	"GoShip/backend/services"
	"github.com/beego/beego/v2/server/web"
)

// WorkflowProjectController 工作流项目控制器
type WorkflowProjectController struct {
	web.Controller
	service *services.WorkflowProjectService
}

// Prepare 初始化
func (c *WorkflowProjectController) Prepare() {
	c.service = services.NewWorkflowProjectService()
}

// GetList 获取工作流项目列表
// @router /api/v1/workflow-projects [get]
func (c *WorkflowProjectController) GetList() {
	// 获取用户ID（这里需要从session或token中获取，暂时使用固定值）
	userID := c.getUserID()
	if userID == 0 {
		c.jsonError(401, "未授权访问")
		return
	}

	// 解析查询参数
	filter := &models.WorkflowProjectFilter{}
	if err := c.<PERSON>rseForm(filter); err != nil {
		c.jsonError(400, "参数解析失败: "+err.Error())
		return
	}

	// 设置默认值
	if filter.Page <= 0 {
		filter.Page = 1
	}
	if filter.Size <= 0 {
		filter.Size = 20
	}
	if filter.Size > 100 {
		filter.Size = 100 // 限制最大页面大小
	}

	// 获取项目列表
	result, err := c.service.GetProjects(filter, userID)
	if err != nil {
		c.jsonError(500, "获取项目列表失败: "+err.Error())
		return
	}

	c.jsonSuccess(result)
}

// Get 获取单个工作流项目
// @router /api/v1/workflow-projects/:id [get]
func (c *WorkflowProjectController) Get() {
	userID := c.getUserID()
	if userID == 0 {
		c.jsonError(401, "未授权访问")
		return
	}

	// 解析项目ID
	id, err := c.getProjectID()
	if err != nil {
		c.jsonError(400, "项目ID无效")
		return
	}

	// 获取项目
	project, err := c.service.GetProject(id, userID)
	if err != nil {
		c.jsonError(404, err.Error())
		return
	}

	c.jsonSuccess(project.ToResponse())
}

// Post 创建工作流项目
// @router /api/v1/workflow-projects [post]
func (c *WorkflowProjectController) Post() {
	userID := c.getUserID()
	if userID == 0 {
		c.jsonError(401, "未授权访问")
		return
	}

	// 解析请求体
	var req models.WorkflowProjectRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		c.jsonError(400, "请求体格式错误: "+err.Error())
		return
	}

	// 验证请求参数
	if err := c.validateProjectRequest(&req); err != nil {
		c.jsonError(400, err.Error())
		return
	}

	// 创建项目
	project, err := c.service.CreateProject(&req, userID)
	if err != nil {
		c.jsonError(500, err.Error())
		return
	}

	c.Ctx.Output.SetStatus(201)
	c.jsonSuccess(project.ToResponse())
}

// Put 更新工作流项目
// @router /api/v1/workflow-projects/:id [put]
func (c *WorkflowProjectController) Put() {
	userID := c.getUserID()
	if userID == 0 {
		c.jsonError(401, "未授权访问")
		return
	}

	// 解析项目ID
	id, err := c.getProjectID()
	if err != nil {
		c.jsonError(400, "项目ID无效")
		return
	}

	// 解析请求体
	var req models.WorkflowProjectRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		c.jsonError(400, "请求体格式错误: "+err.Error())
		return
	}

	// 验证请求参数
	if err := c.validateProjectRequest(&req); err != nil {
		c.jsonError(400, err.Error())
		return
	}

	// 更新项目
	project, err := c.service.UpdateProject(id, &req, userID)
	if err != nil {
		c.jsonError(500, err.Error())
		return
	}

	c.jsonSuccess(project.ToResponse())
}

// Delete 删除工作流项目
// @router /api/v1/workflow-projects/:id [delete]
func (c *WorkflowProjectController) Delete() {
	userID := c.getUserID()
	if userID == 0 {
		c.jsonError(401, "未授权访问")
		return
	}

	// 解析项目ID
	id, err := c.getProjectID()
	if err != nil {
		c.jsonError(400, "项目ID无效")
		return
	}

	// 删除项目
	err = c.service.DeleteProject(id, userID)
	if err != nil {
		c.jsonError(500, err.Error())
		return
	}

	c.jsonSuccess(map[string]string{"message": "项目删除成功"})
}

// GetWorkflow 获取工作流配置
// @router /api/v1/workflow-projects/:id/workflow [get]
func (c *WorkflowProjectController) GetWorkflow() {
	userID := c.getUserID()
	if userID == 0 {
		c.jsonError(401, "未授权访问")
		return
	}

	// 解析项目ID
	id, err := c.getProjectID()
	if err != nil {
		c.jsonError(400, "项目ID无效")
		return
	}

	// 获取工作流配置
	config, err := c.service.GetWorkflowConfig(id, userID)
	if err != nil {
		c.jsonError(404, err.Error())
		return
	}

	if config == nil {
		config = map[string]interface{}{
			"nodes": []interface{}{},
			"edges": []interface{}{},
		}
	}

	c.jsonSuccess(map[string]interface{}{
		"workflow_config": config,
	})
}

// SaveWorkflow 保存工作流配置
// @router /api/v1/workflow-projects/:id/workflow [put]
func (c *WorkflowProjectController) SaveWorkflow() {
	userID := c.getUserID()
	if userID == 0 {
		c.jsonError(401, "未授权访问")
		return
	}

	// 解析项目ID
	id, err := c.getProjectID()
	if err != nil {
		c.jsonError(400, "项目ID无效")
		return
	}

	// 解析请求体
	var req models.WorkflowConfigRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		c.jsonError(400, "请求体格式错误: "+err.Error())
		return
	}

	// 解析配置
	var config map[string]interface{}
	if err := json.Unmarshal(req.Config, &config); err != nil {
		c.jsonError(400, "工作流配置格式错误: "+err.Error())
		return
	}

	// 保存工作流配置
	err = c.service.SaveWorkflowConfig(id, config, userID)
	if err != nil {
		c.jsonError(500, err.Error())
		return
	}

	c.jsonSuccess(map[string]string{"message": "工作流配置保存成功"})
}

// ExecuteWorkflow 执行工作流
// @router /api/v1/workflow-projects/:id/execute [post]
func (c *WorkflowProjectController) ExecuteWorkflow() {
	userID := c.getUserID()
	if userID == 0 {
		c.jsonError(401, "未授权访问")
		return
	}

	// 解析项目ID
	id, err := c.getProjectID()
	if err != nil {
		c.jsonError(400, "项目ID无效")
		return
	}

	// 执行工作流
	execution, err := c.service.ExecuteWorkflow(id, userID)
	if err != nil {
		c.jsonError(500, err.Error())
		return
	}

	c.Ctx.Output.SetStatus(202) // Accepted
	c.jsonSuccess(execution)
}

// 辅助方法

// getUserID 获取用户ID（从session或token中获取）
func (c *WorkflowProjectController) getUserID() uint64 {
	// TODO: 实现真实的用户认证逻辑
	// 这里暂时返回固定值用于测试
	return 1
}

// getProjectID 获取项目ID
func (c *WorkflowProjectController) getProjectID() (uint64, error) {
	idStr := c.Ctx.Input.Param(":id")
	return strconv.ParseUint(idStr, 10, 64)
}

// validateProjectRequest 验证项目请求参数
func (c *WorkflowProjectController) validateProjectRequest(req *models.WorkflowProjectRequest) error {
	if req.Name == "" {
		return fmt.Errorf("项目名称不能为空")
	}
	if len(req.Name) > 255 {
		return fmt.Errorf("项目名称长度不能超过255个字符")
	}

	validEnvironments := map[string]bool{
		"development": true,
		"test":        true,
		"production":  true,
	}
	if !validEnvironments[req.Environment] {
		return fmt.Errorf("无效的部署环境")
	}

	validLanguages := map[string]bool{
		"go":     true,
		"python": true,
		"vue":    true,
		"java":   true,
		"nodejs": true,
		"react":  true,
	}
	if !validLanguages[req.Language] {
		return fmt.Errorf("无效的编程语言")
	}

	validProjectTypes := map[string]bool{
		"frontend":  true,
		"backend":   true,
		"fullstack": true,
	}
	if !validProjectTypes[req.ProjectType] {
		return fmt.Errorf("无效的项目类型")
	}

	return nil
}

// jsonSuccess 返回成功响应
func (c *WorkflowProjectController) jsonSuccess(data interface{}) {
	c.Data["json"] = map[string]interface{}{
		"code":    200,
		"message": "success",
		"data":    data,
	}
	c.ServeJSON()
}

// jsonError 返回错误响应
func (c *WorkflowProjectController) jsonError(code int, message string) {
	c.Ctx.Output.SetStatus(code)
	c.Data["json"] = map[string]interface{}{
		"code":    code,
		"message": message,
		"data":    nil,
	}
	c.ServeJSON()
}
