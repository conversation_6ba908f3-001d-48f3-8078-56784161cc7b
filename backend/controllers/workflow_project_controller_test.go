package controllers

import (
	"testing"

	"GoShip/backend/models"
	"github.com/stretchr/testify/assert"
)

// 暂时注释掉HTTP测试，专注于单元测试
// 完整的集成测试需要数据库和完整的Beego环境

func TestWorkflowProjectController_ValidateProjectRequest(t *testing.T) {
	controller := &WorkflowProjectController{}

	tests := []struct {
		name    string
		req     models.WorkflowProjectRequest
		wantErr bool
	}{
		{
			name: "有效请求",
			req: models.WorkflowProjectRequest{
				Name:        "测试项目",
				Environment: "development",
				Language:    "go",
				ProjectType: "backend",
				Description: "测试描述",
			},
			wantErr: false,
		},
		{
			name: "空项目名称",
			req: models.WorkflowProjectRequest{
				Name:        "",
				Environment: "development",
				Language:    "go",
				ProjectType: "backend",
			},
			wantErr: true,
		},
		{
			name: "无效环境",
			req: models.WorkflowProjectRequest{
				Name:        "测试项目",
				Environment: "invalid",
				Language:    "go",
				ProjectType: "backend",
			},
			wantErr: true,
		},
		{
			name: "无效语言",
			req: models.WorkflowProjectRequest{
				Name:        "测试项目",
				Environment: "development",
				Language:    "invalid",
				ProjectType: "backend",
			},
			wantErr: true,
		},
		{
			name: "无效项目类型",
			req: models.WorkflowProjectRequest{
				Name:        "测试项目",
				Environment: "development",
				Language:    "go",
				ProjectType: "invalid",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := controller.validateProjectRequest(&tt.req)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
