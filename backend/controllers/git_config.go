package controllers

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"

	"GoShip/models"

	beego "github.com/beego/beego/v2/server/web"
)

// GitConfigController Git配置管理控制器
type GitConfigController struct {
	beego.Controller
}

// GetList 获取Git配置列表
func (c *GitConfigController) GetList() {
	// 获取查询参数
	page, _ := c.GetInt("page", 1)
	size, _ := c.GetInt("size", 10)
	search := c.GetString("search")

	// 参数验证
	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 10
	}

	log.Printf("获取Git配置列表: page=%d, size=%d, search=%s", page, size, search)

	// 查询数据
	configs, total, err := models.GetGitConfigs(page, size, search, "name", "asc")
	if err != nil {
		log.Printf("获取Git配置列表失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": fmt.Sprintf("获取Git配置列表失败: %v", err),
		}
		c.ServeJSON()
		return
	}

	// 清理敏感信息用于列表显示
	for i := range configs {
		if configs[i].Password != "" {
			configs[i].Password = "***"
		}
		if configs[i].SSHKey != "" {
			configs[i].SSHKey = "***"
		}
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"data":    configs,
		"total":   total,
		"page":    page,
		"size":    size,
	}
	c.ServeJSON()
}

// Post 创建Git配置
func (c *GitConfigController) Post() {
	var config models.GitConfig
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &config); err != nil {
		log.Printf("解析Git配置数据失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "无效的JSON数据",
		}
		c.ServeJSON()
		return
	}

	// 数据验证
	if err := validateGitConfig(&config); err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": err.Error(),
		}
		c.ServeJSON()
		return
	}

	log.Printf("创建Git配置: %s", config.Name)

	// 创建配置
	if err := models.CreateGitConfig(&config); err != nil {
		log.Printf("创建Git配置失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": fmt.Sprintf("创建Git配置失败: %v", err),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"message": "创建成功",
		"data":    map[string]interface{}{"id": config.ID},
	}
	c.ServeJSON()
}

// GetActiveConfigs 获取活跃的Git配置列表
func (c *GitConfigController) GetActiveConfigs() {
	configs, err := models.GetActiveGitConfigs()
	if err != nil {
		log.Printf("获取活跃Git配置列表失败: %v", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": fmt.Sprintf("获取活跃Git配置列表失败: %v", err),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"data":    configs,
	}
	c.ServeJSON()
}

// validateGitConfig 验证Git配置数据
func validateGitConfig(config *models.GitConfig) error {
	if strings.TrimSpace(config.Name) == "" {
		return fmt.Errorf("配置名称不能为空")
	}

	if strings.TrimSpace(config.RepositoryURL) == "" {
		return fmt.Errorf("仓库地址不能为空")
	}

	// 检查仓库地址是否已存在
	exists, err := models.CheckGitConfigRepositoryExists(config.RepositoryURL, config.ID)
	if err != nil {
		return fmt.Errorf("检查仓库地址失败: %v", err)
	}
	if exists {
		return fmt.Errorf("该仓库地址已存在，不能重复添加")
	}

	if strings.TrimSpace(config.DefaultBranch) == "" {
		config.DefaultBranch = "main"
	}

	if config.Status == "" {
		config.Status = "active"
	}

	return nil
}