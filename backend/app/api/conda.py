"""
Conda 环境管理 API
提供 conda 环境的检测、创建、管理功能
"""

import subprocess
import json
import logging
from typing import Dict, List, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/conda", tags=["conda"])


class EnvironmentCheckRequest(BaseModel):
    """环境检测请求模型"""
    env_name: str


class EnvironmentCheckResponse(BaseModel):
    """环境检测响应模型"""
    exists: bool
    python_version: Optional[str] = None
    packages_count: Optional[int] = None
    location: Optional[str] = None


class EnvironmentInfo(BaseModel):
    """环境信息模型"""
    name: str
    python_version: str
    location: str
    packages: List[str]


async def run_conda_command(command: List[str]) -> Dict:
    """
    执行 conda 命令并返回结果
    
    Args:
        command: conda 命令列表
        
    Returns:
        命令执行结果
        
    Raises:
        HTTPException: 命令执行失败时抛出
    """
    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode != 0:
            logger.error(f"Conda command failed: {' '.join(command)}")
            logger.error(f"Error output: {result.stderr}")
            raise HTTPException(
                status_code=500,
                detail=f"Conda command failed: {result.stderr}"
            )
        
        # 尝试解析 JSON 输出
        try:
            return json.loads(result.stdout)
        except json.JSONDecodeError:
            # 如果不是 JSON 格式，返回原始文本
            return {"output": result.stdout.strip()}
            
    except subprocess.TimeoutExpired:
        raise HTTPException(
            status_code=408,
            detail="Conda command timed out"
        )
    except Exception as e:
        logger.error(f"Error running conda command: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error running conda command: {str(e)}"
        )


@router.post("/check-environment", response_model=EnvironmentCheckResponse)
async def check_environment(request: EnvironmentCheckRequest):
    """
    检测指定的 conda 环境是否存在
    
    Args:
        request: 包含环境名称的请求
        
    Returns:
        环境检测结果
    """
    try:
        # 获取所有 conda 环境列表
        envs_info = await run_conda_command(["conda", "env", "list", "--json"])
        
        # 检查指定环境是否存在
        env_exists = False
        env_location = None
        
        for env in envs_info.get("envs", []):
            env_name = env.split("/")[-1] if "/" in env else env
            if env_name == request.env_name:
                env_exists = True
                env_location = env
                break
        
        response = EnvironmentCheckResponse(exists=env_exists, location=env_location)
        
        # 如果环境存在，获取更多信息
        if env_exists:
            try:
                # 获取环境中的 Python 版本
                python_info = await run_conda_command([
                    "conda", "list", "-n", request.env_name, "python", "--json"
                ])
                
                if python_info and len(python_info) > 0:
                    python_package = python_info[0]
                    response.python_version = python_package.get("version", "unknown")
                
                # 获取环境中的包数量
                packages_info = await run_conda_command([
                    "conda", "list", "-n", request.env_name, "--json"
                ])
                response.packages_count = len(packages_info) if packages_info else 0
                
            except Exception as e:
                logger.warning(f"Failed to get detailed environment info: {e}")
                # 即使获取详细信息失败，仍然返回环境存在的基本信息
        
        return response
        
    except Exception as e:
        logger.error(f"Error checking environment {request.env_name}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to check environment: {str(e)}"
        )


@router.get("/environments", response_model=List[str])
async def list_environments():
    """
    获取所有可用的 conda 环境列表
    
    Returns:
        环境名称列表
    """
    try:
        envs_info = await run_conda_command(["conda", "env", "list", "--json"])
        
        env_names = []
        for env in envs_info.get("envs", []):
            env_name = env.split("/")[-1] if "/" in env else env
            if env_name and env_name != "base":  # 排除 base 环境
                env_names.append(env_name)
        
        return sorted(env_names)
        
    except Exception as e:
        logger.error(f"Error listing environments: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list environments: {str(e)}"
        )


@router.get("/environment/{env_name}", response_model=EnvironmentInfo)
async def get_environment_info(env_name: str):
    """
    获取指定环境的详细信息
    
    Args:
        env_name: 环境名称
        
    Returns:
        环境详细信息
    """
    try:
        # 首先检查环境是否存在
        check_result = await check_environment(EnvironmentCheckRequest(env_name=env_name))
        
        if not check_result.exists:
            raise HTTPException(
                status_code=404,
                detail=f"Environment '{env_name}' not found"
            )
        
        # 获取环境中的所有包
        packages_info = await run_conda_command([
            "conda", "list", "-n", env_name, "--json"
        ])
        
        packages = [f"{pkg['name']}=={pkg['version']}" for pkg in packages_info]
        
        return EnvironmentInfo(
            name=env_name,
            python_version=check_result.python_version or "unknown",
            location=check_result.location or "unknown",
            packages=packages
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting environment info for {env_name}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get environment info: {str(e)}"
        )
