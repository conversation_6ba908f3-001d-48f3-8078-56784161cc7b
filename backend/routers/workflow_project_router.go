package routers

import (
	"GoShip/backend/controllers"
	"github.com/beego/beego/v2/server/web"
)

// InitWorkflowProjectRoutes 初始化工作流项目路由
func InitWorkflowProjectRoutes() {
	// 工作流项目管理路由
	web.Router("/api/v1/workflow-projects", &controllers.WorkflowProjectController{}, "get:GetList;post:Post")
	web.Router("/api/v1/workflow-projects/:id([0-9]+)", &controllers.WorkflowProjectController{}, "get:Get;put:Put;delete:Delete")
	
	// 工作流配置路由
	web.Router("/api/v1/workflow-projects/:id([0-9]+)/workflow", &controllers.WorkflowProjectController{}, "get:GetWorkflow;put:SaveWorkflow")
	
	// 工作流执行路由
	web.Router("/api/v1/workflow-projects/:id([0-9]+)/execute", &controllers.WorkflowProjectController{}, "post:ExecuteWorkflow")
}
