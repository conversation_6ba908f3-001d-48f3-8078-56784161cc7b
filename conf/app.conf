appname = goship
httpport = 8080
autorender = false
copyrequestbody = true
EnableDocs = true
runmode = dev

# MySQL配置
mysql.host = *************
mysql.port = 3306
mysql.user = root
mysql.password = Pixm2022
mysql.database = goship-test
mysql.charset = utf8
# 连接池配置
mysql.maxIdle = 5
mysql.maxOpen = 10

artifact_path = ./artifact

source_root = ./workspace

# 安全配置
# 用于加密数据库中存储的敏感信息（SSH密码、私钥等）
# 生产环境中应该使用环境变量或安全的密钥管理系统
encryption.key = goship-secure-encryption-key-2025-v2.0

# 主机管理安全配置
host.max_connection_timeout = 30
host.max_test_retries = 3
host.allowed_auth_types = password,private_key
