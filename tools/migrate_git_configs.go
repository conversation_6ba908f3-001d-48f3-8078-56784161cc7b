package main

import (
	"database/sql"
	"fmt"
	"log"
	"net/url"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

// ProjectGitInfo 项目Git信息
type ProjectGitInfo struct {
	ID          int64
	Name        string
	GitURL      string
	GitUsername string
	GitPassword string
	GitVersion  string
}

// GitConfig Git配置结构
type GitConfig struct {
	Name          string
	RepositoryURL string
	AuthType      string
	Username      string
	Password      string
	DefaultBranch string
	Description   string
	Status        string
}

func main() {
	// 数据库连接配置
	dsn := "root:Pixm2022@tcp(*************:3306)/goship-test?charset=utf8mb4&parseTime=True&loc=Local"
	
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	// 测试数据库连接
	if err := db.Ping(); err != nil {
		log.Fatalf("数据库连接测试失败: %v", err)
	}

	log.Println("数据库连接成功")

	// 获取现有项目的Git配置
	projects, err := getProjectGitConfigs(db)
	if err != nil {
		log.Fatalf("获取项目Git配置失败: %v", err)
	}

	log.Printf("找到 %d 个项目的Git配置", len(projects))

	// 生成Git配置管理数据
	gitConfigs := generateGitConfigs(projects)
	
	log.Printf("生成了 %d 个Git配置", len(gitConfigs))

	// 插入Git配置到数据库
	if err := insertGitConfigs(db, gitConfigs); err != nil {
		log.Fatalf("插入Git配置失败: %v", err)
	}

	log.Println("Git配置迁移完成！")
}

// getProjectGitConfigs 获取项目的Git配置信息
func getProjectGitConfigs(db *sql.DB) ([]ProjectGitInfo, error) {
	query := `
		SELECT id, name, git_url, git_username, git_password, git_version 
		FROM projects 
		WHERE git_url != '' AND git_url != '1'
		ORDER BY id
	`
	
	rows, err := db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询项目失败: %v", err)
	}
	defer rows.Close()

	var projects []ProjectGitInfo
	for rows.Next() {
		var p ProjectGitInfo
		err := rows.Scan(&p.ID, &p.Name, &p.GitURL, &p.GitUsername, &p.GitPassword, &p.GitVersion)
		if err != nil {
			log.Printf("扫描项目数据失败: %v", err)
			continue
		}
		projects = append(projects, p)
	}

	return projects, nil
}

// generateGitConfigs 根据项目信息生成Git配置
func generateGitConfigs(projects []ProjectGitInfo) []GitConfig {
	// 使用map去重，相同仓库地址和认证信息的只保留一个
	configMap := make(map[string]*GitConfig)
	
	for _, project := range projects {
		// 解析Git版本信息获取默认分支
		defaultBranch := parseDefaultBranch(project.GitVersion)
		
		// 生成配置键（用于去重）
		configKey := fmt.Sprintf("%s|%s|%s", project.GitURL, project.GitUsername, project.GitPassword)
		
		if existingConfig, exists := configMap[configKey]; exists {
			// 如果已存在相同配置，更新描述信息
			existingConfig.Description += fmt.Sprintf(", %s", project.Name)
		} else {
			// 创建新的Git配置
			config := &GitConfig{
				Name:          generateConfigName(project.GitURL, project.GitUsername),
				RepositoryURL: project.GitURL,
				AuthType:      determineAuthType(project.GitURL, project.GitUsername, project.GitPassword),
				Username:      project.GitUsername,
				Password:      project.GitPassword,
				DefaultBranch: defaultBranch,
				Description:   fmt.Sprintf("从项目 %s 自动生成", project.Name),
				Status:        "active",
			}
			configMap[configKey] = config
		}
	}
	
	// 转换为切片
	var configs []GitConfig
	for _, config := range configMap {
		configs = append(configs, *config)
	}
	
	return configs
}

// parseDefaultBranch 解析Git版本信息获取默认分支
func parseDefaultBranch(gitVersion string) string {
	if gitVersion == "" {
		return "main"
	}
	
	// 格式: "branch:master" 或 "tag:v1.0.0"
	parts := strings.Split(gitVersion, ":")
	if len(parts) == 2 && parts[0] == "branch" {
		branch := strings.TrimSpace(parts[1])
		if branch != "" {
			return branch
		}
	}
	
	return "main"
}

// generateConfigName 生成配置名称
func generateConfigName(gitURL, username string) string {
	// 从Git URL中提取仓库名
	u, err := url.Parse(gitURL)
	if err != nil {
		return fmt.Sprintf("Git配置_%s", username)
	}
	
	// 提取路径的最后一部分作为仓库名
	pathParts := strings.Split(strings.Trim(u.Path, "/"), "/")
	if len(pathParts) > 0 {
		repoName := pathParts[len(pathParts)-1]
		// 移除.git后缀
		repoName = strings.TrimSuffix(repoName, ".git")
		
		if username != "" {
			return fmt.Sprintf("%s_%s", repoName, username)
		}
		return repoName
	}
	
	return fmt.Sprintf("Git配置_%s", username)
}

// determineAuthType 确定认证类型
func determineAuthType(gitURL, username, password string) string {
	if username == "" && password == "" {
		return "none"
	}
	
	if username != "" && password != "" {
		// 如果密码看起来像token（通常以ghp_、glpat-等开头）
		if strings.HasPrefix(password, "ghp_") || 
		   strings.HasPrefix(password, "glpat-") || 
		   strings.HasPrefix(password, "gho_") {
			return "token"
		}
		return "username"
	}
	
	return "none"
}

// insertGitConfigs 插入Git配置到数据库
func insertGitConfigs(db *sql.DB, configs []GitConfig) error {
	// 检查表是否存在，如果不存在则创建
	if err := ensureGitConfigTable(db); err != nil {
		return fmt.Errorf("确保Git配置表存在失败: %v", err)
	}
	
	// 清空现有数据（可选）
	log.Println("清空现有Git配置数据...")
	if _, err := db.Exec("DELETE FROM git_configs"); err != nil {
		log.Printf("清空现有数据失败: %v", err)
	}
	
	// 插入新配置
	insertQuery := `
		INSERT INTO git_configs (
			name, repository_url, auth_type, username, password, 
			default_branch, description, status, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`
	
	stmt, err := db.Prepare(insertQuery)
	if err != nil {
		return fmt.Errorf("准备插入语句失败: %v", err)
	}
	defer stmt.Close()
	
	now := time.Now()
	for i, config := range configs {
		_, err := stmt.Exec(
			config.Name,
			config.RepositoryURL,
			config.AuthType,
			config.Username,
			config.Password,
			config.DefaultBranch,
			config.Description,
			config.Status,
			now,
			now,
		)
		if err != nil {
			log.Printf("插入第 %d 个配置失败: %v", i+1, err)
			continue
		}
		log.Printf("插入配置: %s", config.Name)
	}
	
	return nil
}

// ensureGitConfigTable 确保Git配置表存在
func ensureGitConfigTable(db *sql.DB) error {
	createTableSQL := `
		CREATE TABLE IF NOT EXISTS git_configs (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			name VARCHAR(255) NOT NULL UNIQUE COMMENT '配置名称',
			repository_url TEXT NOT NULL COMMENT '仓库地址',
			auth_type ENUM('none', 'token', 'ssh', 'username') DEFAULT 'none' COMMENT '认证类型',
			username VARCHAR(255) COMMENT '用户名',
			password TEXT COMMENT '密码/Token',
			ssh_key TEXT COMMENT 'SSH私钥',
			default_branch VARCHAR(255) DEFAULT 'main' COMMENT '默认分支',
			description TEXT COMMENT '描述',
			status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
			last_test_time DATETIME NULL COMMENT '最后测试时间',
			last_test_result ENUM('success', 'failed') NULL COMMENT '最后测试结果',
			created_at DATETIME NOT NULL COMMENT '创建时间',
			updated_at DATETIME NOT NULL COMMENT '更新时间',
			user_id BIGINT NULL COMMENT '用户ID',
			INDEX idx_status (status),
			INDEX idx_auth_type (auth_type),
			INDEX idx_created_at (created_at)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Git配置管理表';
	`

	_, err := db.Exec(createTableSQL)
	return err
}
