<template>
  <!-- 🎯 仪表板功能卡片 -->
  <div 
    :class="cardClasses"
    @click="handleClick"
  >
    <!-- 图标和徽章 -->
    <div class="flex items-center justify-between mb-4">
      <div :class="iconClasses">
        <Icon :name="icon" :size="24" class="text-white" />
      </div>
      <div v-if="badge" :class="badgeClasses">
        {{ badge }}
      </div>
    </div>

    <!-- 标题和描述 -->
    <h3 class="dashboard-card-title">{{ title }}</h3>
    <p class="dashboard-card-description">{{ description }}</p>

    <!-- 统计信息 -->
    <div v-if="stats" class="dashboard-card-stats">
      <div class="flex items-center justify-between">
        <div>
          <div class="text-2xl font-bold text-romantic-600">{{ stats.value }}</div>
          <div class="text-gray-500 text-sm">{{ stats.label }}</div>
        </div>
        <Icon v-if="stats.icon" :name="stats.icon" :size="20" class="text-romantic-400" />
      </div>
    </div>

    <!-- 快速操作 -->
    <div v-if="quickActions && quickActions.length > 0" class="mb-4">
      <div class="flex flex-wrap gap-2">
        <button 
          v-for="action in quickActions" 
          :key="action.name"
          class="btn btn-ghost btn-xs text-gray-600"
          @click.stop="handleQuickAction(action)"
        >
          <Icon v-if="action.icon" :name="action.icon" :size="14" />
          {{ action.name }}
        </button>
      </div>
    </div>

    <!-- 主要操作按钮 -->
    <div class="dashboard-card-actions">
      <button :class="buttonClasses" @click.stop="handleMainAction">
        <Icon v-if="actionIcon" :name="actionIcon" :size="16" />
        {{ actionText }}
      </button>
    </div>

    <!-- 状态指示器 -->
    <div v-if="status" class="absolute top-4 right-4">
      <div :class="statusClasses"></div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import Icon from './Icon.vue'

const props = defineProps({
  // 基础信息
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    required: true
  },
  
  // 主要操作
  actionText: {
    type: String,
    default: '进入管理'
  },
  actionIcon: String,
  
  // 统计信息
  stats: {
    type: Object,
    default: null
  },
  
  // 徽章
  badge: String,
  
  // 快速操作
  quickActions: {
    type: Array,
    default: () => []
  },
  
  // 状态
  status: {
    type: String,
    validator: (value) => ['online', 'offline', 'warning', 'error'].includes(value)
  },
  
  // 样式变体
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'primary', 'secondary', 'accent'].includes(value)
  },
  
  // 是否可点击
  clickable: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['click', 'action', 'quick-action'])

// 计算样式类
const cardClasses = computed(() => {
  const base = 'dashboard-card relative'
  const clickable = props.clickable ? 'cursor-pointer' : ''
  return `${base} ${clickable}`
})

const iconClasses = computed(() => {
  const variants = {
    default: 'dashboard-card-icon bg-gradient-to-r from-romantic-400 to-dreamy-400',
    primary: 'dashboard-card-icon bg-gradient-to-r from-blue-400 to-cyan-400',
    secondary: 'dashboard-card-icon bg-gradient-to-r from-purple-400 to-pink-400',
    accent: 'dashboard-card-icon bg-gradient-to-r from-green-400 to-emerald-400'
  }
  return variants[props.variant]
})

const badgeClasses = computed(() => {
  const variants = {
    default: 'badge badge-primary badge-sm',
    primary: 'badge badge-info badge-sm',
    secondary: 'badge badge-secondary badge-sm',
    accent: 'badge badge-success badge-sm'
  }
  return variants[props.variant]
})

const buttonClasses = computed(() => {
  const variants = {
    default: 'btn btn-romantic btn-sm',
    primary: 'btn btn-primary btn-sm',
    secondary: 'btn btn-secondary btn-sm',
    accent: 'btn btn-success btn-sm'
  }
  return variants[props.variant]
})

const statusClasses = computed(() => {
  if (!props.status) return ''
  
  const statuses = {
    online: 'w-3 h-3 bg-green-400 rounded-full animate-pulse',
    offline: 'w-3 h-3 bg-gray-400 rounded-full',
    warning: 'w-3 h-3 bg-yellow-400 rounded-full animate-pulse',
    error: 'w-3 h-3 bg-red-400 rounded-full animate-pulse'
  }
  return statuses[props.status]
})

// 事件处理
const handleClick = () => {
  if (props.clickable) {
    emit('click')
  }
}

const handleMainAction = () => {
  emit('action')
}

const handleQuickAction = (action) => {
  emit('quick-action', action)
}
</script>

<style scoped>
.dashboard-card {
  position: relative;
}

.dashboard-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(236, 72, 153, 0.05), rgba(168, 85, 247, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  border-radius: inherit;
}

.dashboard-card:hover::after {
  opacity: 1;
}
</style>
