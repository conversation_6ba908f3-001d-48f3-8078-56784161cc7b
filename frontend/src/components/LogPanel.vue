<template>
  <!-- 日志面板暂时简化，待后续使用DaisyUI重新实现 -->
  <div v-if="visible" class="fixed inset-0 glass-backdrop flex items-center justify-center z-50" @click="onClose">
    <div class="glass-card rounded-lg p-4 md:p-6 max-w-5xl w-full mx-2 md:mx-4 max-h-[90vh] overflow-hidden shadow-large" @click.stop>
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold">部署日志</h3>
        <button class="btn btn-sm btn-circle" @click="onClose">
          <Icon name="close" :size="16" />
        </button>
      </div>
      <div class="bg-gray-900 text-green-400 p-3 md:p-4 rounded font-mono text-xs md:text-sm h-[60vh] modal-scroll-container overflow-y-auto">
        <div v-if="!logs || logs.length === 0" class="text-gray-500 flex items-center justify-center h-full">
          <div class="text-center">
            <div class="animate-pulse mb-2">⏳</div>
            <div>等待日志输出...</div>
          </div>
        </div>
        <div v-else class="space-y-1">
          <div v-for="(log, index) in logs" :key="index"
               class="leading-relaxed hover:bg-gray-800 px-2 py-1 rounded transition-colors"
               :class="getLogClass(log)">
            <span class="text-gray-500 mr-2">[{{ String(index + 1).padStart(3, '0') }}]</span>
            {{ log }}
          </div>
        </div>
      </div>
      <div class="flex flex-col md:flex-row md:justify-between md:items-center mt-4 pt-4 border-t border-gray-200 gap-3 md:gap-0">
        <div class="text-sm text-gray-500 text-center md:text-left">
          共 {{ logs?.length || 0 }} 条日志
        </div>
        <div class="flex gap-2 justify-center md:justify-end">
          <button class="btn btn-warning btn-sm" @click="$emit('clear')">
            <Icon name="delete" :size="16" />
            <span class="hidden sm:inline">清空日志</span>
            <span class="sm:hidden">清空</span>
          </button>
          <button class="btn btn-info btn-sm" @click="$emit('download')">
            <Icon name="download" :size="16" />
            <span class="hidden sm:inline">下载日志</span>
            <span class="sm:hidden">下载</span>
          </button>
          <button class="btn btn-ghost btn-sm" @click="onClose">
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// import { Delete, Refresh, Download } from '@element-plus/icons-vue'
import { toRef } from 'vue'
import Icon from './Icon.vue'
import { useScrollLock } from '../composables/useScrollLock'

const props = defineProps({
  visible: Boolean,
  logs: Array
})
const emit = defineEmits(['update:visible', 'clear', 'refresh', 'download'])

// 使用滚动锁定
const visibleRef = toRef(props, 'visible')
useScrollLock(visibleRef)

const getLogClass = (log) => {
  if (!log) return ''
  
  const logLower = log.toLowerCase()
  if (logLower.includes('success') || logLower.includes('成功')) {
    return 'log-success'
  } else if (logLower.includes('error') || logLower.includes('错误') || logLower.includes('失败')) {
    return 'log-error'
  } else if (logLower.includes('info') || logLower.includes('信息')) {
    return 'log-info'
  } else if (logLower.includes('start') || logLower.includes('开始')) {
    return 'log-start'
  }
  return ''
}

const onClose = () => emit('update:visible', false)
</script>

<style scoped>
.deploy-logs {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #FCF6E5;
  color: #697A82;
  font-family: 'Courier New', Courier, monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
  border-radius: 4px;
}

.empty-logs {
  text-align: center;
  color: #697A82;
  padding: 20px;
}

.log-item {
  margin-bottom: 8px;
  white-space: pre-wrap;
  word-break: break-all;
  width: 100%;
}

.log-success {
  color: #10b981;
  font-weight: 500;
}

.log-error {
  color: #ef4444;
  font-weight: 500;
}

.log-info {
  color: #06b6d4;
}

.log-start {
  color: #3b82f6;
  font-weight: 500;
}

.log-actions {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style> 