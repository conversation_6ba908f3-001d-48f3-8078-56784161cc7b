<template>
  <!-- <PERSON><PERSON> Modal -->
  <div v-if="visible" class="modal modal-open">
    <div class="modal-box modal-scroll-container w-11/12 max-w-3xl max-h-[90vh] overflow-y-auto">
      <!-- 标题 -->
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-xl font-bold text-gray-800">
          {{ isEdit ? '编辑主机' : '新建主机' }}
        </h3>
        <button
          type="button"
          class="btn btn-ghost btn-sm btn-circle"
          @click="handleCancel"
        >
          ✕
        </button>
      </div>

      <form @submit.prevent="handleSubmit" class="space-y-6">
        <!-- 基本信息卡片 -->
        <div class="card bg-base-100 border border-base-300">
          <div class="card-body p-4">
            <h4 class="card-title text-base mb-4 text-gray-700">基本信息</h4>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">主机名称 <span class="text-error">*</span></span>
                </label>
                <input
                  v-model="form.name"
                  type="text"
                  placeholder="请输入主机名称"
                  class="input input-bordered input-romantic w-full"
                  required
                />
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">IP地址 <span class="text-error">*</span></span>
                </label>
                <input
                  v-model="form.ip"
                  type="text"
                  placeholder="*************"
                  class="input input-bordered input-romantic w-full"
                  required
                />
              </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">SSH端口</span>
                </label>
                <input
                  v-model.number="form.port"
                  type="number"
                  placeholder="22"
                  class="input input-bordered input-romantic w-full"
                  min="1"
                  max="65535"
                />
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">用户名 <span class="text-error">*</span></span>
                </label>
                <input
                  v-model="form.username"
                  type="text"
                  placeholder="root"
                  class="input input-bordered input-romantic w-full"
                  required
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 认证配置卡片 -->
        <div class="card bg-base-100 border border-base-300">
          <div class="card-body p-4">
            <h4 class="card-title text-base mb-4 text-gray-700">认证配置</h4>

            <!-- 认证方式选择 -->
            <div class="form-control mb-4">
              <label class="label">
                <span class="label-text font-medium">认证方式</span>
              </label>
              <div class="flex gap-6">
                <label class="label cursor-pointer gap-3 p-3 rounded-lg border-2 transition-all duration-200"
                       :class="form.auth_type === 'password' ? 'border-romantic-400 bg-romantic-50' : 'border-gray-200 hover:border-romantic-300'">
                  <input
                    v-model="form.auth_type"
                    type="radio"
                    value="password"
                    class="radio radio-romantic"
                  />
                  <div class="flex items-center gap-2">
                    <Icon name="shield" :size="16" class="text-romantic-500" />
                    <span class="label-text font-medium">密码认证</span>
                  </div>
                </label>
                <label class="label cursor-pointer gap-3 p-3 rounded-lg border-2 transition-all duration-200"
                       :class="form.auth_type === 'private_key' ? 'border-romantic-400 bg-romantic-50' : 'border-gray-200 hover:border-romantic-300'">
                  <input
                    v-model="form.auth_type"
                    type="radio"
                    value="private_key"
                    class="radio radio-romantic"
                  />
                  <div class="flex items-center gap-2">
                    <Icon name="key" :size="16" class="text-romantic-500" />
                    <span class="label-text font-medium">私钥认证</span>
                  </div>
                </label>
              </div>
            </div>

            <!-- 密码认证 -->
            <div v-if="form.auth_type === 'password'" class="form-control">
              <label class="label">
                <span class="label-text font-medium">密码 <span class="text-error">*</span></span>
              </label>
              <input
                v-model="form.password"
                type="password"
                placeholder="请输入SSH密码"
                class="input input-bordered input-romantic w-full"
                :required="form.auth_type === 'password'"
              />
            </div>

            <!-- 私钥认证 -->
            <div v-if="form.auth_type === 'private_key'" class="form-control">
              <label class="label">
                <span class="label-text font-medium">私钥内容 <span class="text-error">*</span></span>
              </label>
              <textarea
                v-model="form.private_key"
                placeholder="请粘贴私钥内容，例如：&#10;-----BEGIN OPENSSH PRIVATE KEY-----&#10;b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAFwAAAAdzc2gtcn&#10;..."
                class="textarea textarea-bordered textarea-romantic h-32 w-full font-mono text-sm"
                :required="form.auth_type === 'private_key'"
              ></textarea>
            </div>
          </div>
        </div>

        <!-- 环境配置卡片 -->
        <div class="card bg-base-100 border border-base-300">
          <div class="card-body p-4">
            <h4 class="card-title text-base mb-4 text-gray-700">环境配置</h4>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">环境类型</span>
                </label>
                <select v-model="form.environment" class="select select-bordered select-romantic w-full">
                  <option value="test">测试环境</option>
                  <option value="prod">生产环境</option>
                </select>
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">主机状态</span>
                </label>
                <select v-model="form.status" class="select select-bordered select-romantic w-full">
                  <option value="active">活跃</option>
                  <option value="inactive">停用</option>
                </select>
              </div>
            </div>

            <!-- 描述 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">主机描述</span>
              </label>
              <textarea
                v-model="form.description"
                placeholder="请输入主机描述信息（可选）"
                class="textarea textarea-bordered textarea-romantic h-20 w-full"
              ></textarea>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-end gap-3 pt-4 border-t border-base-300">
          <button
            type="button"
            class="btn btn-ghost"
            @click="handleCancel"
          >
            取消
          </button>
          <button
            type="button"
            class="btn btn-outline btn-romantic"
            @click="testConnection"
            :disabled="testing"
          >
            <span v-if="testing" class="loading loading-spinner loading-sm"></span>
            {{ testing ? '测试中...' : '测试连接' }}
          </button>
          <button
            type="submit"
            class="btn btn-romantic"
            :disabled="loading"
          >
            <span v-if="loading" class="loading loading-spinner loading-sm"></span>
            {{ loading ? '保存中...' : '保存' }}
          </button>
        </div>
      </form>
    </div>

    <!-- 点击背景关闭 -->
    <div class="modal-backdrop" @click="handleCancel"></div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, toRef } from 'vue'
import Icon from './Icon.vue'
import { testHostConnectionTemp } from '../api/host.js'
import { useScrollLock } from '../composables/useScrollLock'

const props = defineProps({
  visible: Boolean,
  hostData: Object,
  isEdit: Boolean
})

const emit = defineEmits(['confirm', 'cancel'])

// 使用滚动锁定
const visibleRef = toRef(props, 'visible')
useScrollLock(visibleRef)

const loading = ref(false)
const testing = ref(false)

const form = reactive({
  name: '',
  ip: '',
  port: 22,
  username: '',
  auth_type: 'password',
  password: '',
  private_key: '',
  environment: 'test',
  description: '',
  status: 'active'
})

// 使用Toast替代原始alert弹窗

// 监听hostData变化，填充表单
watch(() => props.hostData, (newData) => {
  if (newData) {
    Object.assign(form, {
      name: newData.name || '',
      ip: newData.ip || '',
      port: newData.port || 22,
      username: newData.username || '',
      auth_type: newData.auth_type || 'password',
      password: newData.password || '',
      private_key: newData.private_key || '',
      environment: newData.environment || 'test',
      description: newData.description || '',
      status: newData.status || 'active'
    })
  } else {
    // 重置表单
    Object.assign(form, {
      name: '',
      ip: '',
      port: 22,
      username: '',
      auth_type: 'password',
      password: '',
      private_key: '',
      environment: 'test',
      description: '',
      status: 'active'
    })
  }
}, { immediate: true })

// 提交表单
const handleSubmit = () => {
  // 基本验证
  if (!form.name.trim()) {
    showMessage('请输入主机名称', 'error')
    return
  }
  if (!form.ip.trim()) {
    showMessage('请输入IP地址', 'error')
    return
  }
  if (!form.username.trim()) {
    showMessage('请输入用户名', 'error')
    return
  }
  if (form.auth_type === 'password' && !form.password.trim()) {
    showMessage('请输入密码', 'error')
    return
  }
  if (form.auth_type === 'private_key' && !form.private_key.trim()) {
    showMessage('请输入私钥', 'error')
    return
  }

  loading.value = true

  // 模拟保存延迟
  setTimeout(() => {
    emit('confirm', { ...form })
    loading.value = false
  }, 500)
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
}

// 测试连接
const testConnection = async () => {
  // 基本验证
  if (!form.ip.trim() || !form.username.trim()) {
    showMessage('请先填写IP地址和用户名', 'error')
    return
  }

  if (form.auth_type === 'password' && !form.password.trim()) {
    showMessage('请输入密码', 'error')
    return
  }

  if (form.auth_type === 'private_key' && !form.private_key.trim()) {
    showMessage('请输入私钥', 'error')
    return
  }

  testing.value = true

  try {
    // 模拟测试连接
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 模拟随机结果
    const success = Math.random() > 0.3 // 70% 成功率

    if (success) {
      const latency = Math.floor(Math.random() * 100) + 10 // 10-110ms
      showMessage(`连接测试成功！延迟: ${latency}ms`, 'success')
    } else {
      showMessage('连接测试失败: 连接超时或认证失败', 'error')
    }
  } catch (error) {
    console.error('测试连接失败:', error)
    showMessage('测试连接失败: ' + error.message, 'error')
  } finally {
    testing.value = false
  }
}
</script>

<style scoped>
/* 🎨 浪漫风格表单样式 */

/* 表单控件间距优化 */
.form-control {
  margin-bottom: 1.25rem;
}

.form-control .label {
  margin-bottom: 0.5rem;
}

.form-control .label-text {
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
}

/* 输入框样式增强 */
.input-romantic {
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background-color: transparent;
  backdrop-filter: blur(2px);
  height: 3rem;
  padding: 0.75rem 1rem;
}

.input-romantic:focus {
  border-color: rgba(0, 0, 0, 0.2);
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
  background-color: rgba(255, 255, 255, 0.1);
}

.input-romantic:hover {
  border-color: rgba(0, 0, 0, 0.15);
  background-color: rgba(255, 255, 255, 0.05);
}

.input-romantic::placeholder {
  color: #9ca3af;
  font-style: italic;
}

/* 文本域样式增强 */
.textarea-romantic {
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background-color: transparent;
  backdrop-filter: blur(2px);
  resize: vertical;
  min-height: 5rem;
  padding: 0.75rem 1rem;
}

.textarea-romantic:focus {
  border-color: rgba(0, 0, 0, 0.2);
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
  background-color: rgba(255, 255, 255, 0.1);
}

.textarea-romantic:hover {
  border-color: rgba(0, 0, 0, 0.15);
  background-color: rgba(255, 255, 255, 0.05);
}

.textarea-romantic::placeholder {
  color: #9ca3af;
  font-style: italic;
}

/* 选择框样式增强 */
.select-romantic {
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background-color: transparent;
  backdrop-filter: blur(2px);
  height: 3rem;
  padding: 0.75rem 1rem;
}

.select-romantic:focus {
  border-color: rgba(0, 0, 0, 0.2);
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
  background-color: rgba(255, 255, 255, 0.1);
}

.select-romantic:hover {
  border-color: rgba(0, 0, 0, 0.15);
  background-color: rgba(255, 255, 255, 0.05);
}

/* Radio按钮样式增强 */
.radio-romantic {
  border-color: #f472b6;
  background-color: white;
}

.radio-romantic:checked {
  background-color: #f472b6;
  border-color: #f472b6;
  background-image: radial-gradient(circle, white 40%, transparent 50%);
}

.radio-romantic:focus {
  box-shadow: 0 0 0 3px rgba(244, 114, 182, 0.2);
}

/* 浪漫按钮样式 */
.btn-romantic {
  background: linear-gradient(to right, #f472b6, #a855f7);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.btn-romantic:hover {
  background: linear-gradient(to right, #ec4899, #9333ea);
  transform: scale(1.02);
  box-shadow: 0 4px 6px -1px rgba(244, 114, 182, 0.3);
}

.btn-romantic.btn-outline {
  background: transparent;
  border: 2px solid #f472b6;
  color: #f472b6;
}

.btn-romantic.btn-outline:hover {
  background: linear-gradient(to right, #f472b6, #a855f7);
  color: white;
  border-color: transparent;
}

/* 认证方式选择卡片样式 */
.border-romantic-400 {
  border-color: #f472b6 !important;
}

.bg-romantic-50 {
  background-color: #fdf2f8 !important;
}

.border-romantic-300 {
  border-color: #f9a8d4 !important;
}

.text-romantic-500 {
  color: #ec4899 !important;
}

/* 卡片悬浮效果 */
.card {
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 标签文字增强 */
.label-text {
  color: #374151;
  font-weight: 500;
}

/* 模态框背景优化 */
.modal-box {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(253, 242, 248, 0.95) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(244, 114, 182, 0.1);
}
</style>

