<template>
  <!-- 🎯 主机卡片组件 -->
  <div
    :class="cardClasses"
    :style="cardStyle"
    @click="handleCardClick"
  >
    <!-- 卡片头部 -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center space-x-3">
        <div :class="iconClasses" :style="iconStyle">
          <Icon name="server" :size="20" class="text-white" />
        </div>
        <div>
          <h3 class="font-semibold text-gray-800 text-lg">{{ host.name }}</h3>
          <p class="text-sm text-gray-500">{{ host.ip }}:{{ host.port }}</p>
        </div>
      </div>
      
      <!-- 状态指示器 -->
      <div :class="statusClasses">
        <div class="w-3 h-3 rounded-full" :style="statusDotStyle"></div>
      </div>
    </div>

    <!-- 主机信息 -->
    <div class="space-y-3 mb-4">
      <!-- 环境标签 -->
      <div class="flex items-center justify-between">
        <span class="text-sm text-gray-600">环境</span>
        <span :class="environmentClasses" :style="environmentStyle">
          <Icon :name="environmentIcon" :size="14" class="mr-1" />
          {{ environmentText }}
        </span>
      </div>

      <!-- 认证方式 -->
      <div class="flex items-center justify-between">
        <span class="text-sm text-gray-600">认证</span>
        <span :class="authClasses" :style="authStyle">
          <Icon :name="authIcon" :size="14" class="mr-1" />
          {{ authText }}
        </span>
      </div>

      <!-- 用户名 -->
      <div class="flex items-center justify-between">
        <span class="text-sm text-gray-600">用户</span>
        <span class="text-sm font-medium text-gray-800">{{ host.username }}</span>
      </div>
    </div>

    <!-- 描述 -->
    <div v-if="host.description" class="mb-4">
      <p class="text-sm text-gray-600 line-clamp-2">{{ host.description }}</p>
    </div>

    <!-- 连接测试状态 -->
    <div v-if="host.testing" class="mb-4">
      <div class="flex items-center space-x-2 text-sm">
        <div class="animate-spin w-4 h-4 border-2 rounded-full"
             style="border-color: #f472b6; border-top-color: transparent;"></div>
        <span style="color: #ec4899;">正在测试连接...</span>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex items-center justify-center gap-2 pt-4 border-t border-gray-100">
      <button
        class="btn btn-ghost btn-sm text-gray-600 hover:text-romantic-600 flex-1"
        @click.stop="handleEdit"
        title="编辑主机"
      >
        <Icon name="edit" :size="16" />
      </button>

      <button
        class="btn btn-ghost btn-sm text-gray-600 hover:text-red-600 flex-1"
        @click.stop="handleDelete"
        title="删除主机"
      >
        <Icon name="delete" :size="16" />
      </button>

      <!-- 连接测试按钮 -->
      <button
        class="btn btn-ghost btn-sm text-gray-600 hover:text-romantic-600 flex-1"
        @click.stop="handleConnect"
        :disabled="host.testing"
        :title="host.testing ? '测试中...' : '连接测试'"
      >
        <span v-if="host.testing" class="loading loading-spinner loading-sm"></span>
        <Icon v-else name="link" :size="16" />
      </button>
    </div>

    <!-- 悬浮效果遮罩 -->
    <div class="absolute inset-0 rounded-xl transition-all duration-300 pointer-events-none hover-overlay"></div>

    <!-- 装饰性星光效果 -->
    <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
      <Icon name="sparkles" :size="16" style="color: #f9a8d4" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import Icon from './Icon.vue'

const props = defineProps({
  host: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['edit', 'delete', 'test', 'connect', 'click'])

// 卡片样式
const cardClasses = computed(() => {
  const baseClasses = [
    'relative group',
    'bg-white rounded-xl',
    'p-6 cursor-pointer transition-all duration-300',
    'hover:-translate-y-1'
  ]

  // 根据环境添加不同的阴影颜色 - 使用浪漫主题色调
  if (props.host.environment === 'prod') {
    baseClasses.push('shadow-romantic hover:shadow-dreamy')
  } else {
    baseClasses.push('shadow-dreamy hover:shadow-romantic')
  }

  return baseClasses
})

// 卡片样式 - 简洁的环境区分
const cardStyle = computed(() => {
  if (props.host.environment === 'prod') {
    // 生产环境：浪漫主题色调
    return {
      background: 'linear-gradient(135deg, rgba(236, 72, 153, 0.02) 0%, rgba(255, 255, 255, 0.98) 30%, white 100%)',
      boxShadow: '0 4px 6px -1px rgba(236, 72, 153, 0.1), 0 2px 4px -1px rgba(236, 72, 153, 0.06)'
    }
  } else {
    // 测试环境：梦幻紫色调
    return {
      background: 'linear-gradient(135deg, rgba(168, 85, 247, 0.02) 0%, rgba(255, 255, 255, 0.98) 30%, white 100%)',
      boxShadow: '0 4px 6px -1px rgba(168, 85, 247, 0.1), 0 2px 4px -1px rgba(168, 85, 247, 0.06)'
    }
  }
})

// 图标容器样式 - 根据环境区分颜色
const iconClasses = computed(() => [
  'w-12 h-12 rounded-lg flex items-center justify-center',
  'shadow-sm'
])

const iconStyle = computed(() => {
  if (props.host.environment === 'prod') {
    // 生产环境：使用与仪表板功能模块相同的粉紫色渐变
    return {
      background: 'linear-gradient(135deg, #f472b6, #a855f7)' // from-romantic-400 to-dreamy-400
    }
  } else {
    // 测试环境：使用与"新建项目"按钮相同的背景色
    return {
      background: 'linear-gradient(to right, #f472b6, #ec4899)' // btn-romantic 样式
    }
  }
})

// 状态指示器样式
const statusClasses = computed(() => [
  'flex items-center space-x-2'
])

const statusDotStyle = computed(() => {
  if (props.host.status === 'active') {
    return {
      backgroundColor: '#4ade80',
      boxShadow: '0 10px 15px -3px rgba(74, 222, 128, 0.5)'
    }
  }
  return {
    backgroundColor: '#9ca3af'
  }
})

// 环境相关
const environmentText = computed(() => {
  return props.host.environment === 'prod' ? '生产环境' : '测试环境'
})

const environmentIcon = computed(() => {
  return props.host.environment === 'prod' ? 'alert-triangle' : 'beaker'
})

const environmentClasses = computed(() => [
  'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium'
])

const environmentStyle = computed(() => {
  if (props.host.environment === 'prod') {
    // 生产环境：使用主题的浪漫色调，与主色调保持一致
    return {
      backgroundColor: '#fbcfe8', // romantic-200，更接近主题色
      color: '#ec4899', // romantic-500，主题主色
      boxShadow: '0 1px 2px rgba(236, 72, 153, 0.15)'
    }
  } else {
    // 测试环境：使用与浪漫色调协调的紫色调
    return {
      backgroundColor: '#e9d5ff', // dreamy-200，与浪漫色调协调
      color: '#a855f7', // dreamy-500，与主题协调
      boxShadow: '0 1px 2px rgba(168, 85, 247, 0.15)'
    }
  }
})

// 认证方式相关
const authText = computed(() => {
  return props.host.auth_type === 'password' ? '密码认证' : '私钥认证'
})

const authIcon = computed(() => {
  return props.host.auth_type === 'password' ? 'shield' : 'key'
})

const authClasses = computed(() => [
  'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium'
])

const authStyle = computed(() => {
  if (props.host.auth_type === 'password') {
    // 密码认证：柔和的绿色
    return {
      backgroundColor: '#dcfce7',
      color: '#15803d',
      boxShadow: '0 1px 2px rgba(21, 128, 61, 0.1)'
    }
  } else {
    // 私钥认证：柔和的紫色
    return {
      backgroundColor: '#f3e8ff',
      color: '#7c3aed',
      boxShadow: '0 1px 2px rgba(124, 58, 237, 0.1)'
    }
  }
})

// 事件处理
const handleCardClick = () => {
  emit('click', props.host)
}

const handleEdit = () => {
  emit('edit', props.host)
}

const handleDelete = () => {
  emit('delete', props.host)
}

const handleConnect = () => {
  emit('connect', props.host)
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 浪漫按钮样式 */
.btn-romantic {
  background: linear-gradient(to right, #f472b6, #a855f7);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.btn-romantic:hover {
  background: linear-gradient(to right, #ec4899, #9333ea);
  transform: scale(1.05);
  box-shadow: 0 4px 6px -1px rgba(236, 72, 153, 0.1), 0 2px 4px -1px rgba(236, 72, 153, 0.06);
}

/* 卡片动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

.group:hover {
  animation: float 2s ease-in-out infinite;
}

/* 状态指示器动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.bg-green-400 {
  animation: pulse 2s ease-in-out infinite;
}

/* 渐变背景效果 */
.bg-gradient-to-br {
  background: linear-gradient(135deg, #f472b6, #a78bfa) !important;
}

/* 悬浮时的光晕效果 */
.group:hover .bg-gradient-to-br {
  box-shadow: 0 0 20px rgba(244, 114, 182, 0.4) !important;
}

/* 悬浮遮罩效果 */
.hover-overlay {
  background: linear-gradient(135deg, rgba(253, 242, 248, 0) 0%, rgba(243, 232, 255, 0) 100%);
}

.group:hover .hover-overlay {
  background: linear-gradient(135deg, rgba(253, 242, 248, 0.2) 0%, rgba(243, 232, 255, 0.2) 100%);
}
</style>
