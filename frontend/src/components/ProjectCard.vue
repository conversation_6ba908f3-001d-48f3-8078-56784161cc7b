<template>
  <!-- 🎯 项目卡片组件 -->
  <div :class="cardClasses" @click="handleCardClick">
    <!-- 卡片头部 -->
    <div class="flex items-center justify-between mb-2 w-full min-w-0">
      <!-- 项目图标和名称 -->
      <div class="flex items-center space-x-3 flex-1 min-w-0">
        <div :class="iconClasses" class="flex-shrink-0">
          <Icon :name="projectIcon" :size="24" color="white" class="drop-shadow-sm" />
        </div>
        <div class="flex-1 min-w-0">
          <h3 :class="titleClasses" class="truncate">{{ project.name }}</h3>
          <p :class="descriptionClasses" class="truncate">{{ project.description }}</p>
        </div>
      </div>

      <!-- 状态指示器 -->
      <div v-if="project.status" :class="statusClasses" class="flex-shrink-0">
        <div class="w-2 h-2 rounded-full" :class="statusDotClasses"></div>
      </div>
    </div>

    <!-- 项目信息标签 -->
    <div class="flex gap-1.5 mb-3 overflow-hidden">
      <div :class="environmentBadgeClasses">
        {{ project.environment === 'prod' ? '生产' : '测试' }}
      </div>
      <div :class="typeBadgeClasses">
        {{ project.project_type === 'frontend' ? '前端' : '后端' }}
      </div>
      <div :class="deployBadgeClasses">
        {{ project.deploy_type === 'local' ? '本地' : '远程' }}
      </div>
    </div>

    <!-- 快速操作按钮 -->
    <div class="flex flex-col gap-2 w-full">
      <button
        :class="primaryActionClasses"
        @click.stop="$emit('deploy', project)"
        :disabled="isLoading"
        class="w-full flex items-center justify-center gap-2"
      >
        <Icon name="upload" :size="14" />
        <span>一键部署</span>
      </button>

      <button
        v-if="!isPythonProject"
        :class="secondaryActionClasses"
        @click.stop="$emit('build', project)"
        :disabled="isLoading"
        class="w-full flex items-center justify-center gap-2"
      >
        <Icon name="tools" :size="14" />
        <span>构建</span>
      </button>

      <button
        v-if="!isPythonProject"
        :class="secondaryActionClasses"
        @click.stop="$emit('artifact', project)"
        class="w-full flex items-center justify-center gap-2"
      >
        <Icon name="files" :size="14" />
        <span>产物</span>
      </button>

      <button
        :class="secondaryActionClasses"
        @click.stop="$emit('logs', project)"
        class="w-full flex items-center justify-center gap-2"
      >
        <Icon name="document" :size="14" />
        <span>日志</span>
      </button>
    </div>

    <!-- 更多操作菜单 -->
    <div class="absolute top-4 right-4" @click.stop>
      <div class="dropdown dropdown-end">
        <label tabindex="0" :class="[menuButtonClasses, 'menu-button-with-ripple']">
          <Icon name="more" :size="16" />
        </label>
        <ul tabindex="0" class="dropdown-content menu p-2 shadow-lg bg-white rounded-box w-32 min-w-max">
          <li>
            <a @click.stop="$emit('edit', project)" class="text-sm flex items-center gap-2 whitespace-nowrap hover:bg-gray-100 hover:text-gray-900 rounded-lg transition-all duration-200 px-3 py-2">
              <Icon name="edit" :size="14" />
              编辑项目
            </a>
          </li>
          <li>
            <a @click.stop="$emit('delete', project)" class="text-sm text-red-600 flex items-center gap-2 whitespace-nowrap hover:bg-red-50 hover:text-red-700 rounded-lg transition-all duration-200 px-3 py-2">
              <Icon name="delete" :size="14" />
              删除项目
            </a>
          </li>
        </ul>
      </div>
    </div>

    <!-- 加载状态覆盖层 -->
    <div v-if="isLoading" class="absolute inset-0 bg-white/80 backdrop-blur-sm rounded-2xl flex items-center justify-center">
      <div class="loading loading-spinner loading-md text-romantic-500"></div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import Icon from './Icon.vue'

const props = defineProps({
  // 项目数据
  project: {
    type: Object,
    required: true
  },
  
  // 加载状态
  isLoading: {
    type: Boolean,
    default: false
  },
  
  // 样式变体
  variant: {
    type: String,
    default: 'romantic',
    validator: (value) => [
      'romantic', 'dreamy', 'gradient', 'success', 'glass',
      'production', 'testing', 'default'
    ].includes(value)
  },
  
  // 是否可点击
  clickable: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['click', 'edit', 'deploy', 'build', 'artifact', 'logs', 'delete'])

// 计算项目图标
const projectIcon = computed(() => {
  if (props.project.project_type === 'frontend') {
    return 'monitor' // 更现代的前端图标
  } else if (props.project.project_type === 'backend') {
    return 'database' // 更直观的后端图标
  }
  return 'folder-open' // 更友好的默认图标
})

// 判断是否为 Python 项目
const isPythonProject = computed(() => {
  return props.project.build_config?.language === 'python'
})



// 计算样式类
const cardClasses = computed(() => {
  const base = 'project-card relative transition-all duration-300'
  const variants = {
    // 生产环境：使用粉紫色调的淡雅背景
    production: 'bg-gradient-to-br from-white to-pink-50/30 border border-pink-200/40 shadow-lg hover:shadow-xl',
    // 测试环境：使用粉色调的淡雅背景
    testing: 'bg-gradient-to-br from-white to-pink-50/20 border border-pink-200/30 shadow-lg hover:shadow-xl',
    default: 'bg-gradient-to-br from-white to-gray-50/30 border border-gray-200/40 shadow-lg hover:shadow-xl',
    // 保留旧的变体以防兼容性问题
    romantic: 'bg-gradient-to-br from-white to-pink-50/30 border border-pink-200/40 shadow-lg hover:shadow-xl',
    dreamy: 'bg-gradient-to-br from-white to-pink-50/20 border border-pink-200/30 shadow-lg hover:shadow-xl',
    gradient: 'bg-gradient-to-br from-pink-400 to-purple-500 text-white shadow-lg hover:shadow-xl',
    success: 'bg-gradient-to-br from-green-400 to-emerald-500 text-white shadow-lg hover:shadow-xl',
    glass: 'bg-gradient-to-br from-white to-gray-50/30 border border-gray-200/40 shadow-lg hover:shadow-xl'
  }

  const interactive = props.clickable ? 'cursor-pointer hover:scale-[1.02]' : ''

  return `${base} ${variants[props.variant]} ${interactive} ${props.variant}`
})

const iconClasses = computed(() => {
  const variants = {
    // 生产环境：使用与仪表板功能模块相同的粉紫色渐变
    production: 'bg-gradient-to-br from-pink-400 to-purple-500',
    // 测试环境：使用与"新建项目"按钮相同的浪漫粉色渐变
    testing: 'bg-gradient-to-r from-pink-400 to-pink-500',
    default: 'bg-gradient-to-br from-gray-400 to-gray-600',
    // 保留旧的变体以防兼容性问题
    romantic: 'bg-gradient-to-br from-pink-400 to-purple-500',
    dreamy: 'bg-gradient-to-r from-pink-400 to-pink-500',
    gradient: 'bg-gradient-to-br from-white/30 to-white/10 backdrop-blur-sm',
    success: 'bg-gradient-to-br from-white/30 to-white/10 backdrop-blur-sm',
    glass: 'bg-gradient-to-br from-gray-400 to-gray-600'
  }

  return `w-14 h-14 rounded-2xl flex items-center justify-center ${variants[props.variant]} transition-all duration-300 hover:scale-110 shadow-xl hover:shadow-2xl`
})

const titleClasses = computed(() => {
  const variants = {
    production: 'text-lg font-bold text-gray-800',
    testing: 'text-lg font-bold text-gray-800',
    default: 'text-lg font-bold text-gray-800',
    // 保留旧的变体以防兼容性问题
    romantic: 'text-lg font-bold text-gray-800',
    dreamy: 'text-lg font-bold text-gray-800',
    gradient: 'text-lg font-bold text-white',
    success: 'text-lg font-bold text-white',
    glass: 'text-lg font-bold text-gray-800'
  }

  return variants[props.variant]
})

const descriptionClasses = computed(() => {
  const variants = {
    production: 'text-gray-600 text-sm leading-relaxed',
    testing: 'text-gray-600 text-sm leading-relaxed',
    default: 'text-gray-600 text-sm leading-relaxed',
    // 保留旧的变体以防兼容性问题
    romantic: 'text-gray-600 text-sm leading-relaxed',
    dreamy: 'text-gray-600 text-sm leading-relaxed',
    gradient: 'text-white/90 text-sm leading-relaxed',
    success: 'text-white/90 text-sm leading-relaxed',
    glass: 'text-gray-600 text-sm leading-relaxed'
  }

  return variants[props.variant]
})

// 徽章样式
const environmentBadgeClasses = computed(() => {
  const isProduction = props.project.environment === 'prod'
  const variants = {
    production: isProduction
      ? 'badge bg-gradient-to-r from-pink-400 to-purple-500 text-white border-0 shadow-sm px-2 py-1 text-xs font-medium rounded-md'
      : 'badge bg-gradient-to-r from-pink-400 to-purple-500 text-white border-0 shadow-sm px-2 py-1 text-xs font-medium rounded-md',
    testing: isProduction
      ? 'badge bg-gradient-to-r from-pink-400 to-pink-500 text-white border-0 shadow-sm px-2 py-1 text-xs font-medium rounded-md'
      : 'badge bg-gradient-to-r from-pink-400 to-pink-500 text-white border-0 shadow-sm px-2 py-1 text-xs font-medium rounded-md',
    default: isProduction
      ? 'badge bg-gradient-to-r from-pink-400 to-purple-500 text-white border-0 shadow-sm px-2 py-1 text-xs font-medium rounded-md'
      : 'badge bg-gradient-to-r from-pink-400 to-pink-500 text-white border-0 shadow-sm px-2 py-1 text-xs font-medium rounded-md',
    // 保留旧的变体以防兼容性问题
    romantic: isProduction
      ? 'badge bg-gradient-to-r from-pink-400 to-purple-500 text-white border-0 shadow-sm px-2 py-1 text-xs font-medium rounded-md'
      : 'badge bg-gradient-to-r from-pink-400 to-purple-500 text-white border-0 shadow-sm px-2 py-1 text-xs font-medium rounded-md',
    dreamy: isProduction
      ? 'badge bg-gradient-to-r from-pink-400 to-pink-500 text-white border-0 shadow-sm px-2 py-1 text-xs font-medium rounded-md'
      : 'badge bg-gradient-to-r from-pink-400 to-pink-500 text-white border-0 shadow-sm px-2 py-1 text-xs font-medium rounded-md',
    gradient: 'badge bg-white/20 text-white border-white/30 px-2 py-1 text-xs font-medium rounded-md',
    success: 'badge bg-white/20 text-white border-white/30 px-2 py-1 text-xs font-medium rounded-md',
    glass: isProduction
      ? 'badge bg-gray-100 text-gray-700 border-gray-200 px-2 py-1 text-xs font-medium rounded-md'
      : 'badge bg-gray-50 text-gray-600 border-gray-200 px-2 py-1 text-xs font-medium rounded-md'
  }

  return variants[props.variant]
})

const typeBadgeClasses = computed(() => {
  const isFrontend = props.project.project_type === 'frontend'
  const variants = {
    production: isFrontend
      ? 'badge bg-orange-100 text-orange-700 border-orange-200 px-2 py-1 text-xs font-medium rounded-md'
      : 'badge bg-red-100 text-red-700 border-red-200 px-2 py-1 text-xs font-medium rounded-md',
    testing: isFrontend
      ? 'badge bg-cyan-100 text-cyan-700 border-cyan-200 px-2 py-1 text-xs font-medium rounded-md'
      : 'badge bg-blue-100 text-blue-700 border-blue-200 px-2 py-1 text-xs font-medium rounded-md',
    default: isFrontend
      ? 'badge bg-slate-100 text-slate-700 border-slate-200 px-2 py-1 text-xs font-medium rounded-md'
      : 'badge bg-gray-100 text-gray-700 border-gray-200 px-2 py-1 text-xs font-medium rounded-md',
    // 保留旧的变体以防兼容性问题
    romantic: isFrontend
      ? 'badge bg-orange-100 text-orange-700 border-orange-200 px-2 py-1 text-xs font-medium rounded-md'
      : 'badge bg-red-100 text-red-700 border-red-200 px-2 py-1 text-xs font-medium rounded-md',
    dreamy: isFrontend
      ? 'badge bg-cyan-100 text-cyan-700 border-cyan-200 px-2 py-1 text-xs font-medium rounded-md'
      : 'badge bg-blue-100 text-blue-700 border-blue-200 px-2 py-1 text-xs font-medium rounded-md',
    gradient: 'badge bg-white/20 text-white border-white/30 px-2 py-1 text-xs font-medium rounded-md',
    success: 'badge bg-white/20 text-white border-white/30 px-2 py-1 text-xs font-medium rounded-md',
    glass: isFrontend
      ? 'badge bg-slate-100 text-slate-700 border-slate-200 px-2 py-1 text-xs font-medium rounded-md'
      : 'badge bg-gray-100 text-gray-700 border-gray-200 px-2 py-1 text-xs font-medium rounded-md'
  }

  return variants[props.variant]
})

const deployBadgeClasses = computed(() => {
  const isLocal = props.project.deploy_type === 'local'
  const variants = {
    production: isLocal
      ? 'badge bg-gradient-to-r from-amber-500 to-orange-600 text-white border-0 shadow-sm px-2 py-1 text-xs font-medium rounded-md'
      : 'badge bg-gradient-to-r from-slate-500 to-gray-600 text-white border-0 shadow-sm px-2 py-1 text-xs font-medium rounded-md',
    testing: isLocal
      ? 'badge bg-gradient-to-r from-yellow-500 to-amber-600 text-white border-0 shadow-sm px-2 py-1 text-xs font-medium rounded-md'
      : 'badge bg-gradient-to-r from-gray-500 to-slate-600 text-white border-0 shadow-sm px-2 py-1 text-xs font-medium rounded-md',
    default: isLocal
      ? 'badge bg-gradient-to-r from-amber-500 to-orange-600 text-white border-0 shadow-sm px-2 py-1 text-xs font-medium rounded-md'
      : 'badge bg-gradient-to-r from-slate-500 to-gray-600 text-white border-0 shadow-sm px-2 py-1 text-xs font-medium rounded-md',
    // 保留旧的变体以防兼容性问题
    romantic: isLocal
      ? 'badge bg-gradient-to-r from-amber-500 to-orange-600 text-white border-0 shadow-sm px-2 py-1 text-xs font-medium rounded-md'
      : 'badge bg-gradient-to-r from-slate-500 to-gray-600 text-white border-0 shadow-sm px-2 py-1 text-xs font-medium rounded-md',
    dreamy: isLocal
      ? 'badge bg-gradient-to-r from-yellow-500 to-amber-600 text-white border-0 shadow-sm px-2 py-1 text-xs font-medium rounded-md'
      : 'badge bg-gradient-to-r from-gray-500 to-slate-600 text-white border-0 shadow-sm px-2 py-1 text-xs font-medium rounded-md',
    gradient: 'badge bg-white/20 text-white border-white/30 px-2 py-1 text-xs font-medium rounded-md',
    success: 'badge bg-white/20 text-white border-white/30 px-2 py-1 text-xs font-medium rounded-md',
    glass: isLocal
      ? 'badge bg-yellow-100 text-yellow-700 border-yellow-200 px-2 py-1 text-xs font-medium rounded-md'
      : 'badge bg-indigo-100 text-indigo-700 border-indigo-200 px-2 py-1 text-xs font-medium rounded-md'
  }

  return variants[props.variant]
})



// 按钮样式
const primaryActionClasses = computed(() => {
  const variants = {
    production: 'btn bg-gradient-to-r from-pink-400 to-purple-500 text-white border-0 btn-sm hover:from-pink-500 hover:to-purple-600 shadow-md',
    testing: 'btn bg-gradient-to-r from-pink-400 to-pink-500 text-white border-0 btn-sm hover:from-pink-500 hover:to-pink-600 shadow-md',
    default: 'btn bg-gradient-to-r from-pink-400 to-purple-500 text-white border-0 btn-sm hover:from-pink-500 hover:to-purple-600 shadow-md',
    // 保留旧的变体以防兼容性问题
    romantic: 'btn bg-gradient-to-r from-pink-400 to-purple-500 text-white border-0 btn-sm hover:from-pink-500 hover:to-purple-600 shadow-md',
    dreamy: 'btn bg-gradient-to-r from-pink-400 to-pink-500 text-white border-0 btn-sm hover:from-pink-500 hover:to-pink-600 shadow-md',
    gradient: 'btn bg-white/20 text-white border-white/30 btn-sm hover:bg-white/30',
    success: 'btn bg-white/20 text-white border-white/30 btn-sm hover:bg-white/30',
    glass: 'btn bg-gradient-to-r from-gray-400 to-gray-500 text-white border-0 btn-sm hover:from-gray-500 hover:to-gray-600 shadow-md'
  }

  return variants[props.variant]
})

const secondaryActionClasses = computed(() => {
  const variants = {
    production: 'btn bg-pink-50 text-purple-700 btn-sm hover:bg-pink-100 hover:text-purple-800 border border-pink-200 shadow-sm',
    testing: 'btn bg-pink-50 text-pink-700 btn-sm hover:bg-pink-100 hover:text-pink-800 border border-pink-200 shadow-sm',
    default: 'btn bg-pink-50 text-purple-700 btn-sm hover:bg-pink-100 hover:text-purple-800 border border-pink-200 shadow-sm',
    // 保留旧的变体以防兼容性问题
    romantic: 'btn bg-pink-50 text-purple-700 btn-sm hover:bg-pink-100 hover:text-purple-800 border border-pink-200 shadow-sm',
    dreamy: 'btn bg-pink-50 text-pink-700 btn-sm hover:bg-pink-100 hover:text-pink-800 border border-pink-200 shadow-sm',
    gradient: 'btn bg-white/15 text-white btn-sm hover:bg-white/25 border border-white/20 shadow-sm',
    success: 'btn bg-white/15 text-white btn-sm hover:bg-white/25 border border-white/20 shadow-sm',
    glass: 'btn bg-gray-50 text-gray-700 btn-sm hover:bg-gray-100 hover:text-gray-800 border border-gray-200 shadow-sm'
  }

  return variants[props.variant]
})

const menuButtonClasses = computed(() => {
  const variants = {
    production: 'btn btn-ghost btn-circle btn-sm text-gray-500',
    testing: 'btn btn-ghost btn-circle btn-sm text-gray-500',
    default: 'btn btn-ghost btn-circle btn-sm text-gray-500',
    // 保留旧的变体以防兼容性问题
    romantic: 'btn btn-ghost btn-circle btn-sm text-gray-500',
    dreamy: 'btn btn-ghost btn-circle btn-sm text-slate-500',
    gradient: 'btn btn-ghost btn-circle btn-sm text-white',
    success: 'btn btn-ghost btn-circle btn-sm text-white',
    glass: 'btn btn-ghost btn-circle btn-sm text-gray-500'
  }

  return variants[props.variant]
})

// 状态样式
const statusClasses = computed(() => {
  return 'flex items-center justify-center'
})

const statusDotClasses = computed(() => {
  const status = props.project.status || 'offline'
  const statuses = {
    online: 'bg-green-400 animate-pulse',
    offline: 'bg-gray-400',
    warning: 'bg-yellow-400 animate-pulse',
    error: 'bg-red-400 animate-pulse'
  }

  return statuses[status]
})

// 事件处理
const handleCardClick = () => {
  if (props.clickable) {
    emit('click', props.project)
  }
}
</script>

<style scoped>
.project-card {
  position: relative;
  padding: 1.5rem;
  border-radius: 1rem;
  overflow: hidden;
  min-height: 280px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(236, 72, 153, 0.1);
  box-shadow: var(--shadow-romantic);
  transition: all 0.3s ease;
}

.project-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(236, 72, 153, 0.05), rgba(168, 85, 247, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  border-radius: inherit;
}

.project-card:hover::after {
  opacity: 1;
}

.project-card:hover {
  box-shadow: var(--shadow-dreamy);
}

/* 更多功能按钮呼吸灯效果 */
.menu-button-with-ripple {
  position: relative !important;
  transition: all 0.3s ease !important;
  width: 32px !important;
  height: 32px !important;
  min-height: 32px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.menu-button-with-ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: transparent;
  border: 0px solid #10b981;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  z-index: 0;
  pointer-events: none;
  box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  aspect-ratio: 1 / 1;
  box-sizing: border-box;
}

.menu-button-with-ripple:hover::before {
  width: 28px !important;
  height: 28px !important;
  min-width: 28px !important;
  min-height: 28px !important;
  max-width: 28px !important;
  max-height: 28px !important;
  border-width: 1.5px;
  animation: green-ripple-pulse 2s infinite;
  border-radius: 50% !important;
  box-sizing: border-box !important;
}

.menu-button-with-ripple:hover {
  transform: scale(1.05) !important;
}

/* 统一的绿色呼吸灯效果 - 适配所有主题 */
.menu-button-with-ripple:hover {
  color: #059669 !important;
}

/* 深色主题的文字颜色调整 */
.gradient .menu-button-with-ripple:hover,
.success .menu-button-with-ripple:hover {
  color: #6ee7b7 !important;
}

/* 绿色呼吸灯动画 */
@keyframes green-ripple-pulse {
  0% {
    border-color: #10b981;
    box-shadow:
      0 0 0 0 rgba(16, 185, 129, 0.7),
      0 0 0 0 rgba(16, 185, 129, 0.4);
    transform: translate(-50%, -50%);
  }
  50% {
    border-color: #34d399;
    box-shadow:
      0 0 0 2px rgba(16, 185, 129, 0.4),
      0 0 0 4px rgba(16, 185, 129, 0.2);
    transform: translate(-50%, -50%);
  }
  100% {
    border-color: #10b981;
    box-shadow:
      0 0 0 0 rgba(16, 185, 129, 0.7),
      0 0 0 0 rgba(16, 185, 129, 0.4);
    transform: translate(-50%, -50%);
  }
}

/* 卡片内容分层 */
.project-card .flex.items-center.space-x-3 {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.project-card .flex.flex-wrap.gap-2 {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 0.5rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.project-card .flex.flex-wrap.gap-2:last-of-type {
  background: transparent;
  border: none;
  padding: 0;
  margin-top: auto;
}

/* 加载状态动画 */
.loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 文本内容防溢出 */
.project-card h3,
.project-card p,
.project-card span {
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  max-width: 100%;
}

.project-card .truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .project-card {
    padding: 1rem;
    min-height: 240px;
  }

  .project-card h3 {
    font-size: 1rem;
  }

  .project-card p {
    font-size: 0.875rem;
  }

  .flex.flex-wrap.gap-2 {
    gap: 0.5rem;
  }

  .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
}

@media (max-width: 768px) {
  .project-card {
    min-height: 260px;
  }

  .flex.items-center.space-x-3 {
    space-x: 0.5rem;
  }
}

/* 触摸设备优化 */
@media (hover: none) {
  .project-card:hover {
    transform: none;
  }

  .project-card:active {
    transform: scale(0.98);
  }
}
</style>
