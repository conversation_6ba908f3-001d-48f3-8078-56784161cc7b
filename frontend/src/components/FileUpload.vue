<template>
  <div class="file-upload-container">
    <div 
      class="upload-area"
      :class="{ 'drag-over': isDragOver, 'has-file': hasFile }"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragleave="handleDragLeave"
      @click="triggerFileInput"
    >
      <input
        ref="fileInput"
        type="file"
        style="display: none"
        :accept="accept"
        @change="handleFileSelect"
      />
      
      <div v-if="!hasFile && !defaultFilename" class="upload-placeholder">
        <!-- <el-icon class="upload-icon"><Upload /></el-icon> -->
        <div class="upload-icon">📁</div>
        <div class="upload-text">
          <p>拖拽文件到此处或<span class="click-text">点击上传</span></p>
          <p class="upload-hint">{{ hint }}</p>
        </div>
      </div>

      <!-- 显示默认文件名 -->
      <div v-if="!hasFile && defaultFilename" class="default-file-info">
        <Icon name="document" :size="24" class="file-icon" />
        <div class="file-details">
          <div class="file-name">{{ defaultFilename }}</div>
          <div class="file-hint">点击或拖拽文件以替换</div>
        </div>
      </div>
      
      <div v-else class="file-info">
        <Icon name="document" :size="24" class="file-icon" />
        <div class="file-details">
          <div class="file-name">{{ fileName }}</div>
          <div class="file-size">{{ formatFileSize(fileSize) }}</div>
        </div>
        <button
          class="btn btn-error btn-sm btn-circle"
          @click.stop="removeFile"
        >
          <Icon name="close" :size="16" />
        </button>
      </div>
    </div>
    
    <div v-if="error" class="error-message">
      <Icon name="close" :size="16" />
      {{ error }}
    </div>
    
    <div v-if="hasFile && showPreview" class="file-preview">
      <div class="preview-header">
        <span>文件预览</span>
        <button class="btn btn-soft btn-ghost btn-sm" @click="showPreview = false">
          <Icon name="close" :size="16" />
        </button>
      </div>
      <div class="preview-content">
        <pre>{{ fileContent }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
// import { Upload, Document, Close, Warning } from '@element-plus/icons-vue'
import Icon from './Icon.vue'

const props = defineProps({
  accept: {
    type: String,
    default: '*'
  },
  maxSize: {
    type: Number,
    default: 10 * 1024 * 1024 // 10MB
  },
  hint: {
    type: String,
    default: '支持拖拽上传'
  },
  validateContent: {
    type: Function,
    default: null
  },
  defaultFilename: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['file-change', 'content-change'])

const fileInput = ref(null)
const isDragOver = ref(false)
const selectedFile = ref(null)
const fileContent = ref('')
const error = ref('')
const showPreview = ref(false)

const hasFile = computed(() => !!selectedFile.value)
const fileName = computed(() => selectedFile.value?.name || '')
const fileSize = computed(() => selectedFile.value?.size || 0)

// 处理拖拽事件
const handleDragOver = (e) => {
  e.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (e) => {
  e.preventDefault()
  isDragOver.value = false
}

const handleDrop = (e) => {
  e.preventDefault()
  isDragOver.value = false
  
  const files = e.dataTransfer.files
  if (files.length > 0) {
    handleFile(files[0])
  }
}

// 触发文件选择
const triggerFileInput = () => {
  fileInput.value?.click()
}

// 处理文件选择
const handleFileSelect = (e) => {
  const files = e.target.files
  if (files.length > 0) {
    handleFile(files[0])
  }
}

// 处理文件
const handleFile = async (file) => {
  error.value = ''
  
  // 检查文件大小
  if (file.size > props.maxSize) {
    error.value = `文件大小不能超过 ${formatFileSize(props.maxSize)}`
    return
  }
  
  selectedFile.value = file
  
  try {
    // 读取文件内容
    const content = await readFileContent(file)
    fileContent.value = content
    
    // 验证文件内容
    if (props.validateContent) {
      const validation = props.validateContent(content)
      if (!validation.valid) {
        error.value = validation.message
        return
      }
    }
    
    // 触发事件
    emit('file-change', file)
    emit('content-change', content)
    
    // 如果是文本文件，显示预览
    if (isTextFile(file)) {
      showPreview.value = true
    }
    
  } catch (err) {
    error.value = '读取文件失败: ' + err.message
  }
}

// 读取文件内容
const readFileContent = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => resolve(e.target.result)
    reader.onerror = (e) => reject(new Error('文件读取失败'))
    reader.readAsText(file)
  })
}

// 判断是否为文本文件
const isTextFile = (file) => {
  const textTypes = ['text/', 'application/json', 'application/xml']
  return textTypes.some(type => file.type.startsWith(type)) || 
         file.name.match(/\.(txt|json|xml|yml|yaml|conf|config|key|pem|pub)$/i)
}

// 移除文件
const removeFile = () => {
  selectedFile.value = null
  fileContent.value = ''
  error.value = ''
  showPreview.value = false
  
  // 清空input
  if (fileInput.value) {
    fileInput.value.value = ''
  }
  
  emit('file-change', null)
  emit('content-change', '')
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 暴露方法
defineExpose({
  removeFile,
  getFileContent: () => fileContent.value,
  getFile: () => selectedFile.value
})
</script>

<style scoped>
.file-upload-container {
  width: 100%;
}

.upload-area {
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fafafa;
}

.upload-area:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.upload-area.drag-over {
  border-color: #409eff;
  background-color: #e6f7ff;
}

.upload-area.has-file {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
}

.upload-text p {
  margin: 0;
  color: #606266;
}

.click-text {
  color: #409eff;
  font-weight: 500;
}

.upload-hint {
  font-size: 12px;
  color: #909399;
}

.file-info, .default-file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
}

.default-file-info {
  background-color: #f8f9fa;
  border: 1px dashed #d0d7de;
  border-radius: 4px;
}

.file-hint {
  font-size: 12px;
  color: #909399;
}

.file-icon {
  font-size: 24px;
  color: #409eff;
}

.file-details {
  flex: 1;
  text-align: left;
}

.file-name {
  font-weight: 500;
  color: #303133;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  color: #f56c6c;
  font-size: 14px;
}

.file-preview {
  margin-top: 16px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  font-size: 14px;
  font-weight: 500;
}

.preview-content {
  max-height: 200px;
  overflow: auto;
  padding: 12px;
  background-color: #fafafa;
}

.preview-content pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
