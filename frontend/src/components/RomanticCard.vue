<template>
  <!-- 🌸 浪漫风格基础卡片组件 -->
  <div 
    :class="cardClasses"
    @click="handleClick"
  >
    <!-- 卡片头部 -->
    <div v-if="showHeader" class="flex items-center justify-between mb-4">
      <!-- 图标区域 -->
      <div v-if="icon" :class="iconClasses">
        <Icon :name="icon" :size="iconSize" class="text-white" />
      </div>
      
      <!-- 徽章 -->
      <div v-if="badge" :class="badgeClasses">
        {{ badge }}
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-body p-0">
      <!-- 标题 -->
      <h3 v-if="title" :class="titleClasses">
        {{ title }}
      </h3>
      
      <!-- 描述 -->
      <p v-if="description" :class="descriptionClasses">
        {{ description }}
      </p>
      
      <!-- 统计数据 -->
      <div v-if="stats" :class="statsClasses">
        <div class="stat">
          <div :class="statValueClasses">{{ stats.value }}</div>
          <div :class="statLabelClasses">{{ stats.label }}</div>
        </div>
      </div>
      
      <!-- 自定义内容插槽 -->
      <div v-if="$slots.default" class="mt-4">
        <slot></slot>
      </div>
      
      <!-- 操作按钮 -->
      <div v-if="showActions" class="card-actions justify-end mt-4">
        <slot name="actions">
          <button 
            v-if="primaryAction" 
            :class="primaryButtonClasses"
            @click="handlePrimaryAction"
          >
            {{ primaryAction }}
          </button>
          <button 
            v-if="secondaryAction" 
            :class="secondaryButtonClasses"
            @click="handleSecondaryAction"
          >
            {{ secondaryAction }}
          </button>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import Icon from './Icon.vue'

const props = defineProps({
  // 基础属性
  title: String,
  description: String,
  icon: String,
  iconSize: {
    type: Number,
    default: 24
  },
  badge: String,
  
  // 统计数据
  stats: {
    type: Object,
    default: null
  },
  
  // 样式变体
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'gradient', 'glass', 'romantic', 'dreamy'].includes(value)
  },
  
  // 尺寸
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg', 'xl'].includes(value)
  },
  
  // 交互
  clickable: {
    type: Boolean,
    default: false
  },
  
  // 操作按钮
  primaryAction: String,
  secondaryAction: String,
  
  // 显示控制
  showHeader: {
    type: Boolean,
    default: true
  },
  showActions: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['click', 'primary-action', 'secondary-action'])

// 计算样式类
const cardClasses = computed(() => {
  const base = 'card transition-all duration-300'
  const variants = {
    default: 'card-romantic',
    gradient: 'bg-gradient-to-br from-romantic-400 to-dreamy-500 text-white shadow-xl',
    glass: 'glass-card',
    romantic: 'card-romantic bg-gradient-to-br from-romantic-50 to-romantic-100',
    dreamy: 'card-dreamy bg-gradient-to-br from-dreamy-50 to-dreamy-100'
  }
  
  const sizes = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10'
  }
  
  const interactive = props.clickable ? 'cursor-pointer hover:scale-[1.02]' : ''
  
  return `${base} ${variants[props.variant]} ${sizes[props.size]} ${interactive}`
})

const iconClasses = computed(() => {
  const gradients = {
    default: 'bg-gradient-to-r from-romantic-400 to-dreamy-400',
    gradient: 'bg-white/20',
    glass: 'bg-gradient-to-r from-romantic-400 to-dreamy-400',
    romantic: 'bg-gradient-to-r from-romantic-400 to-romantic-500',
    dreamy: 'bg-gradient-to-r from-dreamy-400 to-dreamy-500'
  }
  
  return `p-3 rounded-2xl ${gradients[props.variant]} group-hover:scale-110 transition-transform`
})

const badgeClasses = computed(() => {
  const variants = {
    default: 'badge badge-primary',
    gradient: 'badge bg-white/20 text-white border-white/30',
    glass: 'badge badge-primary',
    romantic: 'badge bg-romantic-100 text-romantic-700 border-romantic-200',
    dreamy: 'badge bg-dreamy-100 text-dreamy-700 border-dreamy-200'
  }
  
  return variants[props.variant]
})

const titleClasses = computed(() => {
  const variants = {
    default: 'text-lg font-semibold text-gray-800',
    gradient: 'text-lg font-semibold text-white',
    glass: 'text-lg font-semibold text-gray-800',
    romantic: 'text-lg font-semibold text-romantic-800',
    dreamy: 'text-lg font-semibold text-dreamy-800'
  }
  
  return variants[props.variant]
})

const descriptionClasses = computed(() => {
  const variants = {
    default: 'text-gray-600 text-sm',
    gradient: 'text-white/90 text-sm',
    glass: 'text-gray-600 text-sm',
    romantic: 'text-romantic-600 text-sm',
    dreamy: 'text-dreamy-600 text-sm'
  }
  
  return variants[props.variant]
})

const statsClasses = computed(() => {
  const variants = {
    default: 'bg-gradient-to-r from-romantic-50 to-dreamy-50 rounded-xl p-3',
    gradient: 'bg-white/10 rounded-xl p-3',
    glass: 'bg-white/50 rounded-xl p-3',
    romantic: 'bg-romantic-100/50 rounded-xl p-3',
    dreamy: 'bg-dreamy-100/50 rounded-xl p-3'
  }
  
  return variants[props.variant]
})

const statValueClasses = computed(() => {
  const variants = {
    default: 'text-2xl font-bold text-romantic-600',
    gradient: 'text-2xl font-bold text-white',
    glass: 'text-2xl font-bold text-gray-800',
    romantic: 'text-2xl font-bold text-romantic-700',
    dreamy: 'text-2xl font-bold text-dreamy-700'
  }
  
  return variants[props.variant]
})

const statLabelClasses = computed(() => {
  const variants = {
    default: 'text-gray-500 text-sm',
    gradient: 'text-white/80 text-sm',
    glass: 'text-gray-600 text-sm',
    romantic: 'text-romantic-500 text-sm',
    dreamy: 'text-dreamy-500 text-sm'
  }
  
  return variants[props.variant]
})

const primaryButtonClasses = computed(() => {
  const variants = {
    default: 'btn btn-romantic btn-sm',
    gradient: 'btn bg-white/20 text-white border-white/30 btn-sm hover:bg-white/30',
    glass: 'btn btn-romantic btn-sm',
    romantic: 'btn btn-romantic btn-sm',
    dreamy: 'btn btn-dreamy btn-sm'
  }
  
  return variants[props.variant]
})

const secondaryButtonClasses = computed(() => {
  const variants = {
    default: 'btn btn-ghost btn-sm',
    gradient: 'btn btn-ghost text-white btn-sm',
    glass: 'btn btn-ghost btn-sm',
    romantic: 'btn btn-ghost text-romantic-600 btn-sm',
    dreamy: 'btn btn-ghost text-dreamy-600 btn-sm'
  }
  
  return variants[props.variant]
})

// 事件处理
const handleClick = () => {
  if (props.clickable) {
    emit('click')
  }
}

const handlePrimaryAction = () => {
  emit('primary-action')
}

const handleSecondaryAction = () => {
  emit('secondary-action')
}
</script>

<style scoped>
/* 组件特定样式 */
.card {
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--color-romantic-400), var(--color-dreamy-400));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:hover::before {
  opacity: 1;
}
</style>
