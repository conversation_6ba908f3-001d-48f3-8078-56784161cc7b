<template>
  <!-- 🌸 精美浪漫Toast容器 - 顶部居中 -->
  <Teleport to="body">
    <div class="toast-container fixed top-6 left-1/2 transform -translate-x-1/2 z-50 space-y-4 pointer-events-none">
      <TransitionGroup
        name="toast"
        tag="div"
        class="space-y-4"
      >
        <div
          v-for="toast in toasts"
          :key="toast.id"
          class="toast-item pointer-events-auto"
        >
          <!-- 🎨 精美DaisyUI Alert组件 -->
          <div 
            class="
              alert shadow-2xl backdrop-blur-lg border border-white/30 
              min-w-96 max-w-md relative overflow-hidden
              bg-gradient-to-br bg-opacity-90
              hover:scale-105 hover:shadow-3xl
              transition-all duration-300 ease-out
              group cursor-pointer
            "
            :class="[
              toast.alertClass,
              `bg-gradient-to-br ${toast.gradient}`,
              toast.shadowColor
            ]"
          >
            <!-- ✨ 装饰性背景渐变 -->
            <div class="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-white/10 opacity-60"></div>

            <!-- ❌ 关闭按钮 - 右上角 -->
            <button
              v-if="toast.closable"
              @click="removeToast(toast.id)"
              class="
                absolute top-2 right-2 z-10
                btn btn-ghost btn-xs btn-circle
                text-white/70 hover:text-white
                hover:bg-white/20 hover:scale-110
                transition-all duration-200
                backdrop-blur-sm
              "
            >
              <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>

            <!-- 💎 主要内容区域 -->
            <div class="relative flex items-start space-x-3 w-full pr-8">
              <!-- 🎭 图标区域 -->
              <div class="flex-shrink-0">
                <div class="
                  w-8 h-8 rounded-full bg-white/20 backdrop-blur-sm
                  flex items-center justify-center
                  border border-white/30
                  group-hover:scale-110 transition-transform duration-300
                ">
                  <span class="text-white text-lg drop-shadow-sm">{{ toast.emoji }}</span>
                </div>
              </div>

              <!-- 📝 文本内容 -->
              <div class="flex-1 min-w-0 pt-0.5">
                <div v-if="toast.title" class="
                  font-bold text-white mb-1 text-sm
                  drop-shadow-sm tracking-wide
                ">
                  {{ toast.title }}
                </div>
                <div class="
                  text-white/95 text-sm leading-relaxed
                  drop-shadow-sm font-medium
                ">
                  {{ toast.message }}
                </div>
              </div>
            </div>
            
            <!-- 🌈 底部装饰渐变线 -->
            <div class="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-white/40 to-transparent"></div>
            
            <!-- ✨ 左侧装饰线 -->
            <div class="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-white/50 via-white/30 to-white/50"></div>
          </div>
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup>
import { useToast } from '../composables/useToast.js'

// 🌸 使用全局Toast状态和方法
const { toasts, remove } = useToast()

// 🗑️ 移除Toast的方法
const removeToast = (id) => {
  remove(id)
}
</script>

<style scoped>
/* 🎭 Toast动画效果 */
.toast-enter-active {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.toast-leave-active {
  transition: all 0.3s cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

.toast-enter-from {
  opacity: 0;
  transform: translateY(-30px) scale(0.8);
}

.toast-leave-to {
  opacity: 0;
  transform: translateY(-20px) scale(0.9);
}

.toast-move {
  transition: transform 0.3s ease;
}

/* 🌟 悬停效果增强 */
.group:hover .absolute {
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 0.8; }
}

/* 💫 脉冲动画 */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}
</style>
