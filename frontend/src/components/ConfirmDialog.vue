<template>
  <!-- 🌸 真正的磨砂玻璃质感确认对话框 -->
  <Teleport to="body">
    <div v-if="visible" class="fixed inset-0 z-50 flex items-center justify-center pointer-events-none">
      <!-- 🌫️ 背景遮罩层 -->
      <div class="absolute inset-0 bg-black/20 backdrop-blur-sm pointer-events-auto" @click="handleCancel"></div>
      <!-- 🌫️ 磨砂玻璃卡片 -->
      <div
        class="
          relative pointer-events-auto
          backdrop-blur-3xl bg-white/10
          border border-white/20
          rounded-3xl shadow-2xl
          overflow-hidden
        "
        style="
          width: 380px;
          max-width: 90vw;
          backdrop-filter: blur(40px);
          -webkit-backdrop-filter: blur(40px);
          background: rgba(255, 255, 255, 0.1);
          box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        "
      >
        <!-- 🎨 内部光泽效果 -->
        <div class="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-transparent opacity-50"></div>

        <!-- 📝 内容区域 -->
        <div class="relative p-6">
          <!-- ❌ 关闭按钮 -->
          <button
            @click="handleCancel"
            class="
              absolute right-3 top-3 z-10
              w-6 h-6 rounded-full
              bg-white/20 hover:bg-white/30
              border border-white/30
              flex items-center justify-center
              text-white/70 hover:text-white
              transition-all duration-200
              backdrop-blur-sm
            "
          >
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>

          <!-- 🎯 图标区域 -->
          <div class="flex justify-center mb-4">
            <div class="
              w-12 h-12 rounded-full
              bg-white/20 backdrop-blur-sm
              flex items-center justify-center
              border border-white/30
              shadow-lg
            ">
              <svg class="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
              </svg>
            </div>
          </div>

          <!-- 📝 内容区域 -->
          <div class="text-center mb-6">
            <h3 class="text-lg font-bold text-gray-800 mb-2 drop-shadow-sm">
              {{ title || '确认操作' }}
            </h3>
            <p class="text-gray-700 text-sm leading-relaxed drop-shadow-sm">
              {{ message }}
            </p>

            <!-- 💡 额外提示 -->
            <div v-if="warning" class="
              mt-3 p-3 rounded-xl
              bg-white/30 backdrop-blur-sm
              border border-white/40
              text-amber-700 text-xs
              shadow-sm
            ">
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-amber-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
                <span class="font-medium">{{ warning }}</span>
              </div>
            </div>
          </div>

          <!-- 🎮 操作按钮 -->
          <div class="flex space-x-3">
            <button
              @click="handleCancel"
              class="
                flex-1 px-4 py-2.5 rounded-xl
                bg-white/30 hover:bg-white/40
                active:bg-white/50
                backdrop-blur-sm
                text-gray-700 hover:text-gray-800 font-medium text-sm
                border border-white/40 hover:border-white/50
                transition-all duration-200
                transform hover:scale-[1.02] active:scale-[0.98]
                hover:shadow-lg
                drop-shadow-sm
                focus:outline-none focus:ring-2 focus:ring-white/30 focus:ring-offset-2
              "
            >
              {{ cancelText || '取消' }}
            </button>

            <button
              @click="handleConfirm"
              :disabled="loading"
              class="
                flex-1 px-4 py-2.5 rounded-xl
                bg-gradient-to-r from-red-500 to-rose-500
                hover:from-red-600 hover:to-rose-600
                active:from-red-700 active:to-rose-700
                text-white font-semibold text-sm
                border border-red-400/50
                shadow-lg hover:shadow-xl
                transition-all duration-200
                transform hover:scale-[1.02] active:scale-[0.98]
                drop-shadow-sm
                disabled:opacity-50 disabled:cursor-not-allowed
                disabled:transform-none
                focus:outline-none focus:ring-2 focus:ring-red-400/50 focus:ring-offset-2
              "
              :class="{ 'animate-pulse': loading }"
            >
              <span v-if="!loading">{{ confirmText || '确认' }}</span>
              <span v-else class="flex items-center space-x-2">
                <svg class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>处理中...</span>
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '确认操作'
  },
  message: {
    type: String,
    required: true
  },
  warning: {
    type: String,
    default: ''
  },
  confirmText: {
    type: String,
    default: '确认'
  },
  cancelText: {
    type: String,
    default: '取消'
  }
})

const emit = defineEmits(['confirm', 'cancel', 'update:visible'])

const loading = ref(false)

const handleConfirm = async () => {
  loading.value = true
  try {
    await emit('confirm')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
  emit('update:visible', false)
}
</script>

<style scoped>
/* 🎭 模态框动画 */
.modal-open .modal-box {
  animation: modalSlideIn 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 🌟 按钮悬停效果 */
.btn:hover {
  transform: translateY(-1px);
}

/* 💫 加载状态 */
.btn.loading {
  pointer-events: none;
}
</style>
