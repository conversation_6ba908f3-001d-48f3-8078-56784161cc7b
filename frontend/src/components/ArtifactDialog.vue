<template>
  <!-- 产物对话框暂时简化，待后续使用DaisyUI重新实现 -->
  <div v-if="visible" class="fixed inset-0 glass-backdrop flex items-center justify-center z-50" @click="onClose">
    <div class="glass-card rounded-lg p-6 max-w-6xl w-full mx-4 max-h-96 overflow-hidden shadow-large" @click.stop>
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold">历史构建产物</h3>
        <button class="btn btn-ghost btn-sm btn-circle" @click="onClose">
          <Icon name="close" :size="16" />
        </button>
      </div>

      <!-- 简化的产物列表 -->
      <div class="modal-scroll-container overflow-y-auto max-h-64">
        <div v-if="!artifacts || artifacts.length === 0" class="text-center text-gray-500 py-8">
          暂无构建产物
        </div>
        <div v-else class="space-y-2">
          <div v-for="artifact in artifacts" :key="artifact.id" class="flex items-center justify-between p-3 bg-gray-50 rounded">
            <div>
              <span class="font-medium">{{ artifact.artifact_name || '未知产物' }}</span>
              <span v-if="artifact.is_starred" class="ml-2 text-yellow-500">⭐</span>
            </div>
            <div class="flex gap-2">
              <button class="btn btn-primary btn-sm" @click="$emit('deploy', artifact)">
                部署
              </button>
              <button class="btn btn-info btn-sm" @click="$emit('download', artifact)">
                下载
              </button>
              <button class="btn btn-error btn-sm" @click="$emit('delete', artifact)">
                删除
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>


<script setup>
import { ref, watch, toRef } from 'vue'
// import { Download, Delete, StarFilled, Upload } from '@element-plus/icons-vue'
import Icon from './Icon.vue'
import { getStarStatus } from '../api/artifact'
import { useScrollLock } from '../composables/useScrollLock'

const props = defineProps({
  visible: Boolean,
  artifacts: Array,
  projectId: Number
})
const emit = defineEmits(['update:visible', 'download', 'star', 'deploy', 'delete'])

// 使用滚动锁定
const visibleRef = toRef(props, 'visible')
useScrollLock(visibleRef)

// 星标状态
const starStatus = ref(null)

// 获取星标状态
const fetchStarStatus = async () => {
  if (!props.projectId) return

  try {
    const res = await getStarStatus(props.projectId)
    if (res.success) {
      starStatus.value = res.data
    }
  } catch (error) {
    console.error('获取星标状态失败:', error)
  }
}

// 监听对话框显示状态，显示时获取星标状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.projectId) {
    fetchStarStatus()
  }
})

const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const onClose = () => emit('update:visible', false)
</script>

<style scoped>
.empty-data {
  padding: 30px 0;
  text-align: center;
}

.commit-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.commit-msg {
  font-size: 13px;
  color: #333;
  line-height: 1.4;
  word-break: break-word;
}

.commit-id {
  display: flex;
  align-items: center;
}

.no-commit-info {
  color: #999;
  font-size: 12px;
  font-style: italic;
}

.table-actions .el-button {
  border-radius: 8px !important;
}
.table-actions .el-button.download-btn {
  background: linear-gradient(90deg, #ff9800 0%, #ffb74d 100%) !important;
  color: #fff !important;
  border: none !important;
  transition: background 0.2s, box-shadow 0.2s;
}
.table-actions .el-button.download-btn:hover, .table-actions .el-button.download-btn:focus {
  background: linear-gradient(90deg, #fb8c00 0%, #ffa726 100%) !important;
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.15);
}
</style>