<template>
  <div v-if="total > 0" class="flex justify-center pagination-fade-in w-full max-w-full overflow-x-auto">
    <div class="join flex-shrink-0">
      <!-- 上一页按钮 -->
      <button
        class="join-item btn btn-sm"
        :disabled="currentPage <= 1"
        @click="handlePageChange(currentPage - 1)"
      >
        <span class="hidden sm:inline">上一页</span>
        <span class="sm:hidden">上页</span>
      </button>
      
      <!-- 页码按钮 -->
      <button
        v-for="page in getPageNumbers()"
        :key="page"
        class="join-item btn btn-sm"
        :class="{ 'btn-romantic': page === currentPage }"
        @click="handlePageChange(page)"
      >
        {{ page }}
      </button>
      
      <!-- 下一页按钮 -->
      <button
        class="join-item btn btn-sm"
        :disabled="currentPage >= totalPages"
        @click="handlePageChange(currentPage + 1)"
      >
        <span class="hidden sm:inline">下一页</span>
        <span class="sm:hidden">下页</span>
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Pagination',
  props: {
    currentPage: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 10
    },
    total: {
      type: Number,
      default: 0
    }
  },
  emits: ['page-change'],
  computed: {
    totalPages() {
      return Math.ceil(this.total / this.pageSize)
    }
  },
  methods: {
    handlePageChange(page) {
      if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
        this.$emit('page-change', page)
      }
    },
    getPageNumbers() {
      const total = this.totalPages
      const current = this.currentPage
      const pages = []

      // 显示当前页前后2页
      const start = Math.max(1, current - 2)
      const end = Math.min(total, current + 2)

      for (let i = start; i <= end; i++) {
        pages.push(i)
      }

      return pages
    }
  }
}
</script>

<style scoped>
/* 分页组件样式增强 */
.join .btn {
  transition: all 0.3s ease;
}

.join .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.join .btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.join .btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* 分页组件淡入动画 */
.pagination-fade-in {
  animation: fadeIn 0.6s ease-out 0.3s both;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
