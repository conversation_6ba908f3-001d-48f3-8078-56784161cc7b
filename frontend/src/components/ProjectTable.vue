<template>
  <div class="table-container w-full max-w-full">
    <!-- 桌面端表格 -->
    <div class="hidden lg:block table-wrapper">
      <div class="table-content">
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th class="min-w-[120px] w-[15%]">项目名称</th>
                <th class="min-w-[150px] w-[20%]">描述</th>
                <th class="min-w-[80px] w-[10%]">环境</th>
                <th class="min-w-[80px] w-[10%]">类型</th>
                <th class="min-w-[150px] w-[20%]">Git 地址</th>
                <th class="min-w-[80px] w-[10%]">部署</th>
                <th class="min-w-[200px] w-[15%]">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="project in projects" :key="project.id" class="hover">
                <td>
                  <div class="flex items-center">
                    <Icon name="folder" :size="16" class="mr-2" />
                    <span class="project-name-link" @click="$emit('edit', project)">{{ project.name }}</span>
                  </div>
                </td>
                <td>
                  <div class="tooltip" :data-tip="project.description">
                    <span class="truncate max-w-[200px] block">{{ project.description }}</span>
                  </div>
                </td>
                <td>
                  <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                       :style="project.environment === 'prod' ? 'background-color: #fbcfe8; color: #ec4899; box-shadow: 0 1px 2px rgba(236, 72, 153, 0.15);' : 'background-color: #e9d5ff; color: #a855f7; box-shadow: 0 1px 2px rgba(168, 85, 247, 0.15);'">
                    {{ project.environment === 'prod' ? '生产环境' : '测试环境' }}
                  </div>
                </td>
                <td>
                  <div class="badge" :class="project.project_type === 'frontend' ? 'badge-warning' : 'badge-info'">
                    {{ project.project_type === 'frontend' ? '前端项目' : '后端项目' }}
                  </div>
                </td>
                <td>
                  <a :href="project.git_url" target="_blank" class="link link-primary flex items-center">
                    <Icon name="link" :size="14" class="mr-1" />
                    <span class="truncate max-w-[180px]">{{ project.git_url }}</span>
                  </a>
                </td>
                <td>
                  <div class="badge" :class="project.deploy_type === 'local' ? 'badge-success' : 'badge-warning'">
                    {{ project.deploy_type === 'local' ? '本地部署' : '远程部署' }}
                  </div>
                </td>
                <td>
                  <div class="table-actions">
                    <button class="btn btn-primary btn-xs lg:btn-sm" @click="$emit('deploy', project)" title="一键部署">
                      <Icon name="upload" :size="14" />
                      <span class="hidden xl:inline">部署</span>
                    </button>
                    <button v-if="!isPythonProject(project)" class="btn btn-warning btn-xs lg:btn-sm" @click="$emit('build', project)" title="构建">
                      <Icon name="tools" :size="14" />
                      <span class="hidden xl:inline">构建</span>
                    </button>
                    <button v-if="!isPythonProject(project)" class="btn btn-success btn-xs lg:btn-sm" @click="$emit('artifact', project)" title="历史构建">
                      <Icon name="files" :size="14" />
                      <span class="hidden xl:inline">历史</span>
                    </button>
                    <button class="btn btn-info btn-xs lg:btn-sm" @click="$emit('logs', project)" title="日志">
                      <Icon name="document" :size="14" />
                      <span class="hidden xl:inline">日志</span>
                    </button>
                    <button class="btn btn-error btn-xs lg:btn-sm" @click="$emit('delete', project)" title="删除">
                      <Icon name="delete" :size="14" />
                      <span class="hidden xl:inline">删除</span>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="pagination-container">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 w-full">
          <div class="flex items-center gap-2 justify-center lg:justify-start">
            <span class="text-sm text-gray-600">共 {{ pagination.total }} 条</span>
            <select
              class="select select-bordered select-sm w-20"
              :value="pagination.pageSize"
              @change="$emit('size-change', parseInt($event.target.value))"
            >
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
            <span class="text-sm text-gray-600">条/页</span>
          </div>
          <div class="join flex-shrink-0">
            <button
              class="join-item btn btn-sm"
              :disabled="pagination.currentPage <= 1"
              @click="$emit('current-change', pagination.currentPage - 1)"
            >
              上一页
            </button>
            <button
              v-for="page in getPageNumbers()"
              :key="page"
              class="join-item btn btn-sm"
              :class="{ 'btn-active': page === pagination.currentPage }"
              @click="$emit('current-change', page)"
            >
              {{ page }}
            </button>
            <button
              class="join-item btn btn-sm"
              :disabled="pagination.currentPage >= Math.ceil(pagination.total / pagination.pageSize)"
              @click="$emit('current-change', pagination.currentPage + 1)"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 移动端卡片视图 -->
    <div class="lg:hidden mobile-cards-container">
      <div class="space-y-4">
        <div v-for="project in projects" :key="project.id" class="mobile-project-card">
          <div class="mobile-card-header">
            <div class="flex items-center flex-1 min-w-0">
              <Icon name="folder" :size="16" class="mr-2 flex-shrink-0" />
              <span class="project-name-link truncate" @click="$emit('edit', project)">{{ project.name }}</span>
            </div>
            <div class="flex items-center gap-2 flex-shrink-0">
              <div class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
                   :style="project.environment === 'prod' ? 'background-color: #fbcfe8; color: #ec4899; box-shadow: 0 1px 2px rgba(236, 72, 153, 0.15);' : 'background-color: #e9d5ff; color: #a855f7; box-shadow: 0 1px 2px rgba(168, 85, 247, 0.15);'">
                {{ project.environment === 'prod' ? '生产' : project.environment === 'test' ? '测试' : '开发' }}
              </div>
              <div class="badge badge-sm" :class="getStatusClass(project.status)">
                {{ getStatusText(project.status) }}
              </div>
            </div>
          </div>

          <div class="mobile-card-content">
            <p class="text-sm text-gray-600 mb-2 line-clamp-2">{{ project.description || '暂无描述' }}</p>
            <div class="flex items-center gap-4 text-xs text-gray-500 mb-3">
              <span>{{ project.project_type === 'frontend' ? '前端' : '后端' }}</span>
              <span>{{ project.deploy_type === 'local' ? '本地部署' : '远程部署' }}</span>
            </div>
            <div class="text-xs text-gray-400 mb-3 truncate">
              {{ project.git_url }}
            </div>
          </div>

          <div class="mobile-card-actions">
            <button class="btn btn-primary btn-xs" @click="$emit('deploy', project)">
              <Icon name="upload" :size="12" />
              部署
            </button>
            <button v-if="!isPythonProject(project)" class="btn btn-warning btn-xs" @click="$emit('build', project)">
              <Icon name="tools" :size="12" />
              构建
            </button>
            <button v-if="!isPythonProject(project)" class="btn btn-success btn-xs" @click="$emit('artifact', project)">
              <Icon name="files" :size="12" />
              历史
            </button>
            <button class="btn btn-info btn-xs" @click="$emit('logs', project)">
              <Icon name="document" :size="12" />
              日志
            </button>
            <button class="btn btn-error btn-xs" @click="$emit('delete', project)">
              <Icon name="delete" :size="12" />
              删除
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import Icon from './Icon.vue'

const props = defineProps({
  projects: Array,
  pagination: Object,
  deployLogs: Array,
  logPanelVisible: Boolean
})

const getLogClass = (log) => {
  if (!log) return ''

  const logLower = log.toLowerCase()
  if (logLower.includes('success') || logLower.includes('成功')) {
    return 'log-success'
  } else if (logLower.includes('error') || logLower.includes('错误') || logLower.includes('失败')) {
    return 'log-error'
  } else if (logLower.includes('info') || logLower.includes('信息')) {
    return 'log-info'
  } else if (logLower.includes('start') || logLower.includes('开始')) {
    return 'log-start'
  }
  return ''
}

const getPageNumbers = () => {
  const total = Math.ceil(props.pagination.total / props.pagination.pageSize)
  const current = props.pagination.currentPage
  const pages = []

  // 显示当前页前后2页
  const start = Math.max(1, current - 2)
  const end = Math.min(total, current + 2)

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
}

// 移动端卡片辅助方法
const getEnvironmentClass = (environment) => {
  switch (environment) {
    case 'prod': return 'badge-error'
    case 'test': return 'badge-warning'
    default: return 'badge-info'
  }
}

const getStatusClass = (status) => {
  switch (status) {
    case 'online': return 'badge-success'
    case 'warning': return 'badge-warning'
    case 'offline': return 'badge-error'
    default: return 'badge-ghost'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'online': return '在线'
    case 'warning': return '警告'
    case 'offline': return '离线'
    default: return '未知'
  }
}

// 判断是否为 Python 项目
const isPythonProject = (project) => {
  return project.build_config?.language === 'python'
}
</script>

<style scoped>
.table-actions {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
}

.project-name-link {
  color: #3a5cff;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s;
}

.project-name-link:hover {
  color: #5D25FF;
  text-decoration: underline;
}

/* 移动端卡片样式 */
.mobile-cards-container {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.mobile-project-card {
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.mobile-project-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.mobile-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  min-width: 0;
}

.mobile-card-content {
  margin-bottom: 1rem;
}

.mobile-card-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  align-items: center;
}

.mobile-card-actions .btn {
  flex: 1;
  min-width: 0;
  font-size: 0.75rem;
}

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .mobile-project-card {
    padding: 0.75rem;
  }

  .mobile-card-actions .btn {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
  }
}
</style> 