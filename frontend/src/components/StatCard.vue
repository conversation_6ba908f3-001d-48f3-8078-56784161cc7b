<template>
  <!-- 📊 统计卡片组件 -->
  <div :class="cardClasses">
    <div class="flex items-center justify-between">
      <!-- 统计信息 -->
      <div>
        <div :class="valueClasses">{{ value }}</div>
        <div :class="labelClasses">{{ label }}</div>
        <div v-if="change" :class="changeClasses">
          <Icon :name="changeIcon" :size="14" />
          {{ Math.abs(change) }}%
        </div>
      </div>
      
      <!-- 图标 -->
      <div v-if="icon" :class="iconClasses">
        <Icon :name="icon" :size="iconSize" />
      </div>
    </div>
    
    <!-- 趋势图或额外信息 -->
    <div v-if="$slots.default" class="mt-4">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import Icon from './Icon.vue'

const props = defineProps({
  // 统计数据
  value: {
    type: [String, Number],
    required: true
  },
  label: {
    type: String,
    required: true
  },
  
  // 变化百分比
  change: {
    type: Number,
    default: null
  },
  
  // 图标
  icon: String,
  iconSize: {
    type: Number,
    default: 32
  },
  
  // 样式变体
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'gradient', 'romantic', 'dreamy', 'success', 'warning', 'error'].includes(value)
  },
  
  // 尺寸
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg'].includes(value)
  }
})

// 计算样式类
const cardClasses = computed(() => {
  const base = 'stat-card'
  
  const variants = {
    default: 'bg-white/90',
    gradient: 'stat-card-gradient from-romantic-400 to-dreamy-500',
    romantic: 'stat-card-gradient from-romantic-400 to-romantic-500',
    dreamy: 'stat-card-gradient from-dreamy-400 to-dreamy-500',
    success: 'stat-card-gradient from-green-400 to-emerald-500',
    warning: 'stat-card-gradient from-yellow-400 to-orange-500',
    error: 'stat-card-gradient from-red-400 to-pink-500'
  }
  
  const sizes = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  }
  
  return `${base} ${variants[props.variant]} ${sizes[props.size]}`
})

const valueClasses = computed(() => {
  const isGradient = props.variant !== 'default'
  const textColor = isGradient ? 'text-white' : 'text-gray-800'
  
  const sizes = {
    sm: 'text-xl',
    md: 'text-2xl',
    lg: 'text-3xl'
  }
  
  return `${sizes[props.size]} font-bold ${textColor}`
})

const labelClasses = computed(() => {
  const isGradient = props.variant !== 'default'
  const textColor = isGradient ? 'text-white/90' : 'text-gray-600'
  
  return `text-sm ${textColor}`
})

const changeClasses = computed(() => {
  if (!props.change) return ''
  
  const isGradient = props.variant !== 'default'
  const isPositive = props.change > 0
  
  if (isGradient) {
    return `text-xs text-white/80 flex items-center gap-1 mt-1`
  }
  
  const color = isPositive ? 'text-green-600' : 'text-red-600'
  return `text-xs ${color} flex items-center gap-1 mt-1`
})

const iconClasses = computed(() => {
  const isGradient = props.variant !== 'default'
  const iconColor = isGradient ? 'text-white/80' : 'text-gray-400'
  
  return `stat-card-icon ${iconColor}`
})

const changeIcon = computed(() => {
  if (!props.change) return ''
  return props.change > 0 ? 'trending-up' : 'trending-down'
})
</script>

<style scoped>
.stat-card {
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-card-gradient {
  position: relative;
  overflow: hidden;
}

.stat-card-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  pointer-events: none;
}
</style>
