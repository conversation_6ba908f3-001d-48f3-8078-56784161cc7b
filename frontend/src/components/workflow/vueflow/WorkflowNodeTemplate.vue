<template>
  <div
    class="workflow-node-template vue-flow__node-output"
    :draggable="true"
    @dragstart="onDragStart"
    @dragend="onDragEnd"
  >
    <!-- 卡片式布局 - 简洁美观的无边框设计 -->
    <div class="node-card bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 cursor-grab active:cursor-grabbing hover:scale-[1.02] hover:-translate-y-1">
      <!-- 图标区域 -->
      <div class="p-3 pb-2">
        <div
          class="w-12 h-12 rounded-xl flex items-center justify-center text-white text-xl font-semibold mx-auto shadow-md"
          :style="{
            backgroundColor: color,
            boxShadow: `0 4px 12px ${color}30`
          }"
        >
          {{ icon }}
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="px-3 pb-3 text-center">
        <div class="text-sm font-medium text-gray-900 mb-1">
          {{ name }}
        </div>
        <div class="text-xs text-gray-500 leading-tight">
          {{ description }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  type: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    required: true
  },
  color: {
    type: String,
    required: true
  }
})

const isDragging = ref(false)

const onDragStart = (event) => {
  isDragging.value = true

  if (event.dataTransfer) {
    event.dataTransfer.setData("application/vueflow", props.type)
    event.dataTransfer.effectAllowed = "move"

    // 计算鼠标在侧边栏节点中的相对位置（百分比）
    const nodeRect = event.currentTarget.getBoundingClientRect()
    const relativeX = (event.clientX - nodeRect.left) / nodeRect.width
    const relativeY = (event.clientY - nodeRect.top) / nodeRect.height

    // 将相对位置百分比存储到dataTransfer中，供drop时使用
    event.dataTransfer.setData("application/relative-position", JSON.stringify({
      relativeX,
      relativeY
    }))

    console.log('📍 记录相对位置百分比:', {
      relativeX: (relativeX * 100).toFixed(1) + '%',
      relativeY: (relativeY * 100).toFixed(1) + '%',
      nodeSize: { width: nodeRect.width, height: nodeRect.height }
    })
  }

  // 添加拖拽开始的视觉反馈
  event.target.style.opacity = '0.5'

  console.log('🚀 开始拖拽节点:', props.type)
}

const onDragEnd = (event) => {
  isDragging.value = false
  
  // 恢复透明度
  event.target.style.opacity = '1'
  
  console.log('🏁 结束拖拽节点:', props.type)
}
</script>

<style scoped>
.workflow-node-template {
  width: 100%;
  /* 强制移除所有边框 */
  border: none !important;
  border-width: 0 !important;
  border-color: transparent !important;
  outline: none !important;
}

.node-card {
  min-height: 100px;
  /* 移除重复的transition，使用Tailwind的transition-all */
}

/* 移除重复的悬浮效果，使用Tailwind类 */

/* 拖拽时的样式 */
.workflow-node-template.dragging {
  opacity: 0.5;
}
</style>
