<template>
  <div 
    class="custom-workflow-node"
    :class="{ 'selected': selected }"
    @click="$emit('nodeClick', { node: { id, data } })"
  >
    <!-- 极简节点 - 最高性能 -->
    <div class="node-content">
      <span class="node-icon">{{ data.icon || '📦' }}</span>
      <span class="node-title">{{ label || data.nodeType || 'Node' }}</span>
    </div>

    <!-- Vue Flow Handles -->
    <Handle
      id="target"
      type="target"
      :position="Position.Top"
      class="handle-target"
      :style="{ left: '50%', transform: 'translateX(-50%)' }"
    />
    <Handle
      id="source"
      type="source"
      :position="Position.Bottom"
      class="handle-source"
      :style="{ left: '50%', transform: 'translateX(-50%)' }"
    />
  </div>
</template>

<script setup>
import { Handle, Position } from '@vue-flow/core'

const props = defineProps({
  id: {
    type: String,
    required: true
  },
  label: {
    type: String,
    default: ''
  },
  data: {
    type: Object,
    default: () => ({})
  },
  selected: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['nodeClick'])
</script>

<style scoped>
/* 纯长方形样式 - 极致性能 */
.custom-workflow-node {
  width: 150px;
  height: 60px;
  position: relative;
}

.node-content {
  width: 100%;
  height: 100%;
  background: #f0f0f0;
  border: 1px solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 14px;
  color: #333;
}

.node-icon {
  font-size: 16px;
}

.node-title {
  font-size: 13px;
  font-weight: 500;
}

.custom-workflow-node:hover .node-content {
  border-color: #3b82f6;
}

.custom-workflow-node.selected .node-content {
  border-color: #3b82f6;
  background: #e3f2fd;
}

/* 移除不需要的样式 */

/* 纯长方形Handle样式 */
.handle-target,
.handle-source {
  width: 6px;
  height: 6px;
  background: #666;
  border: none;
}

.handle-target {
  top: -3px;
}

.handle-source {
  bottom: -3px;
}
</style>
