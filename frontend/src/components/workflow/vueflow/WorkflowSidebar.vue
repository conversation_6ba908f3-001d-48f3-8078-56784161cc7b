<template>
  <aside class="w-80 bg-white border-r border-gray-200 flex flex-col h-full">
    <!-- 标题 - 固定高度 -->
    <div class="p-4 border-b border-gray-200 flex-shrink-0">
      <h2 class="text-lg font-semibold text-gray-800">节点组件</h2>
      <p class="text-sm text-gray-600 mt-1">拖拽到画布创建节点</p>
    </div>

    <!-- 节点分类 - 独立滚动区域 -->
    <div class="flex-1 overflow-y-auto min-h-0 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
      <div v-for="category in categories" :key="category.key" class="mb-6">
        <div
          class="px-4 py-2 bg-gray-50 border-b border-gray-100 cursor-pointer hover:bg-gray-100 transition-colors"
          @click="toggleCategory(category.key)"
        >
          <div class="flex items-center justify-between">
            <h3 class="text-sm font-medium text-gray-700">
              <span class="mr-2">{{ category.icon }}</span>
              {{ category.name }}
            </h3>
            <div class="flex items-center gap-2">
              <!-- 节点数量 -->
              <span class="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded-full">
                {{ getNodesByCategory(category.key).length }}
              </span>
              <!-- 折叠图标 -->
              <span
                class="text-gray-400 transition-transform duration-200"
                :class="{ 'rotate-180': !collapsedCategories[category.key] }"
              >
                ▼
              </span>
            </div>
          </div>
        </div>

        <!-- 节点内容 - 可折叠 -->
        <div
          v-show="!collapsedCategories[category.key]"
          class="transition-all duration-200"
        >
          <!-- 两排网格布局 -->
          <div class="p-3 grid grid-cols-2 gap-3">
            <WorkflowNodeTemplate
              v-for="nodeType in getNodesByCategory(category.key)"
              :key="nodeType.type"
              :type="nodeType.type"
              :name="nodeType.name"
              :description="nodeType.description"
              :icon="nodeType.icon"
              :color="nodeType.color"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 底部信息 -->
    <div class="p-4 border-t border-gray-200 bg-gray-50">
      <div class="text-xs text-gray-500">
        <p>节点总数: {{ nodeTypes.length }}</p>
        <p>当前画布: {{ currentNodeCount }} 个节点</p>
      </div>
    </div>
  </aside>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useWorkflowStore } from '@/stores/workflow'
import WorkflowNodeTemplate from './WorkflowNodeTemplate.vue'

const store = useWorkflowStore()

// 🎯 当前项目类型（从项目上下文获取，不再需要手动选择）
// const currentProjectType = ref(store.getCurrentProject.type)

// 🎯 折叠状态管理
const collapsedCategories = ref({
  control: false,    // 控制节点 - 默认展开
  source: false,     // 数据源 - 默认展开
  build: false,      // 构建部署 - 默认展开
  test: true,        // 测试验证 - 默认折叠 ⭐
  logic: false,      // 逻辑控制 - 默认展开
  utility: false     // 工具函数 - 默认展开
})

// 节点分类
const categories = [
  {
    key: 'control',
    name: '控制节点',
    icon: '🎮'
  },
  {
    key: 'source',
    name: '数据源',
    icon: '📥'
  },
  {
    key: 'build',
    name: '构建部署',
    icon: '🔨'
  },
  {
    key: 'test',
    name: '测试验证',
    icon: '🧪'
  },
  {
    key: 'logic',
    name: '逻辑控制',
    icon: '🧠'
  },
  {
    key: 'utility',
    name: '工具函数',
    icon: '🛠️'
  }
]

// 计算属性
const nodeTypes = computed(() => store.getNodeTypes)
const currentNodeCount = computed(() => store.getMessages().length)

// 根据分类获取节点
const getNodesByCategory = (category) => {
  return store.getNodeTypesByCategory(category)
}

// 🎯 项目类型切换处理（已移除，现在从项目上下文自动获取）
// const onProjectTypeChange = () => {
//   store.setProjectType(currentProjectType.value)
//   console.log(`🎯 项目类型已切换为: ${currentProjectType.value}`)
// }

// 🎯 获取项目类型标签（已移除，不再需要）
// const getProjectTypeLabel = (type) => {
//   const labels = {
//     'go': 'Go项目',
//     'python': 'Python项目',
//     'vue': 'Vue项目',
//     'nodejs': 'Node.js项目',
//     'java': 'Java项目'
//   }
//   return labels[type] || type
// }

// 🎯 切换分类折叠状态
const toggleCategory = (categoryKey) => {
  collapsedCategories.value[categoryKey] = !collapsedCategories.value[categoryKey]
  console.log(`📁 ${categoryKey} 分类${collapsedCategories.value[categoryKey] ? '折叠' : '展开'}`)
}
</script>

<style scoped>
/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
