<template>
  <div class="enhanced-rectangle-node">
    <div class="node-header">
      <span class="node-icon">✨</span>
      <span class="node-title">{{ data.label || '美化节点' }}</span>
    </div>
    <div class="node-content">
      <p class="node-description">{{ data.description || '这是一个美化的节点' }}</p>
    </div>
    <Handle type="target" position="top" :style="{ left: '50%', transform: 'translateX(-50%)' }" />
    <Handle type="source" position="bottom" :style="{ left: '50%', transform: 'translateX(-50%)' }" />
  </div>
</template>

<script setup>
import { Handle } from '@vue-flow/core'

defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})
</script>

<style scoped>
.enhanced-rectangle-node {
  width: 160px !important;
  min-height: 80px !important;
  border: 2px solid #3b82f6 !important;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
  border-radius: 8px !important;
  position: relative !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15) !important;
  transition: all 0.2s ease !important;
  box-sizing: border-box !important;
}

.enhanced-rectangle-node:hover {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25) !important;
  transform: translateY(-1px) !important;
}

.node-header {
  display: flex !important;
  align-items: center !important;
  padding: 8px 12px !important;
  border-bottom: 1px solid #e5e7eb !important;
  background: rgba(59, 130, 246, 0.05) !important;
  border-radius: 6px 6px 0 0 !important;
}

.node-icon {
  font-size: 14px !important;
  margin-right: 6px !important;
}

.node-title {
  font-size: 12px !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
}

.node-content {
  padding: 8px 12px !important;
}

.node-description {
  font-size: 10px !important;
  color: #6b7280 !important;
  margin: 0 !important;
  line-height: 1.3 !important;
}
</style>
