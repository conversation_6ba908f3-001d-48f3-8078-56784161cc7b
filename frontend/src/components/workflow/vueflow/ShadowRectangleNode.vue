<template>
  <div class="shadow-rectangle-node">
    <div class="node-content">
      <span class="node-icon">🌑</span>
      <span class="node-label">{{ data.label || '阴影节点' }}</span>
    </div>
    <Handle type="target" position="top" :style="{ left: '50%', transform: 'translateX(-50%)' }" />
    <Handle type="source" position="bottom" :style="{ left: '50%', transform: 'translateX(-50%)' }" />
  </div>
</template>

<script setup>
import { Handle } from '@vue-flow/core'

defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})
</script>

<style scoped>
.shadow-rectangle-node {
  width: 140px !important;
  height: 75px !important;
  background: linear-gradient(135deg, #374151 0%, #1f2937 100%) !important;
  border: 1px solid #4b5563 !important;
  border-radius: 8px !important;
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.3),
    0 6px 12px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  transition: all 0.3s ease !important;
  box-sizing: border-box !important;
}

.shadow-rectangle-node:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 15px 35px rgba(0, 0, 0, 0.4),
    0 8px 16px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.node-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.node-icon {
  font-size: 16px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
}

.node-label {
  font-size: 12px;
  font-weight: 600;
  color: #f9fafb;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}
</style>
