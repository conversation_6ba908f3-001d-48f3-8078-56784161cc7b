<template>
  <div class="plain-rectangle-node">
    <div class="rectangle-content">
      {{ data.label || '长方形节点' }}
    </div>
    <Handle type="target" position="top" :style="{ left: '50%', transform: 'translateX(-50%)' }" />
    <Handle type="source" position="bottom" :style="{ left: '50%', transform: 'translateX(-50%)' }" />
  </div>
</template>

<script setup>
import { Handle } from '@vue-flow/core'

defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})
</script>

<style scoped>
.plain-rectangle-node {
  width: 120px !important;
  height: 60px !important;
  border: 2px solid #6b7280 !important;
  background: white !important;
  border-radius: 4px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: relative !important;
  box-sizing: border-box !important;
}

.rectangle-content {
  font-size: 12px !important;
  color: #374151 !important;
  text-align: center !important;
  padding: 4px !important;
  pointer-events: none !important;
}
</style>
