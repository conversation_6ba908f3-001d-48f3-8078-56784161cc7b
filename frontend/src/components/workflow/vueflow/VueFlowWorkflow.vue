<template>
  <div class="vue-flow-workflow h-full flex overflow-hidden">
    <!-- 侧边栏 - 固定宽度，独立滚动 -->
    <WorkflowSidebar class="flex-shrink-0" />

    <!-- 主要工作区 - 占用剩余空间 -->
    <div class="flex-1 flex flex-col min-w-0 overflow-hidden">
      <!-- 工具栏 - 固定高度 -->
      <div class="toolbar bg-white border-b border-gray-200 px-4 py-2 flex items-center justify-between flex-shrink-0">
        <div class="flex items-center gap-4">
          <h1 class="text-lg font-semibold text-gray-800">工作流设计器</h1>
          <div class="text-sm text-gray-500">
            节点: {{ nodes.length }} | 连线: {{ edges.length }}
          </div>
        </div>
        
        <div class="flex items-center gap-2">
          <!-- 缩放控制 -->
          <div class="flex items-center gap-1 bg-gray-100 rounded-lg p-1">
            <button
              v-for="zoom in zoomLevels"
              :key="zoom"
              @click="setZoom(zoom / 100)"
              class="px-2 py-1 text-xs rounded transition-colors"
              :class="currentZoom === Math.round(zoom) ? 'bg-blue-500 text-white' : 'hover:bg-gray-200'"
            >
              {{ zoom }}%
            </button>
          </div>

          <!-- 实时缩放显示 -->
          <div class="flex items-center gap-2 px-3 py-1 bg-blue-50 border border-blue-200 rounded-lg">
            <span class="text-xs text-blue-600 font-medium">当前:</span>
            <button
              @click="setZoom(1)"
              class="text-sm font-semibold text-blue-700 hover:text-blue-900 transition-colors cursor-pointer"
              :title="'点击重置到100%'"
            >
              {{ realTimeZoom }}%
            </button>
          </div>

          <!-- 操作按钮 -->
          <button @click="fitView" class="btn-toolbar">
            适应视图
          </button>
          <button @click="clearWorkflow" class="btn-toolbar">
            清空
          </button>
          <button @click="saveWorkflow" class="btn-toolbar btn-primary">
            保存
          </button>
        </div>
      </div>
      
      <!-- Vue Flow 画布 - 占满剩余高度，防止溢出 -->
      <div class="flex-1 relative overflow-hidden min-h-0">
        <VueFlow
          v-model="elements"
          class="workflow-canvas h-full w-full"
          :snap-to-grid="true"
          :select-nodes-on-drag="true"
          :only-render-visible-elements="true"
          :max-zoom="50"
          :min-zoom="0.05"
          :connection-mode="'loose'"
          :connect-on-click="false"
          :auto-connect="true"
          :is-valid-connection="isValidConnection"
          @dragover="onDragOver"
          @drop="onDrop"
          @nodeClick="onNodeClick"
          @nodeDragStart="onNodeDragStart"
          @nodeDragStop="onNodeDragStop"
          @nodesChange="onChange"
          @connect="onConnect"
          @connectStart="onConnectStart"
          @connectEnd="onConnectEnd"
          @pane-ready="onPaneReady"
          @viewportChange="onViewportChange"
        >
          <!-- 背景 -->
          <Background pattern-color="#e5e7eb" :gap="16" :size="1" />



          <!-- 自定义节点模板 -->
          <template #node-custom="props">
            <CustomWorkflowNode
              v-bind="props"
              @node-click="onNodeClick"
            />
          </template>

          <!-- 测试节点模板 -->
          <template #node-rectangle="props">
            <PlainRectangleNode
              v-bind="props"
              @node-click="onNodeClick"
            />
          </template>

          <template #node-empty="props">
            <EmptyRectangleNode
              v-bind="props"
              @node-click="onNodeClick"
            />
          </template>

          <template #node-enhanced="props">
            <EnhancedRectangleNode
              v-bind="props"
              @node-click="onNodeClick"
            />
          </template>

          <template #node-colored="props">
            <ColoredRectangleNode
              v-bind="props"
              @node-click="onNodeClick"
            />
          </template>

          <template #node-gradient="props">
            <GradientRectangleNode
              v-bind="props"
              @node-click="onNodeClick"
            />
          </template>

          <template #node-animated="props">
            <AnimatedRectangleNode
              v-bind="props"
              @node-click="onNodeClick"
            />
          </template>

          <template #node-shadow="props">
            <ShadowRectangleNode
              v-bind="props"
              @node-click="onNodeClick"
            />
          </template>
          
          <!-- 控制器 -->
          <Controls />
          
          <!-- 小地图 -->
          <MiniMap 
            v-show="!selectedNode"
            :node-color="getNodeColor"
            :mask-color="'rgba(255, 255, 255, 0.8)'"
          />
        </VueFlow>
      </div>
    </div>

    <!-- 节点配置面板 -->
    <NodeConfigPanel
      :visible="showConfigPanel"
      :selected-node="selectedNode"
      @close="onConfigPanelClose"
      @save="onConfigPanelSave"
      @delete="onConfigPanelDelete"
    />
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted } from 'vue'
import { VueFlow, useVueFlow } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import { Controls } from '@vue-flow/controls'
import { MiniMap } from '@vue-flow/minimap'

import WorkflowSidebar from './WorkflowSidebar.vue'
import NodeConfigPanel from './NodeConfigPanel.vue'
import CustomWorkflowNode from './CustomWorkflowNode.vue'
import PlainRectangleNode from './PlainRectangleNode.vue'
import EmptyRectangleNode from './EmptyRectangleNode.vue'
import EnhancedRectangleNode from './EnhancedRectangleNode.vue'
import ColoredRectangleNode from './ColoredRectangleNode.vue'
import GradientRectangleNode from './GradientRectangleNode.vue'
import AnimatedRectangleNode from './AnimatedRectangleNode.vue'
import ShadowRectangleNode from './ShadowRectangleNode.vue'

import { createWorkflowNode } from '@/utils/createWorkflowNode'
import { useWorkflowStore } from '@/stores/workflow'

// Vue Flow composable
const {
  addEdges,
  addNodes,
  onConnect,
  onPaneReady,
  project,
  fitView: vueFlowFitView,
  setViewport,
  getViewport,
  vueFlowRef
} = useVueFlow()

// Store
const store = useWorkflowStore()

// 响应式数据
const elements = ref([])
const selectedNode = ref(null)
const selectedEdge = ref(null)
const currentZoom = ref(100)
const realTimeZoom = ref(100)
const showConfigPanel = ref(false)

// 缩放级别
const zoomLevels = [25, 50, 75, 100, 150, 200]

// 计算属性
const nodes = computed(() => elements.value.filter(el => !el.source && !el.target))
const edges = computed(() => elements.value.filter(el => el.source && el.target))

// 拖拽处理
const onDragOver = (event) => {
  event.preventDefault()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = "move"
  }
}

const onDrop = (event) => {
  console.log('🎯 节点拖拽到画布')

  // 获取当前画布的缩放比例
  const viewport = getViewport()
  const currentZoomLevel = viewport.zoom

  console.log('📏 当前画布缩放比例:', currentZoomLevel)

  createWorkflowNode(event, addNodes, project, store, currentZoomLevel)
}

// 拖拽状态跟踪
const isDragging = ref(false)
const dragStartTime = ref(0)

// 节点拖拽开始
const onNodeDragStart = () => {
  isDragging.value = true
  dragStartTime.value = Date.now()
  console.log('🔄 开始拖拽节点')
}

// 节点拖拽结束
const onNodeDragStop = () => {
  // 延迟重置拖拽状态，避免拖拽结束后立即触发点击
  setTimeout(() => {
    isDragging.value = false
  }, 100)
  console.log('✋ 结束拖拽节点')
}

// 节点单击
const onNodeClick = ({ node }) => {
  // 如果正在拖拽或刚结束拖拽，不触发配置面板
  if (isDragging.value || (Date.now() - dragStartTime.value) < 200) {
    console.log('🚫 拖拽中，忽略点击事件')
    return
  }

  selectedNode.value = node
  selectedEdge.value = null
  showConfigPanel.value = true
  console.log('🎯 单击节点:', node.id)
  console.log('📋 显示节点配置面板:', node.type)
}

// 节点变化处理（完全复制Facebook Messenger的实现）
const onChange = (event) => {
  event.forEach((element) => {
    if (element.type == "remove") {
      store.layers.messages = store.layers.messages.filter((item) => {
        return item.id != element.id;
      });
    }
  });
}

// 连接状态
const isConnecting = ref(false)

// 连接开始处理
const onConnectStart = (event, params) => {
  isConnecting.value = true
  console.log('🔗 开始连接:', params)

  // 安全解构参数
  if (params) {
    const { nodeId, handleId, handleType } = params
    console.log('🔗 连接参数:', { nodeId, handleId, handleType })
  }
}

// 连接结束处理
const onConnectEnd = (event) => {
  console.log('🔗 连接结束')

  // 延迟重置连接状态，确保连接完成
  setTimeout(() => {
    isConnecting.value = false

    // 强制重置连接状态 - 移除所有连接相关的CSS类
    const canvas = document.querySelector('.workflow-canvas')
    if (canvas) {
      // 移除所有连接状态的类
      const connectingElements = canvas.querySelectorAll('.connecting, .vue-flow__handle.connecting')
      connectingElements.forEach(el => {
        el.classList.remove('connecting')
      })

      // 移除连接线
      const connectionLines = canvas.querySelectorAll('.vue-flow__connection-line, .vue-flow__connectionline')
      connectionLines.forEach(line => {
        line.remove()
      })
    }
  }, 100)
}

// 🔗 连接验证函数
const isValidConnection = (connection) => {
  try {
    // 1. 检查基本参数
    if (!connection || !connection.source || !connection.target) {
      console.log('❌ 连接验证失败：缺少必要参数', connection)
      return false
    }

    // 2. 检查是否连接到自己
    if (connection.source === connection.target) {
      console.log('❌ 连接验证失败：不能连接到自己')
      return false
    }

    // 3. 检查连接方向 - 不允许连接到source handle
    if (connection.targetHandle === 'source') {
      console.log('❌ 连接验证失败：不能连接到出口handle')
      return false
    }

    // 4. 检查连接方向 - target handle不能作为起点
    if (connection.sourceHandle === 'target') {
      console.log('❌ 连接验证失败：入口handle不能作为连接起点')
      return false
    }

    // 5. 检查重复连接
    const existingEdge = edges.value.find(edge =>
      edge.source === connection.source &&
      edge.target === connection.target &&
      edge.sourceHandle === connection.sourceHandle &&
      edge.targetHandle === connection.targetHandle
    )

    if (existingEdge) {
      console.log('❌ 连接验证失败：连接已存在', existingEdge)
      return false
    }

    console.log('✅ 连接验证通过', connection)
    return true
  } catch (error) {
    console.error('❌ 连接验证过程中发生错误:', error)
    return false
  }
}

// 连接处理 - 添加安全检查
onConnect((params) => {
  console.log('🔗 onConnect 事件被触发!', params)

  try {
    // 参数检查
    if (!params) {
      console.error('❌ onConnect 参数为空')
      return
    }

    // 再次验证连接（双重保险）
    if (!isValidConnection(params)) {
      console.error('❌ 连接验证失败，取消创建连线')
      return
    }

    // 设置连线属性
    params.type = 'default'
    params.animated = true

    // 安全地添加边
    addEdges([params])
    console.log('✅ 连线创建成功:', params)

  } catch (error) {
    console.error('❌ 创建连线失败:', error)
    // 确保即使出错也要重置状态
  } finally {
    // 连接完成后重置状态
    isConnecting.value = false
  }
})

// 画布就绪
onPaneReady(({ fitView }) => {
  fitView()
  updateZoomDisplay()
})

// 视口变化监听（实时更新缩放显示）
const onViewportChange = (viewport) => {
  currentZoom.value = Math.round(viewport.zoom * 100)
  realTimeZoom.value = Math.round(viewport.zoom * 1000) / 10 // 保留一位小数
}

// 工具栏功能
const fitView = () => {
  vueFlowFitView()
  setTimeout(updateZoomDisplay, 100)
}

const setZoom = (zoomLevel) => {
  setViewport({ x: 0, y: 0, zoom: zoomLevel })
  updateZoomDisplay()
}

const updateZoomDisplay = () => {
  const viewport = getViewport()
  currentZoom.value = Math.round(viewport.zoom * 100)
  realTimeZoom.value = Math.round(viewport.zoom * 1000) / 10 // 保留一位小数
}

const clearWorkflow = () => {
  if (confirm('确定要清空当前工作流吗？')) {
    elements.value = []
    selectedNode.value = null
    selectedEdge.value = null
    store.layers.messages = []
  }
}

const saveWorkflow = () => {
  const workflowData = {
    nodes: nodes.value,
    edges: edges.value,
    metadata: {
      name: 'GoShip 工作流',
      description: '使用 Vue Flow 创建的工作流',
      version: '1.0.0',
      createdAt: new Date().toISOString()
    }
  }
  
  const blob = new Blob([JSON.stringify(workflowData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `goship-workflow-${Date.now()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  console.log('💾 工作流已保存')
}

// 小地图节点颜色
const getNodeColor = (node) => {
  return node.data?.color || '#6b7280'
}

// 项目上下文管理
const projectContext = ref(null)

const setProjectContext = (project) => {
  projectContext.value = project
  console.log('🎯 设置项目上下文:', project)

  // 设置工作流Store的项目类型，用于智能节点过滤
  if (project && project.language) {
    store.setProjectType(project.language)
  }
}

const getWorkflowConfig = () => {
  return {
    nodes: nodes.value,
    edges: edges.value,
    metadata: {
      projectId: projectContext.value?.id,
      projectName: projectContext.value?.name,
      language: projectContext.value?.language,
      createdAt: new Date().toISOString()
    }
  }
}

const loadWorkflow = (config) => {
  if (config && config.nodes && config.edges) {
    // 清空现有节点和边
    elements.value = []

    // 加载节点
    if (config.nodes.length > 0) {
      addNodes(config.nodes)
    }

    // 加载边
    if (config.edges.length > 0) {
      addEdges(config.edges)
    }

    console.log('📥 工作流配置已加载:', config)

    // 适应视图
    nextTick(() => {
      fitView()
    })
  }
}

// 🎯 配置面板事件处理
const onConfigPanelClose = () => {
  showConfigPanel.value = false
  selectedNode.value = null
  console.log('📋 关闭节点配置面板')
}

const onConfigPanelSave = ({ nodeId, data }) => {
  // 更新节点数据
  const nodeIndex = nodes.value.findIndex(node => node.id === nodeId)
  if (nodeIndex !== -1) {
    nodes.value[nodeIndex].data = { ...nodes.value[nodeIndex].data, ...data }
    console.log('💾 节点配置已更新:', nodeId, data)
  }
}

const onConfigPanelDelete = (nodeId) => {
  // 删除节点
  const nodeIndex = nodes.value.findIndex(node => node.id === nodeId)
  if (nodeIndex !== -1) {
    nodes.value.splice(nodeIndex, 1)

    // 删除相关连接线
    edges.value = edges.value.filter(edge =>
      edge.source !== nodeId && edge.target !== nodeId
    )

    console.log('🗑️ 节点已删除:', nodeId)
  }

  showConfigPanel.value = false
  selectedNode.value = null
}

// 暴露方法给父组件
defineExpose({
  setProjectContext,
  getWorkflowConfig,
  loadWorkflow,
  fitView,
  clearWorkflow,
  saveWorkflow
})

// 初始化
onMounted(() => {
  console.log('🚀 Vue Flow 工作流组件已加载')
})
</script>

<style>
/* 导入 Vue Flow 官方样式 */
@import '@vue-flow/core/dist/style.css';
@import '@vue-flow/core/dist/theme-default.css';
@import '@vue-flow/controls/dist/style.css';
@import '@vue-flow/minimap/dist/style.css';
</style>

<style scoped>
.vue-flow-workflow {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.toolbar {
  min-height: 60px;
}

.btn-toolbar {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.5rem;
  border: 1px solid #d1d5db;
  background-color: white;
  transition: all 0.2s ease;
}

.btn-toolbar:hover {
  background-color: #f9fafb;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.workflow-canvas {
  background-color: #f8fafc;
  height: 100%;
  width: 100%;
}

/* Vue Flow 性能优化 */
.workflow-canvas :deep(.vue-flow__transformationpane) {
  will-change: transform;
}

.workflow-canvas :deep(.vue-flow__node) {
  will-change: transform;
  cursor: grab;
}

.workflow-canvas :deep(.vue-flow__node:active) {
  cursor: grabbing;
}

.workflow-canvas :deep(.vue-flow__edge) {
  pointer-events: stroke;
}

/* 控制器样式 */
.workflow-canvas :deep(.vue-flow__controls) {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.workflow-canvas :deep(.vue-flow__controls-button) {
  border: none;
  background: transparent;
  transition: all 0.2s ease;
}

.workflow-canvas :deep(.vue-flow__controls-button:hover) {
  background: #f3f4f6;
  transform: scale(1.05);
}

/* 小地图样式 */
.workflow-canvas :deep(.vue-flow__minimap) {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

/* 🎯 连接点样式 - 让入口和出口更加明显 */
.workflow-canvas :deep(.vue-flow__handle) {
  width: 14px !important;
  height: 14px !important;
  border: 2px solid white !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.2s ease !important;
  z-index: 10 !important;
  cursor: crosshair !important;
  pointer-events: all !important;
}

/* 入口样式 - 绿色表示接收 */
.workflow-canvas :deep(.vue-flow__handle-top) {
  background: #10b981 !important;
  top: -7px !important;
}

/* 出口样式 - 蓝色表示输出 */
.workflow-canvas :deep(.vue-flow__handle-bottom) {
  background: #3b82f6 !important;
  bottom: -7px !important;
}

/* 连接点悬浮效果 */
.workflow-canvas :deep(.vue-flow__handle:hover) {
  transform: translateX(-50%) scale(1.3) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25) !important;
}

/* 入口悬浮时的特殊效果 */
.workflow-canvas :deep(.vue-flow__handle-top:hover) {
  background: #059669 !important;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4) !important;
}

/* 出口悬浮时的特殊效果 */
.workflow-canvas :deep(.vue-flow__handle-bottom:hover) {
  background: #2563eb !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4) !important;
}

/* 连接时的高亮效果 */
.workflow-canvas :deep(.vue-flow__handle.connecting) {
  background: #f59e0b !important;
  transform: translateX(-50%) scale(1.4) !important;
  box-shadow: 0 0 0 4px rgba(245, 158, 11, 0.3) !important;
}

/* 🚫 无效连接目标的样式 */
.workflow-canvas :deep(.vue-flow__handle.invalid) {
  background: #ef4444 !important;
  border-color: #dc2626 !important;
  transform: translateX(-50%) scale(1.2) !important;
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.3) !important;
  cursor: not-allowed !important;
}

/* 🎯 有效连接目标的样式 */
.workflow-canvas :deep(.vue-flow__handle.valid) {
  background: #10b981 !important;
  border-color: #059669 !important;
  transform: translateX(-50%) scale(1.3) !important;
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.3) !important;
}

/* 禁用source handle作为连接目标 */
.workflow-canvas :deep(.vue-flow__handle-source.vue-flow__handle-connectableend) {
  cursor: not-allowed !important;
  opacity: 0.5 !important;
}

.workflow-canvas :deep(.vue-flow__handle-source.vue-flow__handle-connectableend:hover) {
  background: #ef4444 !important;
  border-color: #dc2626 !important;
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.3) !important;
}

/* 🔗 启用连接线拖拽预览 - 显示连接时的临时连线 */
.workflow-canvas :deep(.vue-flow__connection-line),
.workflow-canvas :deep(.vue-flow__connectionline),
.workflow-canvas :deep(.vue-flow__connection),
.workflow-canvas :deep(.vue-flow__temp-edge),
.workflow-canvas :deep(.vue-flow__connecting-edge) {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 启用临时SVG路径显示 */
.workflow-canvas :deep(svg path[class*="connection"]),
.workflow-canvas :deep(svg path[class*="connecting"]),
.workflow-canvas :deep(svg g[class*="connection"]) {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}
</style>
