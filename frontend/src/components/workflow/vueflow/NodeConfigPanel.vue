<template>
  <div
    v-if="visible"
    class="node-config-panel fixed right-0 top-0 h-full w-80 bg-white shadow-2xl z-50 transform transition-all duration-300 ease-out border-l border-gray-100"
    :class="{ 'translate-x-0': visible, 'translate-x-full': !visible }"
  >
    <!-- 面板头部 -->
    <div class="relative bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
      <div class="flex items-center justify-between p-6">
        <div class="flex items-center gap-4">
          <div class="w-12 h-12 bg-white rounded-xl shadow-sm flex items-center justify-center text-2xl border border-blue-100">
            {{ nodeConfig?.icon || '⚙️' }}
          </div>
          <div>
            <h3 class="text-xl font-bold text-gray-800 mb-1">{{ nodeConfig?.name || '节点配置' }}</h3>
            <p class="text-sm text-gray-600 leading-relaxed">{{ nodeConfig?.description || '' }}</p>
          </div>
        </div>
        <button
          @click="closePanel"
          class="w-8 h-8 flex items-center justify-center hover:bg-white/60 rounded-lg transition-all duration-200 text-gray-500 hover:text-gray-700"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      <!-- 装饰性渐变线 -->
      <div class="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-400 to-indigo-400"></div>
    </div>

    <!-- 配置表单 -->
    <div class="flex-1 overflow-y-auto p-6 space-y-8 bg-gray-50/30">
      <div v-for="group in nodeConfig?.configGroups || []" :key="group.title" class="space-y-5">
        <!-- 分组标题 -->
        <div
          class="flex items-center justify-between cursor-pointer group py-2"
          @click="toggleGroup(group.title)"
        >
          <h4 class="text-lg font-semibold text-gray-800 group-hover:text-blue-600 transition-colors">
            {{ group.title }}
          </h4>
          <div class="w-8 h-8 flex items-center justify-center rounded-lg bg-white shadow-sm group-hover:shadow-md transition-all">
            <svg
              class="w-4 h-4 text-gray-500 transition-transform duration-200"
              :class="{ 'rotate-180': !collapsedGroups[group.title] }"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </div>

        <!-- 分组字段 -->
        <div v-show="!collapsedGroups[group.title] && shouldShowGroup(group)" class="space-y-5 ml-2">
          <div v-for="field in group.fields" :key="field.key" v-show="shouldShowField(field)" class="bg-white rounded-xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
            <!-- 字段标签 -->
            <label class="block text-sm font-semibold text-gray-800 mb-3">
              {{ field.label }}
              <span v-if="field.required" class="text-red-500 ml-1">*</span>
            </label>

            <!-- 文本输入框 -->
            <input
              v-if="field.type === 'text'"
              v-model="formData[field.key]"
              :placeholder="field.placeholder"
              :required="field.required"
              class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-gray-50 focus:bg-white transition-all duration-200 placeholder-gray-400"
            />

            <!-- 文本域 -->
            <textarea
              v-else-if="field.type === 'textarea'"
              v-model="formData[field.key]"
              :placeholder="field.placeholder"
              :required="field.required"
              rows="3"
              class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm resize-none bg-gray-50 focus:bg-white transition-all duration-200 placeholder-gray-400"
            ></textarea>

            <!-- 数字输入框 -->
            <input
              v-else-if="field.type === 'number'"
              v-model.number="formData[field.key]"
              type="number"
              :min="field.min"
              :max="field.max"
              :placeholder="field.placeholder"
              :required="field.required"
              class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-gray-50 focus:bg-white transition-all duration-200 placeholder-gray-400"
            />

            <!-- 下拉选择 -->
            <select
              v-else-if="field.type === 'select'"
              v-model="formData[field.key]"
              :required="field.required"
              class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-gray-50 focus:bg-white transition-all duration-200 appearance-none bg-no-repeat bg-right pr-10 select-with-arrow"
            >
              <option v-for="option in field.options" :key="option.value" :value="option.value">
                {{ option.label }}
              </option>
            </select>

            <!-- Git配置选择 -->
            <select
              v-else-if="field.type === 'git-config-select'"
              v-model="formData[field.key]"
              :required="field.required"
              class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-gray-50 focus:bg-white transition-all duration-200 appearance-none bg-no-repeat bg-right pr-10 select-with-arrow"
            >
              <option value="">请选择Git配置</option>
              <option v-for="config in gitConfigs" :key="config.id" :value="config.id">
                {{ config.name }} ({{ config.repository_url }})
              </option>
            </select>

            <!-- 密码输入框 -->
            <input
              v-else-if="field.type === 'password'"
              v-model="formData[field.key]"
              type="password"
              :placeholder="field.placeholder"
              :required="field.required"
              class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-gray-50 focus:bg-white transition-all duration-200 placeholder-gray-400"
            />

            <!-- 复选框 -->
            <label
              v-else-if="field.type === 'checkbox'"
              class="flex items-center space-x-3 cursor-pointer group"
            >
              <div class="relative">
                <input
                  v-model="formData[field.key]"
                  type="checkbox"
                  class="w-5 h-5 text-blue-600 border-2 border-gray-300 rounded-md focus:ring-blue-500 focus:ring-2 transition-all duration-200"
                />
                <svg v-if="formData[field.key]" class="absolute top-0.5 left-0.5 w-3 h-3 text-white pointer-events-none" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <span class="text-sm text-gray-700 group-hover:text-gray-900 transition-colors">{{ field.label }}</span>
            </label>

            <!-- 字段描述 -->
            <p v-if="field.description" class="text-xs text-gray-500 mt-2 leading-relaxed bg-blue-50 px-3 py-2 rounded-lg border-l-2 border-blue-200">
              💡 {{ field.description }}
            </p>

            <!-- 验证错误 -->
            <p v-if="fieldErrors[field.key]" class="text-xs text-red-600 mt-2 bg-red-50 px-3 py-2 rounded-lg border-l-2 border-red-200">
              ⚠️ {{ fieldErrors[field.key] }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 面板底部操作 -->
    <div class="p-6 border-t border-gray-100 bg-white space-y-4">
      <!-- 验证错误汇总 -->
      <div v-if="validationErrors.length > 0" class="bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-xl p-4 shadow-sm">
        <div class="flex items-center gap-2 mb-2">
          <div class="w-5 h-5 bg-red-100 rounded-full flex items-center justify-center">
            <svg class="w-3 h-3 text-red-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <p class="text-sm font-semibold text-red-800">配置错误</p>
        </div>
        <ul class="text-xs text-red-700 space-y-1 ml-7">
          <li v-for="error in validationErrors" :key="error" class="flex items-center gap-1">
            <span class="w-1 h-1 bg-red-400 rounded-full"></span>
            {{ error }}
          </li>
        </ul>
      </div>

      <!-- 操作按钮 -->
      <div class="space-y-3">
        <div class="flex gap-3">
          <button
            @click="saveConfig"
            :disabled="validationErrors.length > 0"
            class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-3 rounded-xl hover:from-blue-700 hover:to-blue-800 disabled:from-gray-300 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200 text-sm font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none flex items-center justify-center gap-2"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            保存配置
          </button>
          <button
            @click="resetConfig"
            class="px-4 py-3 border border-gray-200 text-gray-600 rounded-xl hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
          </button>
        </div>

        <button
          @click="deleteNode"
          class="w-full bg-gradient-to-r from-red-50 to-pink-50 text-red-600 px-4 py-3 rounded-xl hover:from-red-100 hover:to-pink-100 transition-all duration-200 text-sm font-medium border border-red-200 hover:border-red-300 shadow-sm hover:shadow-md flex items-center justify-center gap-2"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
          删除节点
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { getNodeConfig, getNodeDefaultData, validateNodeConfig } from '@/config/nodeConfigs'
import { gitConfigApi } from '@/api/gitConfig'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  selectedNode: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['close', 'save', 'delete'])

// 响应式数据
const formData = ref({})
const collapsedGroups = ref({})
const fieldErrors = ref({})
const gitConfigs = ref([])

// 计算属性
const nodeConfig = computed(() => {
  if (!props.selectedNode) return null
  return getNodeConfig(props.selectedNode.type)
})

const validationErrors = computed(() => {
  if (!props.selectedNode || !nodeConfig.value) return []
  const result = validateNodeConfig(props.selectedNode.type, formData.value)
  return result.errors
})

// 组件挂载时加载Git配置
onMounted(() => {
  loadGitConfigs()
})

// 监听选中节点变化
watch(() => props.selectedNode, (newNode) => {
  if (newNode) {
    loadNodeData(newNode)
    initializeCollapsedGroups()
  }
}, { immediate: true })

// 方法
const loadNodeData = (node) => {
  const defaultData = getNodeDefaultData(node.type)
  formData.value = { ...defaultData, ...node.data }
}

const initializeCollapsedGroups = () => {
  if (!nodeConfig.value) return
  
  collapsedGroups.value = {}
  nodeConfig.value.configGroups.forEach(group => {
    collapsedGroups.value[group.title] = group.collapsed || false
  })
}

const toggleGroup = (groupTitle) => {
  collapsedGroups.value[groupTitle] = !collapsedGroups.value[groupTitle]
}

const saveConfig = () => {
  if (validationErrors.value.length > 0) return
  
  emit('save', {
    nodeId: props.selectedNode.id,
    data: { ...formData.value }
  })
  
  console.log('💾 节点配置已保存:', formData.value)
}

const resetConfig = () => {
  if (props.selectedNode) {
    loadNodeData(props.selectedNode)
  }
  fieldErrors.value = {}
  console.log('🔄 节点配置已重置')
}

const deleteNode = () => {
  if (confirm('确定要删除这个节点吗？')) {
    emit('delete', props.selectedNode.id)
    console.log('🗑️ 节点已删除:', props.selectedNode.id)
  }
}

const closePanel = () => {
  emit('close')
}

// 加载Git配置列表
const loadGitConfigs = async () => {
  try {
    const response = await gitConfigApi.getActiveConfigs()
    gitConfigs.value = response.data || []
  } catch (error) {
    console.error('加载Git配置失败:', error)
    gitConfigs.value = []
  }
}

// 判断是否显示字段
const shouldShowField = (field) => {
  if (!field.dependsOn || !field.showWhen) {
    return true
  }

  const dependValue = formData.value[field.dependsOn]

  if (Array.isArray(field.showWhen)) {
    return field.showWhen.includes(dependValue)
  }

  return dependValue === field.showWhen
}

// 判断是否显示分组
const shouldShowGroup = (group) => {
  if (!group.dependsOn || !group.showWhen) {
    return true
  }

  const dependValue = formData.value[group.dependsOn]

  if (Array.isArray(group.showWhen)) {
    return group.showWhen.includes(dependValue)
  }

  return dependValue === group.showWhen
}
</script>

<style scoped>
.node-config-panel {
  max-height: 100vh;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(10px);
}

/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #e2e8f0, #cbd5e1);
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #cbd5e1, #94a3b8);
}

/* 输入框聚焦动画 */
input:focus, textarea:focus, select:focus {
  transform: translateY(-1px);
}

/* 按钮悬停效果 */
button:hover:not(:disabled) {
  transform: translateY(-1px);
}

button:active:not(:disabled) {
  transform: translateY(0);
}

/* 卡片悬停效果 */
.bg-white:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 下拉选择框箭头 */
.select-with-arrow {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-position: right 12px center;
  background-size: 16px;
  background-repeat: no-repeat;
}
</style>
