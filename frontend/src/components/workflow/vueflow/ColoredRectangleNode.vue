<template>
  <div class="colored-rectangle-node">
    <div class="node-content">
      <span class="node-icon">🎨</span>
      <span class="node-label">{{ data.label || '彩色节点' }}</span>
    </div>
    <Handle type="target" position="top" :style="{ left: '50%', transform: 'translateX(-50%)' }" />
    <Handle type="source" position="bottom" :style="{ left: '50%', transform: 'translateX(-50%)' }" />
  </div>
</template>

<script setup>
import { Handle } from '@vue-flow/core'

defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})
</script>

<style scoped>
.colored-rectangle-node {
  width: 140px !important;
  height: 70px !important;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  border: none !important;
  border-radius: 12px !important;
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3) !important;
  transition: all 0.2s ease !important;
  box-sizing: border-box !important;
}

.colored-rectangle-node:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
}

.node-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.node-icon {
  font-size: 16px;
}

.node-label {
  font-size: 12px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}
</style>
