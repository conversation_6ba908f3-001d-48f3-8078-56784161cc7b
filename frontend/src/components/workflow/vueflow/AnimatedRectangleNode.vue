<template>
  <div class="animated-rectangle-node">
    <div class="pulse-ring"></div>
    <div class="pulse-ring pulse-ring-delay"></div>
    <div class="node-content">
      <span class="node-icon">⚡</span>
      <span class="node-label">{{ data.label || '动画节点' }}</span>
    </div>
    <Handle type="target" position="top" :style="{ left: '50%', transform: 'translateX(-50%)' }" />
    <Handle type="source" position="bottom" :style="{ left: '50%', transform: 'translateX(-50%)' }" />
  </div>
</template>

<script setup>
import { Handle } from '@vue-flow/core'

defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})
</script>

<style scoped>
.animated-rectangle-node {
  width: 130px !important;
  height: 70px !important;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
  border: 2px solid #f59e0b !important;
  border-radius: 10px !important;
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  animation: bounce 2s ease-in-out infinite !important;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3) !important;
  box-sizing: border-box !important;
}

.pulse-ring {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid #f59e0b;
  border-radius: 14px;
  animation: pulse 2s ease-out infinite;
  opacity: 0;
}

.pulse-ring-delay {
  animation-delay: 1s;
}

.node-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  z-index: 1;
}

.node-icon {
  font-size: 16px;
  animation: flash 1.5s ease-in-out infinite;
}

.node-label {
  font-size: 12px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-2px);
  }
}

@keyframes pulse {
  0% {
    opacity: 0;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 0;
    transform: scale(1.3);
  }
}

@keyframes flash {
  0%, 50%, 100% {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0.5;
  }
}
</style>
