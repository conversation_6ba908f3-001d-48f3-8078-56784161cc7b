<template>
  <div class="gradient-rectangle-node">
    <div class="gradient-overlay"></div>
    <div class="node-content">
      <span class="node-icon">🌈</span>
      <span class="node-label">{{ data.label || '渐变节点' }}</span>
    </div>
    <Handle id="target" type="target" position="top" :style="{ left: '50%', transform: 'translateX(-50%)' }" />
    <Handle id="source" type="source" position="bottom" :style="{ left: '50%', transform: 'translateX(-50%)' }" />
  </div>
</template>

<script setup>
import { Handle } from '@vue-flow/core'

defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})
</script>

<style scoped>
.gradient-rectangle-node {
  width: 150px !important;
  height: 80px !important;
  background: linear-gradient(45deg, #8b5cf6, #a855f7, #c084fc, #e879f9, #f0abfc) !important;
  background-size: 300% 300% !important;
  border: none !important;
  border-radius: 16px !important;
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.4) !important;
  animation: gradientShift 3s ease-in-out infinite !important;
  /* 移除 overflow: hidden 以显示连接点 */
  /* overflow: hidden !important; */
  box-sizing: border-box !important;
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  border-radius: 16px;
}

.node-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 8px;
  z-index: 1;
}

.node-icon {
  font-size: 18px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.node-label {
  font-size: 13px;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Handle 连接点样式 */
.gradient-rectangle-node :deep(.vue-flow__handle) {
  width: 12px !important;
  height: 12px !important;
  background: white !important;
  border: 2px solid #8b5cf6 !important;
  border-radius: 50% !important;
  z-index: 10 !important;
  cursor: crosshair !important;
  pointer-events: all !important;
}

.gradient-rectangle-node :deep(.vue-flow__handle-top) {
  top: -6px !important;
}

.gradient-rectangle-node :deep(.vue-flow__handle-bottom) {
  bottom: -6px !important;
}

/* 悬浮效果 - 保持居中的同时缩放 */
.gradient-rectangle-node :deep(.vue-flow__handle:hover) {
  transform: translateX(-50%) scale(1.3) !important;
}

/* 连接时效果 - 保持居中的同时缩放 */
.gradient-rectangle-node :deep(.vue-flow__handle.connecting) {
  transform: translateX(-50%) scale(1.4) !important;
}
</style>
