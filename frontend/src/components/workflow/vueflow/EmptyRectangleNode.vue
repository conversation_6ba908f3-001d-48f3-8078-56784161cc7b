<template>
  <div class="empty-rectangle-node">
    <Handle type="target" position="top" :style="{ left: '50%', transform: 'translateX(-50%)' }" />
    <Handle type="source" position="bottom" :style="{ left: '50%', transform: 'translateX(-50%)' }" />
  </div>
</template>

<script setup>
import { Handle } from '@vue-flow/core'

defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})
</script>

<style scoped>
.empty-rectangle-node {
  width: 120px !important;
  height: 60px !important;
  border: 1px solid #d1d5db !important;
  background: transparent !important;
  border-radius: 4px !important;
  position: relative !important;
  box-sizing: border-box !important;
}
</style>
