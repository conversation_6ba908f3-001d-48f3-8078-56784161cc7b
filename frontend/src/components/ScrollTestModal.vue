<template>
  <!-- 测试滚动锁定的模态框 -->
  <div v-if="visible" class="fixed inset-0 glass-backdrop flex items-center justify-center z-50" @click="onClose">
    <div class="glass-card rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-hidden shadow-large" @click.stop>
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold">滚动测试弹窗</h3>
        <button class="btn btn-ghost btn-sm btn-circle" @click="onClose">
          ✕
        </button>
      </div>

      <!-- 可滚动内容 -->
      <div class="modal-scroll-container overflow-y-auto max-h-64 border rounded p-4 bg-gray-50">
        <div class="space-y-4">
          <div v-for="i in 50" :key="i" class="p-3 bg-white rounded shadow-sm">
            <h4 class="font-medium">测试项目 {{ i }}</h4>
            <p class="text-sm text-gray-600">
              这是第 {{ i }} 个测试项目的描述。当您在这个区域滚动鼠标滚轮时，
              应该只影响弹窗内容的滚动，而不会影响背景页面的滚动。
            </p>
          </div>
        </div>
      </div>

      <div class="flex justify-end gap-2 mt-4">
        <button class="btn btn-ghost" @click="onClose">关闭</button>
        <button class="btn btn-primary">确定</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { toRef } from 'vue'
import { useScrollLock } from '../composables/useScrollLock'

const props = defineProps({
  visible: Boolean
})

const emit = defineEmits(['update:visible'])

// 使用滚动锁定
const visibleRef = toRef(props, 'visible')
useScrollLock(visibleRef)

const onClose = () => {
  emit('update:visible', false)
}
</script>
