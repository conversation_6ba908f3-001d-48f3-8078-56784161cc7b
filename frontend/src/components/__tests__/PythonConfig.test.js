import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import PythonConfig from '../language-configs/PythonConfig.vue'

// Mock fetch
global.fetch = vi.fn()

describe('PythonConfig', () => {
  let wrapper

  beforeEach(() => {
    vi.clearAllMocks()
    fetch.mockClear()
  })

  const createWrapper = (props = {}) => {
    return mount(PythonConfig, {
      props: {
        modelValue: {
          package_manager: 'conda',
          venv_name: 'test_env',
          python_version: '',
          requirements_file: 'environment.yml',
          start_command: 'python app.py'
        },
        deployType: 'local',
        hostId: null,
        ...props
      },
      global: {
        stubs: {
          Icon: true
        }
      }
    })
  }

  describe('主机选择验证', () => {
    it('本地部署时应该允许环境检测', async () => {
      wrapper = createWrapper({
        deployType: 'local',
        hostId: null
      })

      // 模拟成功的环境检测响应
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          exists: true,
          python_version: '3.10.8',
          packages_count: 42,
          host_info: '本地主机'
        })
      })

      // 触发环境检测
      await wrapper.vm.checkEnvironmentStatus()

      expect(fetch).toHaveBeenCalledWith('/api/conda/check-environment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          env_name: 'test_env',
          deploy_type: 'local'
        })
      })
    })

    it('远程部署时需要主机ID才能检测', async () => {
      wrapper = createWrapper({
        deployType: 'remote',
        hostId: null
      })

      // 不应该触发环境检测
      await wrapper.vm.checkEnvironmentStatus()

      expect(fetch).not.toHaveBeenCalled()
      expect(wrapper.vm.environmentStatus).toBe('unknown')
    })

    it('远程部署且有主机ID时应该允许环境检测', async () => {
      wrapper = createWrapper({
        deployType: 'remote',
        hostId: 123
      })

      // 模拟成功的环境检测响应
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          exists: false,
          host_info: '生产服务器 (*************:22)'
        })
      })

      // 触发环境检测
      await wrapper.vm.checkEnvironmentStatus()

      expect(fetch).toHaveBeenCalledWith('/api/conda/check-environment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          env_name: 'test_env',
          deploy_type: 'remote',
          host_id: 123
        })
      })
    })
  })

  describe('Python 版本管理', () => {
    it('环境存在时应该设置Python版本为只读', async () => {
      wrapper = createWrapper({
        deployType: 'local'
      })

      // 模拟环境存在的响应
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          exists: true,
          python_version: '3.10.8',
          packages_count: 42,
          host_info: '本地主机'
        })
      })

      await wrapper.vm.checkEnvironmentStatus()
      await wrapper.vm.$nextTick()

      expect(wrapper.vm.pythonVersionReadonly).toBe(true)
      expect(wrapper.vm.config.python_version).toBe('3.10.8')
      
      // 应该显示只读输入框
      const readonlyInput = wrapper.find('input[readonly]')
      expect(readonlyInput.exists()).toBe(true)
      expect(readonlyInput.element.value).toBe('3.10.8')
    })

    it('环境不存在时Python版本应该可编辑且必填', async () => {
      wrapper = createWrapper({
        deployType: 'local'
      })

      // 模拟环境不存在的响应
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          exists: false,
          host_info: '本地主机'
        })
      })

      await wrapper.vm.checkEnvironmentStatus()
      await wrapper.vm.$nextTick()

      expect(wrapper.vm.pythonVersionReadonly).toBe(false)
      expect(wrapper.vm.isPythonVersionRequired).toBe(true)
      
      // 应该显示下拉选择框
      const selectElement = wrapper.find('select')
      expect(selectElement.exists()).toBe(true)
    })
  })

  describe('状态提示', () => {
    it('未选择主机时应该显示相应提示', () => {
      wrapper = createWrapper({
        deployType: 'remote',
        hostId: null
      })

      expect(wrapper.vm.environmentStatusText).toContain('请先选择部署主机')
    })

    it('环境存在时应该显示详细信息', async () => {
      wrapper = createWrapper({
        deployType: 'local'
      })

      // 设置环境信息
      wrapper.vm.environmentStatus = 'exists'
      wrapper.vm.environmentInfo = {
        pythonVersion: '3.10.8',
        packagesCount: 42,
        hostInfo: '本地主机'
      }

      await wrapper.vm.$nextTick()

      const statusText = wrapper.vm.environmentStatusText
      expect(statusText).toContain('test_env')
      expect(statusText).toContain('已存在')
      expect(statusText).toContain('Python 3.10.8')
      expect(statusText).toContain('42 个包')
    })

    it('环境不存在时应该显示创建提示', async () => {
      wrapper = createWrapper({
        deployType: 'local'
      })

      wrapper.vm.environmentStatus = 'not-exists'
      await wrapper.vm.$nextTick()

      const statusText = wrapper.vm.environmentStatusText
      expect(statusText).toContain('test_env')
      expect(statusText).toContain('不存在')
      expect(statusText).toContain('将创建新环境')
    })
  })

  describe('表单验证', () => {
    it('创建新环境时缺少Python版本应该返回错误', () => {
      wrapper = createWrapper({
        deployType: 'local',
        modelValue: {
          package_manager: 'conda',
          venv_name: 'new_env',
          python_version: '', // 空版本
          requirements_file: 'environment.yml',
          start_command: 'python app.py'
        }
      })

      // 设置环境不存在状态
      wrapper.vm.environmentStatus = 'not-exists'

      const errors = wrapper.vm.validateForm()
      expect(errors).toContain('创建新的 conda 环境时必须指定 Python 版本')
    })

    it('使用现有环境时不需要Python版本', () => {
      wrapper = createWrapper({
        deployType: 'local',
        modelValue: {
          package_manager: 'conda',
          venv_name: 'existing_env',
          python_version: '', // 空版本
          requirements_file: 'environment.yml',
          start_command: 'python app.py'
        }
      })

      // 设置环境存在状态
      wrapper.vm.environmentStatus = 'exists'

      const errors = wrapper.vm.validateForm()
      expect(errors).not.toContain('创建新的 conda 环境时必须指定 Python 版本')
    })
  })

  describe('响应式更新', () => {
    it('主机变化时应该重新检测环境', async () => {
      wrapper = createWrapper({
        deployType: 'remote',
        hostId: 123
      })

      // 模拟第一次检测
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          exists: true,
          python_version: '3.10.8'
        })
      })

      await wrapper.vm.checkEnvironmentStatus()
      expect(fetch).toHaveBeenCalledTimes(1)

      // 清除 mock
      fetch.mockClear()

      // 模拟第二次检测（不同主机）
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          exists: false
        })
      })

      // 更改主机ID
      await wrapper.setProps({ hostId: 456 })

      // 应该触发新的检测
      expect(fetch).toHaveBeenCalledWith('/api/conda/check-environment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          env_name: 'test_env',
          deploy_type: 'remote',
          host_id: 456
        })
      })
    })
  })
})
