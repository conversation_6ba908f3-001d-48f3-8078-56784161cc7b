<template>
  <!-- <PERSON><PERSON> Modal -->
  <div v-if="visible" class="modal modal-open">
    <div class="modal-box modal-scroll-container w-11/12 max-w-4xl max-h-[90vh] overflow-y-auto">
      <!-- 标题 -->
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-xl font-bold text-gray-800">
          {{ isEdit ? '编辑项目' : '新建项目' }}
        </h3>
        <button
          type="button"
          class="btn btn-ghost btn-sm btn-circle"
          @click="onCancel"
        >
          ✕
        </button>
      </div>

      <form @submit.prevent="handleSave" class="space-y-6">
        <!-- 基本信息卡片 -->
        <div class="card bg-base-100 border border-base-300">
          <div class="card-body p-4">
            <h4 class="card-title text-base mb-4 text-gray-700">基本信息</h4>

            <div class="form-control mb-4">
              <label class="label">
                <span class="label-text font-medium">项目名称 <span class="text-error">*</span></span>
              </label>
              <input
                v-model="form.name"
                type="text"
                placeholder="请输入项目名称"
                class="input input-bordered input-romantic w-full"
                required
              />
            </div>

            <div class="form-control mb-4">
              <label class="label">
                <span class="label-text font-medium">项目描述</span>
              </label>
              <textarea
                v-model="form.description"
                placeholder="请输入项目描述"
                class="textarea textarea-bordered textarea-romantic h-20 w-full"
              ></textarea>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">环境 <span class="text-error">*</span></span>
                </label>
                <select v-model="form.environment" class="select select-bordered select-romantic w-full" @change="handleEnvironmentChange">
                  <option value="">请选择环境</option>
                  <option value="test">测试环境</option>
                  <option value="prod">生产环境</option>
                </select>
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">项目类型 <span class="text-error">*</span></span>
                </label>
                <select
                  v-model="form.project_type"
                  class="select select-bordered select-romantic w-full"
                  @change="handleProjectTypeChange"
                >
                  <option value="">请选择项目类型</option>
                  <option value="frontend">前端项目</option>
                  <option value="backend">后端项目</option>
                </select>
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">开发语言 <span class="text-error">*</span></span>
                </label>
                <select
                  v-model="form.build_config.language"
                  class="select select-bordered select-romantic w-full"
                  @change="handleLanguageChange"
                >
                  <option value="">请选择开发语言</option>
                  <option value="go">Go</option>
                  <option value="vue">Vue</option>
                  <option value="java">Java</option>
                  <option value="python">Python</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- Git 配置卡片 -->
        <div class="card bg-base-100 border border-base-300">
          <div class="card-body p-4">
            <h4 class="card-title text-base mb-4 text-gray-700">Git 配置</h4>

            <div class="form-control mb-4">
              <label class="label">
                <span class="label-text font-medium">Git 地址 <span class="text-error">*</span></span>
              </label>
              <input
                v-model="form.git_url"
                type="text"
                placeholder="请输入 Git 地址"
                class="input input-bordered input-romantic w-full"
                @blur="fetchGitVersions"
              />
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">Git 用户名</span>
                </label>
                <input
                  v-model="form.git_username"
                  type="text"
                  placeholder="请输入 Git 用户名"
                  class="input input-bordered input-romantic w-full"
                  @blur="fetchGitVersions"
                />
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">Git 密码</span>
                </label>
                <input
                  v-model="form.git_password"
                  type="password"
                  placeholder="请输入 Git 密码"
                  class="input input-bordered input-romantic w-full"
                  @blur="fetchGitVersions"
                />
              </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">版本类型 <span class="text-error">*</span></span>
                </label>
                <select
                  v-model="versionType"
                  class="select select-bordered select-romantic w-full"
                  @change="handleVersionTypeChange"
                >
                  <option value="">请选择版本类型</option>
                  <option value="branch">分支</option>
                  <option value="tag">标签</option>
                </select>
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">{{ versionType === 'branch' ? '选择分支' : '选择标签' }} <span class="text-error">*</span></span>
                </label>
                <div class="relative">
                  <select
                    v-model="selectedVersion"
                    class="select select-bordered select-romantic w-full"
                    :disabled="loadingVersions"
                  >
                    <option value="">
                      {{ loadingVersions ? '加载中...' : (versionType === 'branch' ? '请选择分支' : '请选择标签') }}
                    </option>
                    <option v-for="item in versionList" :key="item" :value="item">{{ item }}</option>
                  </select>
                  <!-- 加载指示器 -->
                  <div v-if="loadingVersions" class="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <div class="loading loading-spinner loading-sm"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 构建配置卡片 -->
        <div class="card bg-base-100 border border-base-300">
          <div class="card-body p-4">
            <h4 class="card-title text-base mb-4 text-gray-700">构建配置</h4>

            <!-- 后端项目端口配置 -->
            <div v-if="form.project_type === 'backend'" class="form-control mb-4">
              <label class="label">
                <span class="label-text font-medium">服务端口 <span class="text-error">*</span></span>
              </label>
              <input
                v-if="form.build_config.config"
                v-model="form.build_config.config.port"
                type="text"
                placeholder="请输入服务端口，例如: 8080"
                class="input input-bordered input-romantic w-full"
              />
            </div>

            <!-- 语言特定配置 -->
            <LanguageConfigContainer
              :language="form.build_config.language"
              v-model="form.build_config.config"
              :project-name="form.name"
              :deploy-type="form.deploy_type"
              :host-id="form.host_id"
            />
          </div>
        </div>

        <!-- 部署配置卡片 -->
        <div class="card bg-base-100 border border-base-300">
          <div class="card-body p-4">
            <h4 class="card-title text-base mb-4 text-gray-700">部署配置</h4>

            <div class="form-control mb-4">
              <label class="label">
                <span class="label-text font-medium">部署类型 <span class="text-error">*</span></span>
              </label>
              <select v-model="form.deploy_type" class="select select-bordered select-romantic w-full">
                <option value="">请选择部署类型</option>
                <option value="local">本地部署</option>
                <option value="remote">远程部署</option>
              </select>
            </div>

            <!-- 部署路径配置 - 所有部署类型都需要 -->
            <div class="form-control mb-4">
              <label class="label">
                <span class="label-text font-medium">
                  部署路径 <span class="text-error">*</span>
                </span>
              </label>
              <input
                v-model="form.deploy_path"
                type="text"
                :placeholder="form.deploy_type === 'local' ? '例如: /opt/myapp' : '例如: /opt/myapp'"
                class="input input-bordered input-romantic w-full"
              />
              <div class="label">
                <span class="label-text-alt text-gray-500">
                  <Icon name="info" :size="12" class="inline mr-1" />
                  {{ form.deploy_type === 'local' ? '本地部署目录' : '远程服务器上的项目目录，将在此目录执行 git clone/pull' }}
                </span>
              </div>
            </div>

            <!-- 远程部署配置 -->
            <div v-if="form.deploy_type === 'remote'">
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">部署主机 <span class="text-error">*</span></span>
                </label>
                <select v-model="form.host_id" class="select select-bordered select-romantic w-full">
                  <option value="">请选择部署主机</option>
                  <option v-for="host in availableHosts" :key="host.id" :value="host.id">
                    {{ host.name }} ({{ host.ip }}:{{ host.port }})
                  </option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- 历史构建配置卡片 - Python项目不显示，因为不产生构建产物 -->
        <div v-if="form.build_config.language !== 'python'" class="card bg-base-100 border border-base-300">
          <div class="card-body p-4">
            <h4 class="card-title text-base mb-4 text-gray-700">历史构建配置</h4>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">最多保留天数</span>
                </label>
                <input
                  v-model.number="form.max_artifact_days"
                  type="number"
                  min="0"
                  max="365"
                  placeholder="天数，0为不限制"
                  class="input input-bordered input-romantic w-full"
                />
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">最多保留产物数</span>
                </label>
                <input
                  v-model.number="form.max_artifact_count"
                  type="number"
                  min="0"
                  max="100"
                  placeholder="个数，0为不限制"
                  class="input input-bordered input-romantic w-full"
                />
              </div>
            </div>
          </div>
        </div>



        <!-- 操作按钮 -->
        <div class="flex justify-end gap-3 pt-4 border-t border-base-300">
          <button
            type="button"
            class="btn btn-ghost"
            @click="onCancel"
          >
            取消
          </button>
          <button
            type="submit"
            class="btn btn-romantic"
            :disabled="loading"
          >
            <span v-if="loading" class="loading loading-spinner loading-sm"></span>
            {{ loading ? '保存中...' : '保存' }}
          </button>
        </div>
      </form>
    </div>

    <!-- 点击背景关闭 -->
    <div class="modal-backdrop" @click="onCancel"></div>
  </div>
</template>

<script setup>
import { ref, watch, reactive, onMounted, toRef } from 'vue'
import Icon from './Icon.vue'
import LanguageConfigContainer from './LanguageConfigContainer.vue'
import { getGitBranches, getGitTags } from '../api/project'
import { getHostsByEnvironment } from '../api/host'
import { useScrollLock } from '../composables/useScrollLock'
import { toast } from '../composables/useToast.js'

// 🌸 使用全局Toast通知系统
const showMessage = (message, type = 'info') => {
  switch(type) {
    case 'success':
      toast.success(message)
      break
    case 'error':
      toast.error(message)
      break
    case 'warning':
      toast.warning(message)
      break
    case 'info':
    default:
      toast.info(message)
      break
  }
}

const props = defineProps({
  visible: Boolean,
  project: Object,
  isEdit: Boolean
})
const emit = defineEmits(['update:visible', 'save', 'cancel'])

// 使用滚动锁定
const visibleRef = toRef(props, 'visible')
useScrollLock(visibleRef)

// 初始表单数据
const initialForm = {
  name: '',
  description: '',
  git_url: '',
  git_username: '',
  git_password: '',
  git_version: '',
  deploy_type: 'local',
  environment: 'test',
  project_type: 'frontend',
  build_config: {
    language: 'go',
    config: {
      server_os: 'linux',
      server_arch: 'amd64',
      port: '8080',
      node_version: '16',
      package_manager: 'npm',
      build_tool: 'maven',
      jdk_version: '11'
    }
  },
  host_id: null,  // 新增主机ID字段
  server_ip: '',
  server_port: '22',
  server_username: '',
  server_password: '',
  deploy_path: '',
  post_deploy_shell: '',
  max_artifact_days: 0,
  max_artifact_count: 0
}

const form = ref({...initialForm})
const versionType = ref('branch')
const versionList = ref([])
const selectedVersion = ref('')
const loadingVersions = ref(false)

// 确保build_config.config存在
const ensureBuildConfig = () => {
  if (!form.value.build_config) {
    form.value.build_config = {
      language: 'go',
      config: {}
    }
  }
  if (!form.value.build_config.config) {
    form.value.build_config.config = {
      port: '8080'
    }
  }
}

// 主机管理相关状态
const availableHosts = ref([])
const loadingHosts = ref(false)
const showManualConfig = ref([])  // 控制手动配置折叠面板
const loading = ref(false)  // 保存状态

// 组件挂载时加载主机列表
onMounted(() => {
  loadHostsByEnvironment()
  ensureBuildConfig()
})

// 当项目数据变化时，更新表单
watch(() => props.project, async (val) => {
  if (val) {
    form.value = { ...initialForm, ...val }
    ensureBuildConfig()

    // 解析 git_version，设置版本类型和选中版本
    if (val.git_version) {
      const parts = val.git_version.split(':')
      if (parts.length === 2) {
        versionType.value = parts[0]
        selectedVersion.value = parts[1]
      }
    }

    // 如果项目有传统配置但没有主机ID，显示手动配置
    if (!val.host_id && val.server_ip) {
      showManualConfig.value = ['manual']
    }

    // 🎯 自动加载 Git 分支和标签（编辑模式下）
    if (val.git_url && props.isEdit) {
      await fetchGitVersions()
    }
  } else {
    form.value = { ...initialForm }
    versionType.value = 'branch'
    selectedVersion.value = ''
    showManualConfig.value = []
    versionList.value = []
  }
}, { immediate: true })

// 监听环境变化，重新加载主机列表
watch(() => form.value.environment, (newEnv) => {
  if (newEnv) {
    loadHostsByEnvironment()
  }
})

// 处理环境变化
const handleEnvironmentChange = () => {
  loadHostsByEnvironment()
}

// 处理项目类型变化
const handleProjectTypeChange = () => {
  // 根据项目类型设置默认语言
  if (form.value.project_type === 'frontend') {
    form.value.build_config.language = 'vue'
  } else {
    // 后端项目可以是 go、java、python 等
    form.value.build_config.language = 'go'
  }
}



// 处理语言变化
const handleLanguageChange = () => {
  ensureBuildConfig()
  // 语言特定的配置现在由 LanguageConfigContainer 组件处理
  // 这里只需要确保基础配置存在
}

// 处理版本类型变化
const handleVersionTypeChange = () => {
  selectedVersion.value = ''
  fetchGitVersions()
}

// 获取 Git 版本列表
const fetchGitVersions = async () => {
  if (!form.value.git_url) return

  loadingVersions.value = true
  try {
    let res
    if (versionType.value === 'branch') {
      res = await getGitBranches(form.value.git_url, form.value.git_username, form.value.git_password)
    } else {
      res = await getGitTags(form.value.git_url, form.value.git_username, form.value.git_password)
    }

    versionList.value = res.data || []

    // 🎯 智能选择版本：
    // 1. 如果已有选中版本且在列表中，保持选中
    // 2. 否则选择第一个版本
    if (versionList.value.length > 0) {
      const currentSelection = selectedVersion.value
      if (currentSelection && versionList.value.includes(currentSelection)) {
        // 保持当前选择
        selectedVersion.value = currentSelection
      } else {
        // 选择第一个版本
        selectedVersion.value = versionList.value[0]
      }
    } else {
      selectedVersion.value = ''
    }
  } catch (error) {
    console.error('获取版本列表失败:', error)
    showMessage(`获取${versionType.value === 'branch' ? '分支' : '标签'}列表失败: ${error.message}`, 'error')
    versionList.value = []
    selectedVersion.value = ''
  } finally {
    loadingVersions.value = false
  }
}

// 加载指定环境的主机列表
const loadHostsByEnvironment = async () => {
  if (!form.value.environment) return

  try {
    loadingHosts.value = true
    const result = await getHostsByEnvironment(form.value.environment)
    availableHosts.value = result.data || []
  } catch (error) {
    console.error('加载主机列表失败:', error)
    availableHosts.value = []
  } finally {
    loadingHosts.value = false
  }
}

// 处理主机选择变化
const handleHostChange = (hostId) => {
  if (hostId) {
    // 选择了主机，清空手动配置
    form.value.server_ip = ''
    form.value.server_port = '22'
    form.value.server_username = ''
    form.value.server_password = ''
    showManualConfig.value = []
  }
}

// 刷新主机列表
const refreshHosts = () => {
  loadHostsByEnvironment()
}

// 显示主机管理页面
const showHostManagement = () => {
  // 在新窗口打开主机管理页面
  window.open('/hosts', '_blank')
}

const onCancel = () => {
  emit('update:visible', false)
  emit('cancel')
}

const handleSave = () => {
  // 设置 git_version
  form.value.git_version = `${versionType.value}:${selectedVersion.value}`

  // Python项目不需要历史构建配置，设置为0
  if (form.value.build_config.language === 'python') {
    form.value.max_artifact_days = 0
    form.value.max_artifact_count = 0
  }

  // 表单验证
  if (!form.value.name) {
    showMessage('请输入项目名称', 'error')
    return
  }

  // 验证部署路径
  if (!form.value.deploy_path) {
    showMessage('请输入部署路径', 'error')
    return
  }

  // 验证部署路径格式（应该是绝对路径）
  if (!form.value.deploy_path.startsWith('/')) {
    showMessage('部署路径必须是绝对路径（以 / 开头）', 'error')
    return
  }

  // 如果选择了远程部署但没有配置主机信息，提示用户
  if (form.value.deploy_type === 'remote') {
    if (!form.value.host_id && !form.value.server_ip) {
      showMessage('远程部署需要选择主机或手动配置服务器信息', 'error')
      return
    }
  }

  loading.value = true

  // 模拟保存延迟
  setTimeout(() => {
    emit('save', { ...form.value })
    emit('update:visible', false)
    loading.value = false
  }, 500)
}
</script>

<style scoped>
/* 🎨 浪漫风格表单样式 */

/* 表单控件间距优化 */
.form-control {
  margin-bottom: 1.25rem;
}

.form-control .label {
  margin-bottom: 0.5rem;
}

.form-control .label-text {
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
}

/* 输入框样式增强 */
.input-romantic {
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background-color: transparent;
  backdrop-filter: blur(2px);
  height: 3rem;
  padding: 0.75rem 1rem;
}

.input-romantic:focus {
  border-color: rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
  background-color: rgba(255, 255, 255, 0.1);
}

.input-romantic:hover {
  border-color: rgba(0, 0, 0, 0.15);
  background-color: rgba(255, 255, 255, 0.05);
}

/* 文本域样式 */
.textarea-romantic {
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background-color: transparent;
  backdrop-filter: blur(2px);
  resize: vertical;
  min-height: 5rem;
  padding: 0.75rem 1rem;
}

.textarea-romantic:focus {
  border-color: rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
  background-color: rgba(255, 255, 255, 0.1);
}

.textarea-romantic:hover {
  border-color: rgba(0, 0, 0, 0.15);
  background-color: rgba(255, 255, 255, 0.05);
}

/* 选择框样式 */
.select-romantic {
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background-color: transparent;
  backdrop-filter: blur(2px);
  height: 3rem;
  padding: 0.75rem 1rem;
}

.select-romantic:focus {
  border-color: rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
  background-color: rgba(255, 255, 255, 0.1);
}

.select-romantic:hover {
  border-color: rgba(0, 0, 0, 0.15);
  background-color: rgba(255, 255, 255, 0.05);
}

/* 单选按钮样式 */
.radio-romantic {
  accent-color: #f472b6;
}

/* 浪漫按钮样式 */
.btn-romantic {
  background: linear-gradient(to right, #f472b6, #a855f7);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.btn-romantic:hover {
  background: linear-gradient(to right, #ec4899, #9333ea);
  transform: scale(1.02);
  box-shadow: 0 4px 6px -1px rgba(236, 72, 153, 0.1), 0 2px 4px -1px rgba(236, 72, 153, 0.06);
}

.btn-romantic:disabled {
  background: linear-gradient(to right, #d1d5db, #9ca3af);
  transform: none;
  box-shadow: none;
}

/* 模态框背景优化 */
.modal-box {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(253, 242, 248, 0.95) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(244, 114, 182, 0.1);
}


</style>