<template>
  <!-- 🐍 Python 项目配置组件 -->
  <div class="space-y-4">




    <div class="form-control mb-4">
      <label class="label">
        <span class="label-text font-medium">Conda环境名 <span class="text-error">*</span></span>
      </label>
      <div class="relative">
        <input
          :value="config.conda_env_name"
          @input="handleCondaEnvNameChange"
          @blur="handleCondaEnvNameBlur"
          type="text"
          placeholder="例如: ai-voice-bot-server"
          class="input input-bordered input-romantic w-full pr-10"
          :class="{
            'input-success': envStatus.exists === true,
            'input-error': envStatus.exists === false,
            'input-warning': envStatus.checking
          }"
          required
        />
        <!-- 状态图标 -->
        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
          <span v-if="envStatus.checking" class="loading loading-spinner loading-sm text-warning"></span>
          <Icon v-else-if="envStatus.exists === true" name="check" :size="16" class="text-success" />
          <Icon v-else-if="envStatus.exists === false" name="x" :size="16" class="text-error" />
        </div>
      </div>
      <!-- 状态提示 -->
      <div class="label">
        <span v-if="envStatus.checking" class="label-text-alt text-warning">
          <Icon name="refresh" :size="12" class="inline mr-1" />
          检测环境中...
        </span>
        <span v-else-if="envStatus.exists === true" class="label-text-alt text-success">
          <Icon name="check" :size="12" class="inline mr-1" />
          环境已存在
        </span>
        <span v-else-if="envStatus.exists === false" class="label-text-alt text-error">
          <Icon name="alert-triangle" :size="12" class="inline mr-1" />
          环境不存在，部署时将自动创建
        </span>
        <span v-else-if="config.conda_env_name" class="label-text-alt text-gray-500">
          请等待环境检测...
        </span>
      </div>
    </div>

    <div class="form-control mb-4">
      <label class="label">
        <span class="label-text font-medium">Python版本</span>
      </label>
      <div class="border border-gray-300 rounded-lg w-full bg-gray-50 text-gray-700 cursor-not-allowed flex items-center px-3 py-2 min-h-[3rem]">
        <span v-if="envStatus.checking" class="text-gray-500">
          <Icon name="refresh" :size="14" class="inline mr-2 animate-spin" />
          检测中...
        </span>
        <span v-else-if="envStatus.exists === true && envStatus.pythonVersion" class="text-green-700">
          Python {{ envStatus.pythonVersion }}
        </span>
        <span v-else-if="envStatus.exists === true && !envStatus.pythonVersion" class="text-blue-700">
          <Icon name="info" :size="14" class="inline mr-2 text-blue-500" />
          环境已存在（版本检测中...）
        </span>
        <span v-else-if="envStatus.exists === false" class="text-orange-700">
          <Icon name="alert-triangle" :size="14" class="inline mr-2 text-orange-500" />
          环境不存在，将使用系统默认版本
        </span>
        <span v-else class="text-gray-500">
          自动检测
        </span>
      </div>
      <div class="label">
        <span v-if="envStatus.exists === true" class="label-text-alt text-info">
          <Icon name="info" :size="12" class="inline mr-1" />
          将使用环境中已安装的Python版本
        </span>
        <span v-else-if="envStatus.exists === false" class="label-text-alt text-warning">
          <Icon name="alert-triangle" :size="12" class="inline mr-1" />
          部署时将创建新环境，使用系统默认Python版本
        </span>
      </div>
    </div>



    <div class="form-control mb-4">
      <label class="label">
        <span class="label-text font-medium">启动脚本</span>
      </label>
      <textarea
        v-model="config.start_command"
        @input="handleStartCommandChange"
        placeholder="例如: python app.py 或 gunicorn app:app --bind 0.0.0.0:8000"
        class="textarea textarea-bordered textarea-romantic w-full"
        :style="{ height: textareaHeight }"
      ></textarea>
    </div>





    <!-- Python 项目部署说明 - 放在最后 -->
    <div class="card bg-blue-50 border border-blue-200">
      <div class="card-body p-4">
        <h5 class="card-title text-sm mb-3 text-blue-700">
          <Icon name="info" :size="16" class="inline mr-2" />
          Python 项目部署说明
        </h5>

        <div class="text-sm text-blue-600 space-y-2">
          <div class="flex items-start gap-2">
            <Icon name="git" :size="14" class="mt-0.5 flex-shrink-0" />
            <span>在目标机器的指定目录执行 <code class="bg-blue-100 px-1 rounded">git clone</code> 或 <code class="bg-blue-100 px-1 rounded">git pull</code></span>
          </div>
          <div class="flex items-start gap-2">
            <Icon name="refresh" :size="14" class="mt-0.5 flex-shrink-0" />
            <span>自动丢弃本地修改，强制拉取远程更新</span>
          </div>
          <div class="flex items-start gap-2">
            <Icon name="settings" :size="14" class="mt-0.5 flex-shrink-0" />
            <span>使用指定的Conda环境名称和Python版本</span>
          </div>
          <div class="flex items-start gap-2">
            <Icon name="tools" :size="14" class="mt-0.5 flex-shrink-0" />
            <span>使用 <code class="bg-blue-100 px-1 rounded">pip install -r requirements.txt</code> 安装项目依赖</span>
          </div>
          <div class="flex items-start gap-2">
            <Icon name="play" :size="14" class="mt-0.5 flex-shrink-0" />
            <span>执行启动脚本完成部署</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, watch, ref, onMounted } from 'vue'
import Icon from '../Icon.vue'
import { checkCondaEnvironment } from '../../api/python.js'



const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  projectName: {
    type: String,
    default: ''
  },
  deployType: {
    type: String,
    default: 'local'
  },
  hostId: {
    type: Number,
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

// 环境检测状态
const envStatus = ref({
  checking: false,
  exists: null,
  pythonVersion: null,
  error: null
})

// 计算 textarea 高度
const textareaHeight = computed(() => {
  const content = config.value.start_command || ''

  // 计算行数
  const lines = content.split('\n')
  const lineCount = Math.max(lines.length, 1)

  // 根据实际的 CSS 样式计算高度
  // textarea 的 line-height 通常是 1.5，font-size 约 14px (0.875rem)
  // 所以每行实际高度约为 1.3125rem (0.875 * 1.5)
  const lineHeight = 1.3125 // rem (实际行高)
  const padding = 1.5 // rem (上下内边距 0.75rem * 2)
  const minHeight = 5 // rem (最小高度，约3行)
  const maxHeight = 15 // rem (最大高度，约10行)

  const calculatedHeight = lineCount * lineHeight + padding
  const finalHeight = Math.max(minHeight, Math.min(maxHeight, calculatedHeight))

  return `${finalHeight}rem`
})






// 默认配置（只支持conda，手动指定环境信息）
const defaultConfig = {
  package_manager: 'conda',
  conda_env_name: '',
  start_command: 'python app.py'
}

const config = computed({
  get() {
    // 只保留需要的字段，过滤掉废弃的字段
    const userConfig = props.modelValue || {}
    const result = {
      package_manager: 'conda', // 固定为conda
      conda_env_name: userConfig.conda_env_name || defaultConfig.conda_env_name || '',
      start_command: userConfig.start_command || defaultConfig.start_command || 'python app.py'
    }
    return result
  },
  set(value) {
    // 保留原有配置的所有字段，只更新Python相关字段
    const updatedConfig = {
      ...props.modelValue, // 保留所有原有字段（如服务端口等）
      package_manager: 'conda',
      conda_env_name: value.conda_env_name,
      start_command: value.start_command
    }
    emit('update:modelValue', updatedConfig)
  }
})







// 配置已在computed中处理，不需要额外的watch





// 检测Conda环境
const checkEnvironment = async (envName) => {
  if (!envName || !envName.trim()) {
    envStatus.value = {
      checking: false,
      exists: null,
      pythonVersion: null,
      error: null
    }
    return
  }

  envStatus.value.checking = true
  envStatus.value.error = null

  try {
    const response = await checkCondaEnvironment({
      conda_env_name: envName.trim(),
      host_id: props.hostId,
      deploy_type: props.deployType
    })

    if (response.success) {
      envStatus.value = {
        checking: false,
        exists: response.exists,
        pythonVersion: response.python_version || null,
        error: null
      }
    } else {
      envStatus.value = {
        checking: false,
        exists: false,
        pythonVersion: null,
        error: response.message || '检测失败'
      }
    }
  } catch (error) {
    console.error('环境检测失败:', error)
    envStatus.value = {
      checking: false,
      exists: null,
      pythonVersion: null,
      error: '网络错误，无法检测环境'
    }
  }
}

// 处理Conda环境名变化（仅更新值，不触发检测）
const handleCondaEnvNameChange = (event) => {
  if (!event || !event.target) return

  const envName = event.target.value || ''
  // 保留原有配置的所有字段，只更新环境名
  const updatedConfig = {
    ...props.modelValue, // 保留所有原有字段
    package_manager: 'conda',
    conda_env_name: envName,
    start_command: config.value.start_command || 'python app.py'
  }
  emit('update:modelValue', updatedConfig)

  // 清除之前的检测状态
  envStatus.value = {
    checking: false,
    exists: null,
    pythonVersion: null,
    error: null
  }
}

// 处理环境名失焦（触发检测）
const handleCondaEnvNameBlur = () => {
  try {
    const envName = config.value?.conda_env_name
    if (envName && envName.trim()) {
      checkEnvironment(envName)
    }
  } catch (error) {
    console.warn('PythonConfig blur error:', error)
  }
}





// 处理启动命令变化
const handleStartCommandChange = (event) => {
  if (!event || !event.target) return

  // 保留原有配置的所有字段，只更新启动命令
  const updatedConfig = {
    ...props.modelValue, // 保留所有原有字段
    package_manager: 'conda',
    conda_env_name: config.value.conda_env_name || '',
    start_command: event.target.value || ''
  }
  emit('update:modelValue', updatedConfig)
}

// 表单验证
const validateForm = () => {
  const errors = []

  // Conda环境名验证
  if (!config.value.conda_env_name) {
    errors.push('请指定Conda环境名')
  }

  // 启动脚本验证
  if (!config.value.start_command) {
    errors.push('请输入启动脚本')
  }

  return errors
}

// 监听环境名变化，但不自动检测（改为失焦时检测）
watch(() => config.value?.conda_env_name, (newEnvName) => {
  // 只清除状态，不自动检测
  if (!newEnvName || !newEnvName.trim()) {
    envStatus.value = {
      checking: false,
      exists: null,
      pythonVersion: null,
      error: null
    }
  }
}, { immediate: true })

// 组件挂载时，如果已有环境名，立即检测
onMounted(() => {
  try {
    const envName = config.value?.conda_env_name
    if (envName && envName.trim()) {
      // 立即检测
      checkEnvironment(envName)
    }
  } catch (error) {
    console.warn('PythonConfig onMounted error:', error)
  }
})

// 暴露验证方法给父组件
defineExpose({
  validateForm
})
</script>

<style scoped>
/* Python 配置特定样式 */
.card-title {
  font-size: 0.875rem;
  font-weight: 600;
}

/* 表单控件间距优化 - 与ProjectDialog保持一致 */
.form-control {
  margin-bottom: 1.25rem;
}

.form-control .label {
  margin-bottom: 0.5rem;
}

.form-control .label-text {
  font-weight: 500;
  color: #374151;
}

/* 选择框样式 */
.select-romantic {
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background-color: transparent;
  backdrop-filter: blur(2px);
  height: 3rem;
  padding: 0.75rem 1rem;
}

.select-romantic:focus {
  border-color: rgba(0, 0, 0, 0.2);
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
  background-color: rgba(255, 255, 255, 0.1);
}

.select-romantic:hover {
  border-color: rgba(0, 0, 0, 0.15);
  background-color: rgba(255, 255, 255, 0.05);
}

/* 输入框样式 */
.input-romantic {
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background-color: transparent;
  backdrop-filter: blur(2px);
  height: 3rem;
  padding: 0.75rem 1rem;
}

.input-romantic:focus {
  border-color: rgba(0, 0, 0, 0.2);
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
  background-color: rgba(255, 255, 255, 0.1);
}

.input-romantic:hover {
  border-color: rgba(0, 0, 0, 0.15);
  background-color: rgba(255, 255, 255, 0.05);
}

/* 文本域样式 */
.textarea-romantic {
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background-color: transparent;
  backdrop-filter: blur(2px);
  padding: 0.75rem 1rem;
  resize: vertical;
}

.textarea-romantic:focus {
  border-color: rgba(0, 0, 0, 0.2);
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
  background-color: rgba(255, 255, 255, 0.1);
}

.textarea-romantic:hover {
  border-color: rgba(0, 0, 0, 0.15);
  background-color: rgba(255, 255, 255, 0.05);
}

/* 复选框样式 */
.checkbox-romantic {
  border: 2px solid rgba(0, 0, 0, 0.3);
  background-color: transparent;
  border-radius: 0.25rem;
  width: 1.25rem;
  height: 1.25rem;
  transition: all 0.3s ease;
}

.checkbox-romantic:checked {
  background-color: rgba(0, 0, 0, 0.8);
  border-color: rgba(0, 0, 0, 0.8);
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
}

.checkbox-romantic:hover {
  border-color: rgba(0, 0, 0, 0.5);
  background-color: rgba(0, 0, 0, 0.05);
}

.checkbox-romantic:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}
</style>
