<template>
  <!-- 🐹 Go 项目配置组件 -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
    <div class="form-control mb-4">
      <label class="label">
        <span class="label-text font-medium">服务器类型 <span class="text-error">*</span></span>
      </label>
      <select v-model="config.server_os" class="select select-bordered select-romantic w-full">
        <option value="">请选择服务器类型</option>
        <option value="linux">Linux</option>
        <option value="darwin">macOS</option>
        <option value="windows">Windows</option>
      </select>
    </div>

    <div class="form-control mb-4">
      <label class="label">
        <span class="label-text font-medium">服务器架构 <span class="text-error">*</span></span>
      </label>
      <select v-model="config.server_arch" class="select select-bordered select-romantic w-full">
        <option value="">请选择服务器架构</option>
        <option value="amd64">AMD64</option>
        <option value="arm64">ARM64</option>
        <option value="386">386</option>
      </select>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

// 默认配置
const defaultConfig = {
  server_os: 'linux',
  server_arch: 'amd64'
}

const config = computed({
  get() {
    return { ...defaultConfig, ...props.modelValue }
  },
  set(value) {
    emit('update:modelValue', value)
  }
})
</script>

<style scoped>
/* Go 配置特定样式 */

/* 表单控件间距优化 - 与ProjectDialog保持一致 */
.form-control {
  margin-bottom: 1.25rem;
}

.form-control .label {
  margin-bottom: 0.5rem;
}

.form-control .label-text {
  font-weight: 500;
  color: #374151;
}

/* 选择框样式 */
.select-romantic {
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background-color: transparent;
  backdrop-filter: blur(2px);
  height: 3rem;
  padding: 0.75rem 1rem;
}

.select-romantic:focus {
  border-color: rgba(0, 0, 0, 0.2);
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
  background-color: rgba(255, 255, 255, 0.1);
}

.select-romantic:hover {
  border-color: rgba(0, 0, 0, 0.15);
  background-color: rgba(255, 255, 255, 0.05);
}

/* 输入框样式 */
.input-romantic {
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background-color: transparent;
  backdrop-filter: blur(2px);
  height: 3rem;
  padding: 0.75rem 1rem;
}

.input-romantic:focus {
  border-color: rgba(0, 0, 0, 0.2);
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
  background-color: rgba(255, 255, 255, 0.1);
}

.input-romantic:hover {
  border-color: rgba(0, 0, 0, 0.15);
  background-color: rgba(255, 255, 255, 0.05);
}

/* 文本域样式 */
.textarea-romantic {
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background-color: transparent;
  backdrop-filter: blur(2px);
  padding: 0.75rem 1rem;
  resize: vertical;
}

.textarea-romantic:focus {
  border-color: rgba(0, 0, 0, 0.2);
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
  background-color: rgba(255, 255, 255, 0.1);
}

.textarea-romantic:hover {
  border-color: rgba(0, 0, 0, 0.15);
  background-color: rgba(255, 255, 255, 0.05);
}

/* 复选框样式 */
.checkbox-romantic {
  border: 2px solid rgba(0, 0, 0, 0.3);
  background-color: transparent;
  border-radius: 0.25rem;
  width: 1.25rem;
  height: 1.25rem;
  transition: all 0.3s ease;
}

.checkbox-romantic:checked {
  background-color: rgba(0, 0, 0, 0.8);
  border-color: rgba(0, 0, 0, 0.8);
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
}

.checkbox-romantic:hover {
  border-color: rgba(0, 0, 0, 0.5);
  background-color: rgba(0, 0, 0, 0.05);
}

.checkbox-romantic:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}
</style>
