<template>
  <!-- 🔧 语言配置容器组件 -->
  <div class="language-config-container">
    <!-- Go 语言配置 -->
    <GoConfig
      v-if="language === 'go'"
      v-model="config"
    />

    <!-- Java 语言配置 -->
    <JavaConfig
      v-else-if="language === 'java'"
      v-model="config"
    />

    <!-- Vue 语言配置 -->
    <VueConfig
      v-else-if="language === 'vue'"
      v-model="config"
    />

    <!-- Python 语言配置 -->
    <PythonConfig
      v-else-if="language === 'python'"
      v-model="config"
      :project-name="projectName"
      :deploy-type="deployType"
      :host-id="hostId"
    />

    <!-- 未知语言提示 -->
    <div v-else-if="language" class="alert alert-warning">
      <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
      </svg>
      <span>暂不支持 {{ language }} 语言的配置</span>
    </div>
  </div>
</template>

<script setup>
import { computed, watch } from 'vue'
import GoConfig from './language-configs/GoConfig.vue'
import JavaConfig from './language-configs/JavaConfig.vue'
import VueConfig from './language-configs/VueConfig.vue'
import PythonConfig from './language-configs/PythonConfig.vue'

const props = defineProps({
  language: {
    type: String,
    default: ''
  },
  modelValue: {
    type: Object,
    default: () => ({})
  },
  projectName: {
    type: String,
    default: ''
  },
  deployType: {
    type: String,
    default: 'local'
  },
  hostId: {
    type: Number,
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

// 语言默认配置
const languageDefaults = {
  go: {
    server_os: 'linux',
    server_arch: 'amd64'
  },
  java: {
    build_tool: 'maven',
    jdk_version: '11'
  },
  vue: {
    node_version: '16',
    package_manager: 'npm'
  },
  python: {
    python_version: '3.10',
    package_manager: 'conda',
    venv_type: 'conda',
    start_command: 'python app.py',
    conda_channels: 'conda-forge,defaults',
    conda_prune: false  // 添加 conda_prune 默认值
    // 移除 venv_name 默认值，完全由用户手动输入
    // 依赖文件固定为 requirements.txt，不需要配置
  }
}

const config = computed({
  get() {
    const defaults = languageDefaults[props.language] || {}
    // 只有当 props.modelValue 中的字段为 undefined 时才使用默认值
    const result = { ...props.modelValue }
    Object.keys(defaults).forEach(key => {
      if (result[key] === undefined) {
        result[key] = defaults[key]
      }
    })
    return result
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

// 监听语言变化，合并配置而不是完全重置
watch(() => props.language, (newLanguage, oldLanguage) => {
  if (newLanguage && languageDefaults[newLanguage] && newLanguage !== oldLanguage) {
    // 保留现有配置，只添加新语言的默认值（如果字段不存在）
    const currentConfig = { ...config.value }
    const defaults = languageDefaults[newLanguage]

    Object.keys(defaults).forEach(key => {
      if (currentConfig[key] === undefined) {
        currentConfig[key] = defaults[key]
      }
    })

    config.value = currentConfig
  }
})
</script>

<style scoped>
.language-config-container {
  min-height: 100px;
}
</style>
