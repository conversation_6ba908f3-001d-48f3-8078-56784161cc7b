// 🎯 工作流节点配置定义
// 定义各种节点类型的配置属性和默认值

export const nodeConfigs = {
  // 🚀 开始节点
  start: {
    name: '开始',
    icon: '🚀',
    description: '工作流开始节点',
    configGroups: [
      {
        title: '基础配置',
        fields: [
          {
            key: 'name',
            label: '节点名称',
            type: 'text',
            required: true,
            defaultValue: '开始',
            placeholder: '请输入节点名称'
          },
          {
            key: 'description',
            label: '节点描述',
            type: 'textarea',
            defaultValue: '工作流开始节点',
            placeholder: '请输入节点描述'
          }
        ]
      }
    ]
  },

  // 📥 Git克隆节点
  git_clone: {
    name: 'Git克隆',
    icon: '📥',
    description: '从Git仓库克隆代码',
    configGroups: [
      {
        title: '基础配置',
        fields: [
          {
            key: 'name',
            label: '节点名称',
            type: 'text',
            required: true,
            defaultValue: 'Git克隆',
            placeholder: '请输入节点名称'
          },
          {
            key: 'configSource',
            label: '配置来源',
            type: 'select',
            defaultValue: 'manual',
            options: [
              { value: 'manual', label: '手动配置' },
              { value: 'preset', label: '使用预配置' }
            ],
            description: '选择手动配置或使用预设的Git配置'
          },
          {
            key: 'gitConfigId',
            label: '预配置Git仓库',
            type: 'git-config-select',
            dependsOn: 'configSource',
            showWhen: 'preset',
            description: '选择已配置的Git仓库'
          },
          {
            key: 'repository',
            label: '仓库地址',
            type: 'text',
            dependsOn: 'configSource',
            showWhen: 'manual',
            required: true,
            placeholder: 'https://github.com/user/repo.git',
            validation: {
              pattern: '^https?://.*\\.git$|^git@.*\\.git$',
              message: '请输入有效的Git仓库地址'
            }
          },
          {
            key: 'branch',
            label: '分支/标签',
            type: 'text',
            defaultValue: 'main',
            placeholder: 'main, develop, v1.0.0'
          },
          {
            key: 'targetDir',
            label: '目标目录',
            type: 'text',
            defaultValue: './src',
            placeholder: './src'
          }
        ]
      },
      {
        title: '认证配置',
        collapsed: true,
        dependsOn: 'configSource',
        showWhen: 'manual',
        fields: [
          {
            key: 'authType',
            label: '认证方式',
            type: 'select',
            defaultValue: 'none',
            options: [
              { value: 'none', label: '无需认证' },
              { value: 'token', label: 'Personal Access Token' },
              { value: 'ssh', label: 'SSH密钥' },
              { value: 'username', label: '用户名密码' }
            ]
          },
          {
            key: 'username',
            label: '用户名',
            type: 'text',
            dependsOn: 'authType',
            showWhen: 'username',
            placeholder: 'Git用户名'
          },
          {
            key: 'password',
            label: '密码/Token',
            type: 'password',
            dependsOn: 'authType',
            showWhen: ['username', 'token'],
            placeholder: '密码或Personal Access Token'
          },
          {
            key: 'sshKey',
            label: 'SSH私钥',
            type: 'textarea',
            dependsOn: 'authType',
            showWhen: 'ssh',
            placeholder: '-----BEGIN OPENSSH PRIVATE KEY-----'
          }
        ]
      },
      {
        title: '高级配置',
        collapsed: true,
        fields: [
          {
            key: 'depth',
            label: '克隆深度',
            type: 'number',
            defaultValue: 1,
            min: 1,
            description: '限制克隆历史深度，1表示只克隆最新提交'
          },
          {
            key: 'submodules',
            label: '递归克隆子模块',
            type: 'checkbox',
            defaultValue: false
          }
        ]
      }
    ]
  },

  // 🔨 Go构建节点
  go_build: {
    name: 'Go构建',
    icon: '🔨',
    description: '编译Go项目为二进制文件',
    configGroups: [
      {
        title: '基础配置',
        fields: [
          {
            key: 'name',
            label: '节点名称',
            type: 'text',
            required: true,
            defaultValue: 'Go构建',
            placeholder: '请输入节点名称'
          },
          {
            key: 'workDir',
            label: '工作目录',
            type: 'text',
            defaultValue: './src',
            placeholder: './src'
          },
          {
            key: 'mainFile',
            label: '主文件路径',
            type: 'text',
            defaultValue: './main.go',
            placeholder: './main.go, ./cmd/server/main.go'
          },
          {
            key: 'outputName',
            label: '输出文件名',
            type: 'text',
            defaultValue: 'app',
            placeholder: 'app, server, api'
          }
        ]
      },
      {
        title: '构建参数',
        fields: [
          {
            key: 'goVersion',
            label: 'Go版本',
            type: 'select',
            defaultValue: '1.21',
            options: [
              { value: '1.19', label: 'Go 1.19' },
              { value: '1.20', label: 'Go 1.20' },
              { value: '1.21', label: 'Go 1.21' },
              { value: '1.22', label: 'Go 1.22' }
            ]
          },
          {
            key: 'buildTags',
            label: '构建标签',
            type: 'text',
            placeholder: 'prod,mysql,redis',
            description: '用逗号分隔多个标签'
          },
          {
            key: 'ldflags',
            label: '链接参数',
            type: 'text',
            placeholder: '-s -w -X main.version=1.0.0',
            description: '编译时链接参数'
          }
        ]
      },
      {
        title: '环境变量',
        fields: [
          {
            key: 'cgoEnabled',
            label: '启用CGO',
            type: 'checkbox',
            defaultValue: false
          },
          {
            key: 'goos',
            label: '目标操作系统',
            type: 'select',
            defaultValue: 'linux',
            options: [
              { value: 'linux', label: 'Linux' },
              { value: 'windows', label: 'Windows' },
              { value: 'darwin', label: 'macOS' }
            ]
          },
          {
            key: 'goarch',
            label: '目标架构',
            type: 'select',
            defaultValue: 'amd64',
            options: [
              { value: 'amd64', label: 'AMD64' },
              { value: 'arm64', label: 'ARM64' },
              { value: '386', label: 'i386' }
            ]
          }
        ]
      }
    ]
  },

  // 📦 Go模块节点
  go_module: {
    name: 'Go模块',
    icon: '📦',
    description: '管理Go模块依赖',
    configGroups: [
      {
        title: '基础配置',
        fields: [
          {
            key: 'name',
            label: '节点名称',
            type: 'text',
            required: true,
            defaultValue: 'Go模块',
            placeholder: '请输入节点名称'
          },
          {
            key: 'workDir',
            label: '工作目录',
            type: 'text',
            defaultValue: './src',
            placeholder: './src'
          },
          {
            key: 'command',
            label: '执行命令',
            type: 'select',
            defaultValue: 'download',
            options: [
              { value: 'download', label: 'go mod download' },
              { value: 'tidy', label: 'go mod tidy' },
              { value: 'vendor', label: 'go mod vendor' },
              { value: 'verify', label: 'go mod verify' }
            ]
          }
        ]
      },
      {
        title: '模块配置',
        fields: [
          {
            key: 'goproxy',
            label: '模块代理',
            type: 'text',
            defaultValue: 'https://goproxy.cn,direct',
            placeholder: 'https://goproxy.cn,direct'
          },
          {
            key: 'goprivate',
            label: '私有模块',
            type: 'text',
            placeholder: 'github.com/company/*',
            description: '不通过代理下载的模块'
          },
          {
            key: 'useCache',
            label: '使用模块缓存',
            type: 'checkbox',
            defaultValue: true
          }
        ]
      }
    ]
  },

  // 🧪 Go测试节点
  go_test: {
    name: 'Go测试',
    icon: '🧪',
    description: '执行Go单元测试',
    configGroups: [
      {
        title: '基础配置',
        fields: [
          {
            key: 'name',
            label: '节点名称',
            type: 'text',
            required: true,
            defaultValue: 'Go测试',
            placeholder: '请输入节点名称'
          },
          {
            key: 'workDir',
            label: '工作目录',
            type: 'text',
            defaultValue: './src',
            placeholder: './src'
          },
          {
            key: 'testPath',
            label: '测试包路径',
            type: 'text',
            defaultValue: './...',
            placeholder: './..., ./pkg/..., ./internal/...'
          }
        ]
      },
      {
        title: '测试参数',
        fields: [
          {
            key: 'verbose',
            label: '详细输出',
            type: 'checkbox',
            defaultValue: true
          },
          {
            key: 'race',
            label: '竞态检测',
            type: 'checkbox',
            defaultValue: false
          },
          {
            key: 'coverage',
            label: '生成覆盖率报告',
            type: 'checkbox',
            defaultValue: true
          },
          {
            key: 'timeout',
            label: '超时时间(秒)',
            type: 'number',
            defaultValue: 300,
            min: 1
          },
          {
            key: 'parallel',
            label: '并发数',
            type: 'number',
            defaultValue: 4,
            min: 1,
            max: 16
          }
        ]
      }
    ]
  },

  // 🌐 远程部署节点
  remote_deploy: {
    name: '远程部署',
    icon: '🌐',
    description: '部署到远程服务器',
    configGroups: [
      {
        title: '基础配置',
        fields: [
          {
            key: 'name',
            label: '节点名称',
            type: 'text',
            required: true,
            defaultValue: '远程部署',
            placeholder: '请输入节点名称'
          },
          {
            key: 'serverHost',
            label: '服务器地址',
            type: 'text',
            required: true,
            placeholder: '*************, example.com'
          },
          {
            key: 'serverPort',
            label: 'SSH端口',
            type: 'number',
            defaultValue: 22,
            min: 1,
            max: 65535
          },
          {
            key: 'username',
            label: '用户名',
            type: 'text',
            required: true,
            defaultValue: 'root',
            placeholder: 'root, ubuntu, deploy'
          }
        ]
      },
      {
        title: '部署配置',
        fields: [
          {
            key: 'authType',
            label: '认证方式',
            type: 'select',
            defaultValue: 'key',
            options: [
              { value: 'key', label: 'SSH密钥' },
              { value: 'password', label: '密码认证' }
            ]
          },
          {
            key: 'remotePath',
            label: '远程部署路径',
            type: 'text',
            required: true,
            defaultValue: '/opt/app',
            placeholder: '/opt/app, /home/<USER>/app'
          },
          {
            key: 'transferMethod',
            label: '传输方式',
            type: 'select',
            defaultValue: 'scp',
            options: [
              { value: 'scp', label: 'SCP' },
              { value: 'sftp', label: 'SFTP' },
              { value: 'rsync', label: 'Rsync' }
            ]
          },
          {
            key: 'backupOld',
            label: '备份旧版本',
            type: 'checkbox',
            defaultValue: true
          }
        ]
      },
      {
        title: '执行脚本',
        fields: [
          {
            key: 'preDeployScript',
            label: '部署前脚本',
            type: 'textarea',
            placeholder: 'systemctl stop myapp\ncp config.yml /tmp/backup/',
            description: '部署前执行的命令'
          },
          {
            key: 'postDeployScript',
            label: '部署后脚本',
            type: 'textarea',
            placeholder: 'chmod +x /opt/app/myapp\nsystemctl start myapp',
            description: '部署后执行的命令'
          }
        ]
      }
    ]
  },

  // ▶️ 启动服务节点
  start_service: {
    name: '启动服务',
    icon: '▶️',
    description: '启动应用服务',
    configGroups: [
      {
        title: '基础配置',
        fields: [
          {
            key: 'name',
            label: '节点名称',
            type: 'text',
            required: true,
            defaultValue: '启动服务',
            placeholder: '请输入节点名称'
          },
          {
            key: 'serviceName',
            label: '服务名称',
            type: 'text',
            required: true,
            placeholder: 'myapp, api-server, web-service'
          },
          {
            key: 'executablePath',
            label: '可执行文件路径',
            type: 'text',
            required: true,
            defaultValue: './app',
            placeholder: './app, /opt/app/server'
          },
          {
            key: 'workingDir',
            label: '工作目录',
            type: 'text',
            defaultValue: './',
            placeholder: './, /opt/app'
          }
        ]
      },
      {
        title: '服务配置',
        fields: [
          {
            key: 'port',
            label: '服务端口',
            type: 'number',
            defaultValue: 8080,
            min: 1,
            max: 65535
          },
          {
            key: 'args',
            label: '启动参数',
            type: 'text',
            placeholder: '--config=config.yml --env=prod',
            description: '命令行参数'
          },
          {
            key: 'daemon',
            label: '后台运行',
            type: 'checkbox',
            defaultValue: true
          },
          {
            key: 'autoRestart',
            label: '自动重启',
            type: 'checkbox',
            defaultValue: true
          }
        ]
      },
      {
        title: '健康检查',
        fields: [
          {
            key: 'healthCheckUrl',
            label: '健康检查URL',
            type: 'text',
            placeholder: 'http://localhost:8080/health',
            description: '用于检查服务是否正常运行'
          },
          {
            key: 'healthCheckInterval',
            label: '检查间隔(秒)',
            type: 'number',
            defaultValue: 30,
            min: 5
          },
          {
            key: 'maxRetries',
            label: '最大重试次数',
            type: 'number',
            defaultValue: 3,
            min: 1
          }
        ]
      }
    ]
  }
}

// 🎯 获取节点配置
export function getNodeConfig(nodeType) {
  return nodeConfigs[nodeType] || null
}

// 🎯 获取节点默认数据
export function getNodeDefaultData(nodeType) {
  const config = getNodeConfig(nodeType)
  if (!config) return {}
  
  const defaultData = {}
  config.configGroups.forEach(group => {
    group.fields.forEach(field => {
      if (field.defaultValue !== undefined) {
        defaultData[field.key] = field.defaultValue
      }
    })
  })
  
  return defaultData
}

// 🎯 验证节点配置
export function validateNodeConfig(nodeType, data) {
  const config = getNodeConfig(nodeType)
  if (!config) return { valid: true, errors: [] }
  
  const errors = []
  
  config.configGroups.forEach(group => {
    group.fields.forEach(field => {
      const value = data[field.key]
      
      // 必填验证
      if (field.required && (!value || value.toString().trim() === '')) {
        errors.push(`${field.label}是必填项`)
      }
      
      // 格式验证
      if (value && field.validation) {
        const pattern = new RegExp(field.validation.pattern)
        if (!pattern.test(value)) {
          errors.push(field.validation.message || `${field.label}格式不正确`)
        }
      }
      
      // 数字范围验证
      if (field.type === 'number' && value !== undefined) {
        if (field.min !== undefined && value < field.min) {
          errors.push(`${field.label}不能小于${field.min}`)
        }
        if (field.max !== undefined && value > field.max) {
          errors.push(`${field.label}不能大于${field.max}`)
        }
      }
    })
  })
  
  return {
    valid: errors.length === 0,
    errors
  }
}
