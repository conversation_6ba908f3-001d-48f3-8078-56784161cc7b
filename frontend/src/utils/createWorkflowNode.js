import getId from "./randomId";

const createWorkflowNode = (event, addNodes, project, store, zoomLevel = 1) => {
  let id = getId();

  const rawData = event.dataTransfer?.getData("application/vueflow");

  // 解析节点数据
  let parsedData;
  let type;

  try {
    // 尝试解析JSON数据
    parsedData = JSON.parse(rawData);
    type = parsedData.type || rawData;
  } catch (e) {
    // 如果不是JSON，直接使用原始数据作为type
    type = rawData;
    parsedData = { type: rawData };
  }

  // 获取拖拽时记录的相对位置百分比
  const relativeData = event.dataTransfer?.getData("application/relative-position");
  const { relativeX, relativeY } = relativeData ? JSON.parse(relativeData) : { relativeX: 0.5, relativeY: 0.5 };

  // 获取Vue Flow画布容器的边界矩形
  const vueFlowBounds = event.currentTarget.getBoundingClientRect();

  // 获取画布节点的原始尺寸
  const originalNodeSize = getNodeSize(type);

  // 根据当前缩放比例调整节点尺寸
  const adjustedNodeSize = {
    width: originalNodeSize.width * zoomLevel,
    height: originalNodeSize.height * zoomLevel
  };

  // 使用缩放调整后的尺寸进行百分比映射计算
  const position = project({
    x: event.clientX - vueFlowBounds.left - (adjustedNodeSize.width * relativeX),
    y: event.clientY - vueFlowBounds.top - (adjustedNodeSize.height * relativeY)
  });

  console.log('🎯 缩放感知定位 - 缩放比例:', zoomLevel, '相对位置:', { relativeX, relativeY }, '原始尺寸:', originalNodeSize, '调整后尺寸:', adjustedNodeSize, '最终位置:', position);

  // 根据节点类型选择模板
  let nodeTemplate = 'custom';
  if (type === 'rectangle') {
    nodeTemplate = 'rectangle';
  } else if (type === 'empty') {
    nodeTemplate = 'empty';
  } else if (type === 'enhanced') {
    nodeTemplate = 'enhanced';
  } else if (type === 'colored') {
    nodeTemplate = 'colored';
  } else if (type === 'gradient') {
    nodeTemplate = 'gradient';
  } else if (type === 'animated') {
    nodeTemplate = 'animated';
  } else if (type === 'shadow') {
    nodeTemplate = 'shadow';
  }

  let newNode = {
    id: type + id,
    type: nodeTemplate,
    position,
    label: `${type} node`,
    data: {
      nodeType: type,
      icon: getNodeIcon(type),
      color: getNodeColor(type),
      description: getNodeDescription(type),
      properties: getDefaultProperties(type)
    }
  };

  // 根据节点类型设置默认数据到store
  const nodeData = {
    id: newNode.id,
    type: type,
    label: getNodeLabel(type),
    color: getNodeColor(type),
    properties: getDefaultProperties(type)
  };

  // 使用$patch方式更新store（类似Facebook Messenger）
  if (store && typeof store.$patch === 'function') {
    store.$patch((state) => {
      state.layers.messages.push(nodeData);
    });
  } else if (store && typeof store.addWorkflowNode === 'function') {
    store.addWorkflowNode(nodeData);
  }

  // 添加到Vue Flow
  addNodes([newNode]);

  console.log('✅ 节点创建成功:', newNode);
};

// 获取节点图标
const getNodeIcon = (type) => {
  const icons = {
    'start': '🚀',
    'end': '🏁',
    'git': '📥',
    'build': '🔨',
    'deploy': '🚀',
    'test': '🧪',
    'condition': '❓',
    'loop': '🔄',
    'parallel': '⚡',
    'wait': '⏰',
    // 测试节点图标
    'rectangle': '⬜',
    'empty': '⬛',
    'enhanced': '✨',
    'colored': '🎨',
    'gradient': '🌈',
    'animated': '⚡',
    'shadow': '🌑',

  };
  return icons[type] || '📦';
};

// 获取节点颜色
const getNodeColor = (type) => {
  const colors = {
    'start': '#10b981',
    'end': '#ef4444',
    'git': '#f59e0b',
    'build': '#3b82f6',
    'deploy': '#8b5cf6',
    'test': '#06b6d4',
    'condition': '#f97316',
    'loop': '#84cc16',
    'parallel': '#ec4899',
    'wait': '#6b7280',
    // 测试节点颜色
    'rectangle': '#6b7280',
    'empty': '#999999',
    'enhanced': '#3b82f6',
    'colored': '#10b981',
    'gradient': '#8b5cf6',
    'animated': '#f59e0b',
    'shadow': '#374151'

  };
  return colors[type] || '#6b7280';
};

// 获取节点标签
const getNodeLabel = (type) => {
  const labels = {
    'start': '开始',
    'end': '结束',
    'git': 'Git克隆',
    'build': '构建项目',
    'deploy': '部署应用',
    'test': '运行测试',
    'condition': '条件判断',
    'loop': '循环执行',
    'parallel': '并行执行',
    'wait': '等待延迟',
    // 测试节点标签
    'rectangle': '长方形',
    'empty': '空白',
    'enhanced': '美化',
    'colored': '彩色',
    'gradient': '渐变',
    'animated': '动画',
    'shadow': '阴影'

  };
  return labels[type] || type;
};

// 获取节点描述
const getNodeDescription = (type) => {
  const descriptions = {
    'start': '工作流开始节点',
    'end': '工作流结束节点',
    'git': '从Git仓库克隆代码',
    'build': '编译和构建项目',
    'deploy': '部署到目标环境',
    'test': '执行自动化测试',
    'condition': '根据条件分支执行',
    'loop': '重复执行指定次数',
    'parallel': '并行执行多个任务',
    'wait': '等待指定时间',
    // 测试节点描述
    'rectangle': '极简测试节点',
    'empty': '完全空白节点',
    'enhanced': '美化长方形节点',
    'colored': '带背景颜色节点',
    'gradient': '渐变背景节点',
    'animated': '动画效果节点',
    'shadow': '带阴影效果节点'

  };
  return descriptions[type] || '工作流节点';
};

// 获取画布节点尺寸（根据节点类型）
const getNodeSize = (type) => {
  const nodeSizes = {
    // 基础节点尺寸
    'custom': { width: 150, height: 60 },
    'rectangle': { width: 120, height: 60 },
    'empty': { width: 100, height: 50 },
    'enhanced': { width: 180, height: 80 },
    'colored': { width: 140, height: 70 },
    'gradient': { width: 160, height: 80 },
    'animated': { width: 150, height: 70 },
    'shadow': { width: 150, height: 70 },

    // 工作流节点尺寸
    'start': { width: 150, height: 60 },
    'end': { width: 150, height: 60 },
    'git': { width: 150, height: 60 },
    'build': { width: 150, height: 60 },
    'deploy': { width: 150, height: 60 },
    'test': { width: 150, height: 60 },
    'condition': { width: 150, height: 60 },
    'loop': { width: 150, height: 60 },
    'parallel': { width: 150, height: 60 },
    'wait': { width: 150, height: 60 }
  };

  return nodeSizes[type] || { width: 150, height: 60 }; // 默认尺寸
};

// 获取默认属性
const getDefaultProperties = (type) => {
  const defaultProps = {
    start: { description: '工作流开始' },
    end: { description: '工作流结束' },
    git: {
      repository: 'https://github.com/user/repo.git',
      branch: 'main',
      credentials: ''
    },
    build: {
      command: 'npm run build',
      workingDir: './',
      timeout: 300
    },
    deploy: {
      strategy: 'rolling',
      timeout: 300,
      replicas: 1
    },
    test: {
      command: 'npm test',
      coverage: true,
      timeout: 120
    },
    condition: {
      expression: 'true',
      trueLabel: '是',
      falseLabel: '否'
    },
    loop: {
      iterations: 10,
      condition: 'i < 10'
    },
    parallel: {
      maxConcurrency: 3
    },
    wait: {
      duration: 10,
      unit: 'seconds'
    }
  };
  return defaultProps[type] || {};
};

export { createWorkflowNode };
