// HTTP请求工具
// 基于fetch API的简单封装

const BASE_URL = 'http://localhost:8080'

// 默认请求配置
const defaultConfig = {
  headers: {
    'Content-Type': 'application/json',
  },
  credentials: 'include', // 包含cookies
}

// 请求拦截器
const requestInterceptor = (config) => {
  // 可以在这里添加认证token等
  return config
}

// 响应拦截器
const responseInterceptor = async (response) => {
  if (!response.ok) {
    const error = new Error(`HTTP Error: ${response.status}`)
    error.status = response.status
    error.statusText = response.statusText
    
    try {
      const errorData = await response.json()
      error.data = errorData
    } catch (e) {
      // 如果响应不是JSON格式，忽略
    }
    
    throw error
  }
  
  try {
    const data = await response.json()
    return data
  } catch (e) {
    // 如果响应不是JSON格式，返回空对象
    return {}
  }
}

// 主要的request函数
const request = async (options) => {
  const {
    url,
    method = 'GET',
    data,
    params,
    headers = {},
    ...otherOptions
  } = options

  // 构建完整URL
  let fullUrl = url.startsWith('http') ? url : `${BASE_URL}${url}`
  
  // 处理查询参数
  if (params) {
    const searchParams = new URLSearchParams()
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        searchParams.append(key, params[key])
      }
    })
    const queryString = searchParams.toString()
    if (queryString) {
      fullUrl += (fullUrl.includes('?') ? '&' : '?') + queryString
    }
  }

  // 构建请求配置
  const config = {
    ...defaultConfig,
    ...otherOptions,
    method: method.toUpperCase(),
    headers: {
      ...defaultConfig.headers,
      ...headers,
    },
  }

  // 处理请求体
  if (data && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
    if (typeof data === 'object') {
      config.body = JSON.stringify(data)
    } else {
      config.body = data
    }
  }

  // 应用请求拦截器
  const interceptedConfig = requestInterceptor(config)

  try {
    // 发送请求
    const response = await fetch(fullUrl, interceptedConfig)
    
    // 应用响应拦截器
    const result = await responseInterceptor(response)
    
    return result
  } catch (error) {
    console.error('Request failed:', error)
    throw error
  }
}

// 便捷方法
request.get = (url, options = {}) => {
  return request({ ...options, url, method: 'GET' })
}

request.post = (url, data, options = {}) => {
  return request({ ...options, url, method: 'POST', data })
}

request.put = (url, data, options = {}) => {
  return request({ ...options, url, method: 'PUT', data })
}

request.delete = (url, options = {}) => {
  return request({ ...options, url, method: 'DELETE' })
}

request.patch = (url, data, options = {}) => {
  return request({ ...options, url, method: 'PATCH', data })
}

export default request
