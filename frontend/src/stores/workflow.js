import { defineStore } from "pinia";

export const useWorkflowStore = defineStore("workflow", {
  state: () => {
    return {
      layers: {
        messageToEdit: "",
        elements: {},
        messages: [],
        default_values: {
          image:
            "https://images.unsplash.com/photo-1545703399-4313b14625d9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxleHBsb3JlLWZlZWR8NHx8fGVufDB8fHx8&w=1000&q=80",
          video: "",
        },
      },
      // 🎯 当前项目信息
      currentProject: {
        type: 'go', // go, python, vue, java等
        environment: 'production', // development, test, production
        deployType: 'remote' // local, remote
      },
      // 🎯 智能工作流节点库 - 根据项目类型动态显示
      nodeTypes: [
        // 🎮 通用控制节点 - 所有项目都显示
        {
          type: 'start',
          name: '开始',
          icon: '🚀',
          color: '#10b981',
          description: '工作流开始节点',
          category: 'control',
          projectTypes: ['*'] // 通用节点
        },
        {
          type: 'end',
          name: '结束',
          icon: '🏁',
          color: '#ef4444',
          description: '工作流结束节点',
          category: 'control',
          projectTypes: ['*']
        },

        // 📥 通用数据源节点
        {
          type: 'git_clone',
          name: 'Git克隆',
          icon: '📥',
          color: '#f59e0b',
          description: '从Git仓库克隆代码',
          category: 'source',
          projectTypes: ['*']
        },

        // 🔨 Go项目专用构建节点
        {
          type: 'go_build',
          name: 'Go构建',
          icon: '🔨',
          color: '#00add8',
          description: '编译Go项目为二进制文件',
          category: 'build',
          projectTypes: ['go']
        },
        {
          type: 'go_test',
          name: 'Go测试',
          icon: '🧪',
          color: '#00add8',
          description: '执行Go单元测试',
          category: 'test',
          projectTypes: ['go']
        },
        {
          type: 'go_mod',
          name: 'Go模块',
          icon: '📦',
          color: '#00add8',
          description: '管理Go模块依赖',
          category: 'build',
          projectTypes: ['go']
        },

        // 🐍 Python项目专用节点
        {
          type: 'python_env',
          name: 'Python环境',
          icon: '🐍',
          color: '#3776ab',
          description: '创建Python虚拟环境',
          category: 'build',
          projectTypes: ['python']
        },
        {
          type: 'pip_install',
          name: 'Pip安装',
          icon: '📦',
          color: '#3776ab',
          description: '安装Python依赖包',
          category: 'build',
          projectTypes: ['python']
        },
        {
          type: 'conda_env',
          name: 'Conda环境',
          icon: '🔬',
          color: '#44a047',
          description: '管理Conda环境',
          category: 'build',
          projectTypes: ['python']
        },
        {
          type: 'poetry_install',
          name: 'Poetry安装',
          icon: '📝',
          color: '#3776ab',
          description: '使用Poetry管理依赖',
          category: 'build',
          projectTypes: ['python']
        },
        // ⚡ Vue/Node.js项目专用节点
        {
          type: 'npm_install',
          name: 'NPM安装',
          icon: '📦',
          color: '#cb3837',
          description: '安装Node.js依赖',
          category: 'build',
          projectTypes: ['vue', 'react', 'nodejs']
        },
        {
          type: 'vue_build',
          name: 'Vue构建',
          icon: '⚡',
          color: '#4fc08d',
          description: '构建Vue项目',
          category: 'build',
          projectTypes: ['vue']
        },
        {
          type: 'webpack_build',
          name: 'Webpack构建',
          icon: '📦',
          color: '#8dd6f9',
          description: '使用Webpack构建',
          category: 'build',
          projectTypes: ['vue', 'react']
        },

        // 🚀 通用部署节点
        {
          type: 'local_deploy',
          name: '本地部署',
          icon: '📁',
          color: '#8b5cf6',
          description: '部署到本地环境',
          category: 'build',
          projectTypes: ['*']
        },
        {
          type: 'remote_deploy',
          name: '远程部署',
          icon: '🌐',
          color: '#8b5cf6',
          description: '部署到远程服务器',
          category: 'build',
          projectTypes: ['*']
        },
        {
          type: 'service_start',
          name: '启动服务',
          icon: '▶️',
          color: '#10b981',
          description: '启动应用服务',
          category: 'build',
          projectTypes: ['*']
        },
        {
          type: 'health_check',
          name: '健康检查',
          icon: '❤️',
          color: '#ef4444',
          description: '检查服务健康状态',
          category: 'test',
          projectTypes: ['*']
        },
        // 🧠 通用逻辑控制节点
        {
          type: 'condition',
          name: '条件判断',
          icon: '❓',
          color: '#f97316',
          description: '根据条件分支执行',
          category: 'logic',
          projectTypes: ['*']
        },
        {
          type: 'parallel',
          name: '并行执行',
          icon: '⚡',
          color: '#ec4899',
          description: '并行执行多个任务',
          category: 'logic',
          projectTypes: ['*']
        },
        {
          type: 'loop',
          name: '循环执行',
          icon: '🔄',
          color: '#84cc16',
          description: '重复执行指定次数',
          category: 'logic',
          projectTypes: ['*']
        },

        // 🛠️ 通用工具节点
        {
          type: 'script_execute',
          name: '脚本执行',
          icon: '📜',
          color: '#6b7280',
          description: '执行自定义脚本',
          category: 'utility',
          projectTypes: ['*']
        },
        {
          type: 'file_operation',
          name: '文件操作',
          icon: '📂',
          color: '#6b7280',
          description: '文件复制移动删除',
          category: 'utility',
          projectTypes: ['*']
        },
        {
          type: 'wait',
          name: '等待延迟',
          icon: '⏰',
          color: '#6b7280',
          description: '等待指定时间',
          category: 'utility',
          projectTypes: ['*']
        },
        {
          type: 'email_notification',
          name: '邮件通知',
          icon: '📧',
          color: '#3b82f6',
          description: '发送邮件通知',
          category: 'utility',
          projectTypes: ['*']
        },
        // 🧪 测试节点 - 用于UI测试，所有项目都可用
        {
          type: 'rectangle',
          name: '长方形',
          icon: '⬜',
          color: '#6b7280',
          description: '极简测试节点',
          category: 'test',
          projectTypes: ['*']
        },
        {
          type: 'empty',
          name: '空白',
          icon: '⬛',
          color: '#999999',
          description: '完全空白节点',
          category: 'test',
          projectTypes: ['*']
        },
        {
          type: 'enhanced',
          name: '美化',
          icon: '✨',
          color: '#3b82f6',
          description: '美化长方形节点',
          category: 'test',
          projectTypes: ['*']
        },
        {
          type: 'colored',
          name: '彩色',
          icon: '🎨',
          color: '#10b981',
          description: '带背景颜色节点',
          category: 'test',
          projectTypes: ['*']
        },
        {
          type: 'gradient',
          name: '渐变',
          icon: '🌈',
          color: '#8b5cf6',
          description: '渐变背景节点',
          category: 'test',
          projectTypes: ['*']
        },
        {
          type: 'animated',
          name: '动画',
          icon: '⚡',
          color: '#f59e0b',
          description: '动画效果节点',
          category: 'test',
          projectTypes: ['*']
        },
        {
          type: 'shadow',
          name: '阴影',
          icon: '🌑',
          color: '#374151',
          description: '带阴影效果节点',
          category: 'test',
          projectTypes: ['*']
        }

      ]
    };
  },
  getters: {
    getMessages: (state) => {
      return () => state.layers.messages;
    },
    getMessageById: (state) => {
      return (messageId) =>
        state.layers.messages.find((element) => element.id == messageId);
    },
    getItemById: (state) => {
      return (messageId, itemId) =>
        state
          .getMessageById(messageId)
          .items.find((element) => element.id == itemId);
    },
    getDefaultValues: (state) => {
      return () => state.layers.default_values;
    },
    getNodeTypes: (state) => {
      // 🎯 根据当前项目类型智能过滤节点
      const currentProjectType = state.currentProject.type;
      return state.nodeTypes.filter(node => {
        // 安全检查：确保 projectTypes 存在且是数组
        if (!node.projectTypes || !Array.isArray(node.projectTypes)) {
          console.warn(`节点 ${node.type} 缺少 projectTypes 属性，默认显示`);
          return true; // 默认显示
        }
        return node.projectTypes.includes('*') ||
               node.projectTypes.includes(currentProjectType);
      });
    },
    getNodeTypesByCategory: (state) => {
      return (category) => {
        const currentProjectType = state.currentProject.type;
        return state.nodeTypes.filter(node => {
          // 安全检查：确保 projectTypes 存在且是数组
          if (!node.projectTypes || !Array.isArray(node.projectTypes)) {
            console.warn(`节点 ${node.type} 缺少 projectTypes 属性，默认显示`);
            return node.category === category; // 只检查分类
          }
          return node.category === category &&
                 (node.projectTypes.includes('*') || node.projectTypes.includes(currentProjectType));
        });
      };
    },
    // 🎯 获取当前项目信息
    getCurrentProject: (state) => {
      return state.currentProject;
    },
    // 🎯 获取项目特定节点
    getProjectSpecificNodes: (state) => {
      return (projectType) => {
        return state.nodeTypes.filter(node =>
          node.projectTypes.includes(projectType) &&
          !node.projectTypes.includes('*')
        );
      };
    }
  },
  actions: {
    setMessageItems(messageId, items) {
      let result = this.layers.messages.find(
        (element) => element.id === messageId
      );
      result.items = items;
    },
    addWorkflowNode(nodeData) {
      this.layers.messages.push(nodeData);
    },
    removeWorkflowNode(nodeId) {
      this.layers.messages = this.layers.messages.filter(
        (item) => item.id !== nodeId
      );
    },
    updateWorkflowNode(nodeId, updates) {
      const node = this.layers.messages.find(item => item.id === nodeId);
      if (node) {
        Object.assign(node, updates);
      }
    },
    // 🎯 设置当前项目信息
    setCurrentProject(projectInfo) {
      this.currentProject = { ...this.currentProject, ...projectInfo };
    },
    // 🎯 设置项目类型
    setProjectType(projectType) {
      this.currentProject.type = projectType;
    }
  },
});
