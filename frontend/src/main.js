import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import Dashboard from './views/Dashboard.vue'
import ProjectList from './views/ProjectList.vue'
import HostList from './views/HostList.vue'
import GitConfigManagement from './views/GitConfigManagement.vue'
// 工作流设计器 - 使用Vue Flow版本
import VueFlowWorkflow from './components/workflow/vueflow/VueFlowWorkflow.vue'
import ToastDemo from './views/ToastDemo.vue'
import ScrollTest from './views/ScrollTest.vue'
// 工作流项目管理
import WorkflowProjectList from './views/WorkflowProjectList.vue'
import WorkflowProjectForm from './views/WorkflowProjectForm.vue'
import WorkflowProjectDesign from './views/WorkflowProjectDesign.vue'

const routes = [
  { path: '/', redirect: '/dashboard' },
  { path: '/dashboard', component: Dashboard },
  { path: '/projects', component: ProjectList },
  { path: '/hosts', component: HostList },
  { path: '/git-configs', component: GitConfigManagement, meta: { title: 'Git配置管理' } },
  { path: '/workflow', component: VueFlowWorkflow },
  { path: '/toast-demo', component: ToastDemo },
  { path: '/scroll-test', component: ScrollTest, meta: { title: '滚动测试' } },
  // 工作流项目管理路由
  { path: '/workflow-projects', component: WorkflowProjectList, meta: { title: '工作流项目管理' } },
  { path: '/workflow-projects/create', component: WorkflowProjectForm, meta: { title: '创建工作流项目' } },
  { path: '/workflow-projects/:id/edit', component: WorkflowProjectForm, meta: { title: '编辑工作流项目' } },
  { path: '/workflow-projects/:id/design', component: WorkflowProjectDesign, meta: { title: '工作流设计' } }
]
const router = createRouter({
  history: createWebHistory(),
  routes
})

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.mount('#app')
