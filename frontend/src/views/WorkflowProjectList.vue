<template>
  <div class="workflow-project-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">工作流项目管理</h1>
        <p class="page-description">管理您的工作流项目，配置自动化部署流程</p>
      </div>
      <button class="btn-primary" @click="createProject">
        <span class="icon">➕</span>
        新建项目
      </button>
    </div>

    <!-- 过滤器和搜索 -->
    <div class="filters-section">
      <div class="filter-group">
        <label>编程语言：</label>
        <select v-model="filters.language" @change="applyFilters">
          <option value="">全部</option>
          <option value="go">🔨 Go</option>
          <option value="python">🐍 Python</option>
          <option value="vue">⚡ Vue</option>
          <option value="java">☕ Java</option>
          <option value="nodejs">📦 Node.js</option>
          <option value="react">⚛️ React</option>
        </select>
      </div>

      <div class="filter-group">
        <label>部署环境：</label>
        <select v-model="filters.environment" @change="applyFilters">
          <option value="">全部</option>
          <option value="development">🔧 开发环境</option>
          <option value="test">🧪 测试环境</option>
          <option value="production">🚀 生产环境</option>
        </select>
      </div>

      <div class="filter-group">
        <label>项目类型：</label>
        <select v-model="filters.projectType" @change="applyFilters">
          <option value="">全部</option>
          <option value="frontend">🎨 前端项目</option>
          <option value="backend">⚙️ 后端项目</option>
          <option value="fullstack">🔄 全栈项目</option>
        </select>
      </div>

      <div class="search-group">
        <input 
          type="text" 
          v-model="filters.search" 
          @input="applyFilters"
          placeholder="搜索项目名称..."
          class="search-input"
        />
      </div>
    </div>

    <!-- 项目网格 -->
    <div class="projects-grid" v-if="!loading">
      <div 
        v-for="project in projects" 
        :key="project.id" 
        class="project-card"
      >
        <div class="card-header">
          <h3 class="project-name">{{ project.name }}</h3>
          <div class="card-actions">
            <button class="btn-icon" @click="editProject(project)" title="编辑">
              ✏️
            </button>
            <button class="btn-icon" @click="deleteProject(project)" title="删除">
              🗑️
            </button>
          </div>
        </div>

        <div class="card-content">
          <div class="project-meta">
            <span class="tag" :class="`tag-${project.language}`">
              {{ getLanguageIcon(project.language) }} {{ getLanguageLabel(project.language) }}
            </span>
            <span class="tag" :class="`tag-${project.environment}`">
              {{ getEnvironmentIcon(project.environment) }} {{ getEnvironmentLabel(project.environment) }}
            </span>
            <span class="tag" :class="`tag-${project.project_type}`">
              {{ getProjectTypeIcon(project.project_type) }} {{ getProjectTypeLabel(project.project_type) }}
            </span>
          </div>

          <p class="project-description">
            {{ project.description || '暂无描述' }}
          </p>

          <div class="project-stats">
            <span class="stat">
              <span class="stat-icon">📊</span>
              执行次数: {{ project.execution_count || 0 }}
            </span>
            <span class="stat" v-if="project.last_execution_at">
              <span class="stat-icon">⏰</span>
              最后执行: {{ formatDate(project.last_execution_at) }}
            </span>
          </div>
        </div>

        <div class="card-footer">
          <button 
            class="btn-primary btn-design" 
            @click="designWorkflow(project)"
          >
            <span class="icon">🎨</span>
            流程设计
          </button>
          <button 
            class="btn-secondary" 
            @click="executeWorkflow(project)"
            :disabled="!project.has_workflow"
          >
            <span class="icon">▶️</span>
            执行
          </button>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="projects.length === 0" class="empty-state">
        <div class="empty-icon">📋</div>
        <h3>暂无项目</h3>
        <p>创建您的第一个工作流项目开始自动化部署</p>
        <button class="btn-primary" @click="createProject">
          <span class="icon">➕</span>
          创建项目
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="spinner"></div>
      <p>加载中...</p>
    </div>

    <!-- 分页 -->
    <div class="pagination" v-if="totalPages > 1">
      <button 
        class="btn-page" 
        @click="goToPage(currentPage - 1)"
        :disabled="currentPage <= 1"
      >
        上一页
      </button>
      
      <span class="page-info">
        第 {{ currentPage }} 页，共 {{ totalPages }} 页
      </span>
      
      <button 
        class="btn-page" 
        @click="goToPage(currentPage + 1)"
        :disabled="currentPage >= totalPages"
      >
        下一页
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const projects = ref([])
const currentPage = ref(1)
const pageSize = ref(12)
const totalCount = ref(0)

const filters = reactive({
  language: '',
  environment: '',
  projectType: '',
  search: ''
})

// 计算属性
const totalPages = computed(() => Math.ceil(totalCount.value / pageSize.value))

// 方法
const loadProjects = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取项目列表
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 500))
    
    projects.value = [
      {
        id: 1,
        name: 'Go API 服务',
        language: 'go',
        environment: 'production',
        project_type: 'backend',
        description: '用户管理API服务',
        has_workflow: true,
        execution_count: 15,
        last_execution_at: '2024-01-15T10:30:00Z',
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        name: 'Vue 前端应用',
        language: 'vue',
        environment: 'development',
        project_type: 'frontend',
        description: '管理后台前端应用',
        has_workflow: false,
        execution_count: 0,
        created_at: '2024-01-02T00:00:00Z'
      }
    ]
    totalCount.value = projects.value.length
  } catch (error) {
    console.error('加载项目列表失败:', error)
  } finally {
    loading.value = false
  }
}

const applyFilters = () => {
  currentPage.value = 1
  loadProjects()
}

const goToPage = (page) => {
  currentPage.value = page
  loadProjects()
}

const createProject = () => {
  router.push('/workflow-projects/create')
}

const editProject = (project) => {
  router.push(`/workflow-projects/${project.id}/edit`)
}

const deleteProject = (project) => {
  if (confirm(`确定要删除项目"${project.name}"吗？`)) {
    // TODO: 调用删除API
    console.log('删除项目:', project.id)
    loadProjects()
  }
}

const designWorkflow = (project) => {
  router.push(`/workflow-projects/${project.id}/design`)
}

const executeWorkflow = (project) => {
  if (!project.has_workflow) {
    alert('请先配置工作流')
    return
  }
  // TODO: 调用执行API
  console.log('执行工作流:', project.id)
}

// 辅助函数
const getLanguageIcon = (language) => {
  const icons = {
    go: '🔨', python: '🐍', vue: '⚡', java: '☕', nodejs: '📦', react: '⚛️'
  }
  return icons[language] || '💻'
}

const getLanguageLabel = (language) => {
  const labels = {
    go: 'Go', python: 'Python', vue: 'Vue.js', java: 'Java', nodejs: 'Node.js', react: 'React'
  }
  return labels[language] || language
}

const getEnvironmentIcon = (environment) => {
  const icons = {
    development: '🔧', test: '🧪', production: '🚀'
  }
  return icons[environment] || '🌍'
}

const getEnvironmentLabel = (environment) => {
  const labels = {
    development: '开发环境', test: '测试环境', production: '生产环境'
  }
  return labels[environment] || environment
}

const getProjectTypeIcon = (projectType) => {
  const icons = {
    frontend: '🎨', backend: '⚙️', fullstack: '🔄'
  }
  return icons[projectType] || '📦'
}

const getProjectTypeLabel = (projectType) => {
  const labels = {
    frontend: '前端项目', backend: '后端项目', fullstack: '全栈项目'
  }
  return labels[projectType] || projectType
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadProjects()
})
</script>

<style scoped>
.workflow-project-list {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
}

.header-content p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

.filters-section {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.filter-group, .search-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
}

.filter-group select, .search-input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.search-input {
  width: 200px;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.project-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.2s;
}

.project-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.project-name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.project-meta {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.tag-go { background: #dbeafe; color: #1e40af; }
.tag-python { background: #fef3c7; color: #92400e; }
.tag-vue { background: #d1fae5; color: #065f46; }
.tag-development { background: #f3f4f6; color: #374151; }
.tag-test { background: #fef3c7; color: #92400e; }
.tag-production { background: #fee2e2; color: #991b1b; }
.tag-frontend { background: #ede9fe; color: #5b21b6; }
.tag-backend { background: #dbeafe; color: #1e40af; }
.tag-fullstack { background: #d1fae5; color: #065f46; }

.project-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
}

.project-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  font-size: 12px;
  color: #6b7280;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
}

.card-footer {
  display: flex;
  gap: 12px;
}

.btn-primary, .btn-secondary, .btn-icon, .btn-page {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-secondary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-icon {
  padding: 6px;
  background: transparent;
  border: 1px solid #d1d5db;
  border-radius: 4px;
}

.btn-icon:hover {
  background: #f3f4f6;
}

.btn-design {
  flex: 1;
}

.empty-state {
  text-align: center;
  padding: 64px 32px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.loading-state {
  text-align: center;
  padding: 64px 32px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.page-info {
  font-size: 14px;
  color: #6b7280;
}

.btn-page {
  padding: 8px 16px;
}

.btn-page:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
