<template>
  <div class="container mx-auto px-6 py-8">
    <!-- 🎯 页面标题 -->
    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold text-gray-800 mb-2">DaisyUI Toast 通知演示</h1>
      <p class="text-gray-600">精美的通知提示系统，替代浏览器默认弹窗</p>
    </div>

    <!-- 🎨 演示卡片 -->
    <div class="max-w-4xl mx-auto">
      <div class="card bg-white shadow-lg border border-gray-200">
        <div class="card-body">
          <h2 class="card-title text-xl mb-6">Toast 通知类型演示</h2>
          
          <!-- 基础通知 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <button 
              @click="showSuccessToast"
              class="btn btn-success"
            >
              <Icon name="check" :size="16" />
              成功通知
            </button>
            
            <button 
              @click="showErrorToast"
              class="btn btn-error"
            >
              <Icon name="close" :size="16" />
              错误通知
            </button>
            
            <button 
              @click="showWarningToast"
              class="btn btn-warning"
            >
              <Icon name="warning" :size="16" />
              警告通知
            </button>
            
            <button 
              @click="showInfoToast"
              class="btn btn-info"
            >
              <Icon name="info" :size="16" />
              信息通知
            </button>
          </div>

          <!-- 高级通知 -->
          <div class="divider">高级通知演示</div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
            <button 
              @click="showLongToast"
              class="btn btn-outline btn-primary"
            >
              长时间通知 (10秒)
            </button>
            
            <button 
              @click="showTitleToast"
              class="btn btn-outline btn-secondary"
            >
              带标题的通知
            </button>
            
            <button 
              @click="showNonClosableToast"
              class="btn btn-outline btn-accent"
            >
              不可关闭通知
            </button>
            
            <button 
              @click="showMultipleToasts"
              class="btn btn-outline btn-neutral"
            >
              多个通知
            </button>
          </div>

          <!-- 操作按钮 -->
          <div class="divider">操作</div>
          
          <div class="flex gap-4">
            <button 
              @click="clearAllToasts"
              class="btn btn-ghost"
            >
              清除所有通知
            </button>
          </div>
        </div>
      </div>

      <!-- 🎯 项目操作演示 -->
      <div class="card bg-white shadow-lg border border-gray-200 mt-6">
        <div class="card-body">
          <h2 class="card-title text-xl mb-6">项目操作演示</h2>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              @click="simulateProjectCreate"
              class="btn btn-primary"
            >
              <Icon name="add" :size="16" />
              创建项目
            </button>

            <button
              @click="simulateProjectBuild"
              class="btn btn-secondary"
            >
              <Icon name="build" :size="16" />
              构建项目
            </button>

            <button
              @click="simulateProjectDelete"
              class="btn btn-error"
            >
              <Icon name="delete" :size="16" />
              删除项目
            </button>
          </div>
        </div>
      </div>

      <!-- 🎭 确认对话框演示 -->
      <div class="card bg-white shadow-lg border border-gray-200 mt-6">
        <div class="card-body">
          <h2 class="card-title text-xl mb-6">精美确认对话框演示</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button
              @click="demoDeleteConfirm"
              class="btn btn-error btn-outline"
            >
              <Icon name="delete" :size="16" />
              删除确认
            </button>

            <button
              @click="demoClearConfirm"
              class="btn btn-warning btn-outline"
            >
              <Icon name="refresh" :size="16" />
              清空确认
            </button>

            <button
              @click="demoLogoutConfirm"
              class="btn btn-info btn-outline"
            >
              <Icon name="logout" :size="16" />
              退出确认
            </button>

            <button
              @click="demoResetConfirm"
              class="btn btn-neutral btn-outline"
            >
              <Icon name="settings" :size="16" />
              重置确认
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import Icon from '../components/Icon.vue'
import { toast } from '../composables/useToast.js'
import { confirm } from '../composables/useConfirm.js'

// 🌸 基础通知演示
const showSuccessToast = () => {
  toast.success('操作成功完成！', { title: '成功' })
}

const showErrorToast = () => {
  toast.error('操作失败，请重试', { title: '错误' })
}

const showWarningToast = () => {
  toast.warning('请注意检查输入内容', { title: '警告' })
}

const showInfoToast = () => {
  toast.info('这是一条信息提示', { title: '提示' })
}

// 🎭 高级通知演示
const showLongToast = () => {
  toast.info('这是一个长时间显示的通知，将在10秒后自动消失', {
    title: '长时间通知',
    duration: 10000
  })
}

const showTitleToast = () => {
  toast.success('这是一个带有详细标题的通知消息，展示了精美的设计风格', {
    title: '恭喜！操作成功',
    duration: 5000
  })
}

const showNonClosableToast = () => {
  toast.warning('这个通知无法手动关闭，只能等待自动消失', {
    title: '重要提醒',
    closable: false,
    duration: 6000
  })
}

const showMultipleToasts = () => {
  toast.info('第一个通知', { title: '通知 1' })
  setTimeout(() => {
    toast.success('第二个通知', { title: '通知 2' })
  }, 500)
  setTimeout(() => {
    toast.warning('第三个通知', { title: '通知 3' })
  }, 1000)
  setTimeout(() => {
    toast.error('第四个通知', { title: '通知 4' })
  }, 1500)
}

const clearAllToasts = () => {
  toast.clear()
}

// 🚀 项目操作演示
const simulateProjectCreate = () => {
  toast.success('项目创建成功！', { title: '创建成功' })
}

const simulateProjectBuild = () => {
  toast.success('构建请求已发送，正在构建中...', { title: '构建开始' })
}

const simulateProjectDelete = async () => {
  try {
    await confirm.confirmDelete('示例项目', '项目')
    toast.success('项目删除成功！', { title: '删除成功' })
  } catch (error) {
    if (error !== false) {
      toast.error('删除失败！', { title: '删除失败' })
    }
  }
}

// 🎭 确认对话框演示
const demoDeleteConfirm = async () => {
  try {
    await confirm.confirmDelete('重要文件', '文件')
    toast.success('文件删除成功！', { title: '删除成功' })
  } catch (error) {
    if (error !== false) {
      toast.info('已取消删除操作', { title: '操作取消' })
    }
  }
}

const demoClearConfirm = async () => {
  try {
    await confirm.confirmClear('缓存数据')
    toast.success('缓存清空成功！', { title: '清空成功' })
  } catch (error) {
    if (error !== false) {
      toast.info('已取消清空操作', { title: '操作取消' })
    }
  }
}

const demoLogoutConfirm = async () => {
  try {
    await confirm.confirmLogout()
    toast.success('已退出登录！', { title: '退出成功' })
  } catch (error) {
    if (error !== false) {
      toast.info('已取消退出操作', { title: '操作取消' })
    }
  }
}

const demoResetConfirm = async () => {
  try {
    await confirm.confirmReset('系统配置')
    toast.success('配置重置成功！', { title: '重置成功' })
  } catch (error) {
    if (error !== false) {
      toast.info('已取消重置操作', { title: '操作取消' })
    }
  }
}
</script>

<style scoped>
.card {
  backdrop-filter: blur(10px);
}

.btn {
  transition: all 0.2s ease;
}

.btn:hover {
  transform: translateY(-1px);
}
</style>
