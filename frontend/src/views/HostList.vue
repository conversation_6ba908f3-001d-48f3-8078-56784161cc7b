<template>
  <!-- 🎯 浪漫风格主机管理页面 -->
  <div class="dashboard-container">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h1 class="text-3xl font-bold text-romantic-gradient mb-2">
            主机管理
          </h1>
          <p class="text-gray-600">
            管理您的服务器主机，让部署变得简单优雅 ✨
          </p>
        </div>
        <button class="btn btn-romantic btn-lg shadow-lg" @click="showCreateDialog">
          <Icon name="plus" :size="20" />
          新建主机
        </button>
      </div>
    </div>



    <!-- 快捷筛选和视图切换 -->
    <div class="mb-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <!-- 快捷筛选按钮 -->
        <div class="flex flex-wrap gap-3">
          <button
            class="btn btn-sm"
            :class="activeFilter === 'all' ? 'btn-romantic' : 'btn-outline'"
            @click="setFilter('all')"
          >
            <Icon name="sparkles" :size="16" />
            全部主机
          </button>
          <button
            class="btn btn-sm transition-all duration-200"
            :class="activeFilter === 'prod' ? 'btn-error text-white border-red-500 bg-red-500 shadow-lg' : 'btn-outline border-red-300 text-red-600 hover:bg-red-50'"
            @click="setFilter('prod')"
          >
            <Icon name="alert-triangle" :size="16" />
            生产环境
          </button>
          <button
            class="btn btn-sm transition-all duration-200"
            :class="activeFilter === 'test' ? 'btn-info text-white border-blue-500 bg-blue-500 shadow-lg' : 'btn-outline border-blue-300 text-blue-600 hover:bg-blue-50'"
            @click="setFilter('test')"
          >
            <Icon name="beaker" :size="16" />
            测试环境
          </button>
        </div>

        <!-- 视图切换和排序 -->
        <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4">
          <!-- 视图切换按钮 -->
          <div class="btn-group flex-shrink-0">
            <button
              class="btn btn-sm flex-1 sm:flex-initial"
              :class="viewMode === 'grid' ? 'btn-romantic' : 'btn-ghost'"
              @click="viewMode = 'grid'"
            >
              <Icon name="grid" :size="16" />
              <span class="hidden sm:inline ml-1">网格</span>
            </button>
            <button
              class="btn btn-sm flex-1 sm:flex-initial"
              :class="viewMode === 'list' ? 'btn-romantic' : 'btn-ghost'"
              @click="viewMode = 'list'"
            >
              <Icon name="list" :size="16" />
              <span class="hidden sm:inline ml-1">列表</span>
            </button>
          </div>

          <!-- 排序选择 -->
          <select class="select select-bordered select-sm w-full sm:w-32 flex-shrink-0" v-model="sortBy">
            <option value="name">按名称</option>
            <option value="environment">按环境</option>
            <option value="status">按状态</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 主机列表内容 -->
    <div class="mb-8">
      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="text-center">
          <div class="animate-spin w-8 h-8 border-4 rounded-full mx-auto mb-4"
               style="border-color: #f472b6; border-top-color: transparent;"></div>
          <p class="text-gray-500">加载中...</p>
        </div>
      </div>

      <div v-else-if="!filteredHosts || filteredHosts.length === 0" class="text-center py-12">
        <div class="mb-4">
          <Icon name="server" :size="48" class="text-gray-300 mx-auto" />
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无主机数据</h3>
        <p class="text-gray-500 mb-4">开始添加您的第一台主机吧</p>
        <button class="btn btn-romantic btn-lg shadow-lg" @click="showCreateDialog">
          <Icon name="plus" :size="20" />
          新建主机
        </button>
      </div>

      <!-- 网格视图 -->
      <div v-else-if="viewMode === 'grid'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <HostCard
          v-for="host in filteredHosts"
          :key="host.id"
          :host="host"
          @edit="showEditDialog"
          @delete="deleteHost"
          @connect="testConnection"
          @click="showEditDialog"
        />
      </div>

      <!-- 列表视图 -->
      <div v-else class="bg-white rounded-xl shadow-sm overflow-hidden" style="border: 1px solid #f3f4f6;">
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead style="background-color: #f9fafb; border-bottom: 1px solid #e5e7eb;">
              <tr>
                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主机信息</th>
                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">环境</th>
                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">认证方式</th>
                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
                <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white" style="border-top: 1px solid #e5e7eb;">
              <tr v-for="host in filteredHosts" :key="host.id" class="transition-colors" style="border-bottom: 1px solid #f3f4f6;" @mouseenter="$event.target.style.backgroundColor='#f9fafb'" @mouseleave="$event.target.style.backgroundColor='white'">
                <td class="px-6 py-4">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 rounded-lg flex items-center justify-center" style="background: linear-gradient(135deg, #f472b6, #a78bfa);">
                      <Icon name="server" :size="16" class="text-white" />
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ host.name }}</div>
                      <div class="text-sm text-gray-500">{{ host.ip }}:{{ host.port }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :style="host.environment === 'prod' ? 'background-color: #fbcfe8; color: #ec4899; box-shadow: 0 1px 2px rgba(236, 72, 153, 0.15);' : 'background-color: #e9d5ff; color: #a855f7; box-shadow: 0 1px 2px rgba(168, 85, 247, 0.15);'">
                    <Icon :name="host.environment === 'prod' ? 'alert-triangle' : 'beaker'" :size="12" class="mr-1" />
                    {{ host.environment === 'prod' ? '生产环境' : '测试环境' }}
                  </span>
                </td>
                <td class="px-6 py-4">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :style="host.auth_type === 'password' ? 'background-color: #dcfce7; color: #15803d; box-shadow: 0 1px 2px rgba(21, 128, 61, 0.1);' : 'background-color: #f3e8ff; color: #7c3aed; box-shadow: 0 1px 2px rgba(124, 58, 237, 0.1);'">
                    <Icon :name="host.auth_type === 'password' ? 'shield' : 'key'" :size="12" class="mr-1" />
                    {{ host.auth_type === 'password' ? '密码认证' : '私钥认证' }}
                  </span>
                </td>
                <td class="px-6 py-4">
                  <div class="flex items-center">
                    <div class="w-2 h-2 rounded-full mr-2"
                         :style="{ backgroundColor: host.status === 'active' ? '#4ade80' : '#9ca3af' }"></div>
                    <span class="text-sm text-gray-900">
                      {{ host.status === 'active' ? '活跃' : '停用' }}
                    </span>
                  </div>
                </td>
                <td class="px-6 py-4">
                  <span class="text-sm text-gray-600">{{ host.description || '-' }}</span>
                </td>
                <td class="px-6 py-4 text-right">
                  <div class="flex items-center justify-end space-x-2">
                    <button
                      class="btn btn-ghost btn-sm text-gray-600 hover:text-romantic-600"
                      @click="testConnection(host)"
                      :disabled="host.testing"
                      :title="host.testing ? '测试中...' : '连接测试'"
                    >
                      <span v-if="host.testing" class="loading loading-spinner loading-sm"></span>
                      <Icon v-else name="link" :size="16" />
                    </button>
                    <button
                      class="btn btn-ghost btn-sm text-gray-600 hover:text-romantic-600"
                      @click="showEditDialog(host)"
                      title="编辑"
                    >
                      <Icon name="edit" :size="16" />
                    </button>
                    <button
                      class="btn btn-ghost btn-sm text-gray-600 hover:text-red-600"
                      @click="deleteHost(host)"
                      title="删除"
                    >
                      <Icon name="delete" :size="16" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 分页组件 -->
    <div v-if="pagination.total > 0" class="flex justify-center mt-8 w-full max-w-full overflow-x-auto">
      <div class="join flex-shrink-0">
        <button
          class="join-item btn btn-sm"
          :disabled="pagination.page <= 1"
          @click="handleCurrentChange(pagination.page - 1)"
        >
          <span class="hidden sm:inline">上一页</span>
          <span class="sm:hidden">上页</span>
        </button>
        <button
          v-for="page in getPageNumbers()"
          :key="page"
          class="join-item btn btn-sm"
          :class="{ 'btn-romantic': page === pagination.page }"
          @click="handleCurrentChange(page)"
        >
          {{ page }}
        </button>
        <button
          class="join-item btn btn-sm"
          :disabled="pagination.page >= Math.ceil(pagination.total / pagination.size)"
          @click="handleCurrentChange(pagination.page + 1)"
        >
          <span class="hidden sm:inline">下一页</span>
          <span class="sm:hidden">下页</span>
        </button>
      </div>
    </div>

    <!-- 主机对话框 -->
    <HostDialog
      :visible="dialogVisible"
      :host-data="currentHost"
      :is-edit="isEdit"
      @confirm="handleHostSave"
      @cancel="handleDialogCancel"
    />
  </div>
</template>


<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import Icon from '../components/Icon.vue'
import HostCard from '../components/HostCard.vue'
import HostDialog from '../components/HostDialog.vue'
import * as hostAPI from '../api/host.js'
import { toast } from '../composables/useToast.js'
import { confirm } from '../composables/useConfirm.js'

// 🌸 使用全局Toast通知系统
const showMessage = (message, type = 'info') => {
  switch(type) {
    case 'success':
      toast.success(message)
      break
    case 'error':
      toast.error(message)
      break
    case 'warning':
      toast.warning(message)
      break
    case 'info':
    default:
      toast.info(message)
      break
  }
}

// 响应式数据
const hosts = ref([])
const loading = ref(false)
const dialogVisible = ref(false)
const currentHost = ref(null)
const isEdit = ref(false)
const testingHosts = ref(new Set())

// 新增的响应式数据
const viewMode = ref('grid') // 'grid' | 'list'
const activeFilter = ref('all') // 'all' | 'prod' | 'test'
const sortBy = ref('environment') // 'name' | 'environment' | 'status'

// 分页数据
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 计算属性：筛选后的主机列表
const filteredHosts = computed(() => {
  let filtered = [...hosts.value]

  // 环境筛选
  if (activeFilter.value !== 'all') {
    filtered = filtered.filter(host => host.environment === activeFilter.value)
  }

  // 排序
  switch (sortBy.value) {
    case 'name':
      return filtered.sort((a, b) => a.name.localeCompare(b.name))
    case 'environment':
      return filtered.sort((a, b) => {
        // 生产环境排在前面
        if (a.environment === 'prod' && b.environment !== 'prod') return -1
        if (a.environment !== 'prod' && b.environment === 'prod') return 1
        return a.name.localeCompare(b.name)
      })
    case 'status':
      return filtered.sort((a, b) => {
        // 活跃状态排在前面
        if (a.status === 'active' && b.status !== 'active') return -1
        if (a.status !== 'active' && b.status === 'active') return 1
        return a.name.localeCompare(b.name)
      })
    default:
      return filtered
  }
})

import {
  getHosts,
  createHost,
  updateHost,
  deleteHost as deleteHostApi,
  testHostConnection
} from '../api/host.js'

// 监听排序变化
watch(sortBy, () => {
  pagination.page = 1 // 重置到第一页
  loadHosts() // 重新加载数据
})

// 页面初始化
onMounted(() => {
  loadHosts()
})










// 加载主机列表
const loadHosts = async () => {
  try {
    loading.value = true

    // 确定环境筛选参数
    const environment = activeFilter.value === 'all' ? '' : activeFilter.value

    const result = await getHosts(
      pagination.page,
      pagination.size,
      environment,
      sortBy.value,
      'asc'
    )

    // 确保每个主机都有testing属性
    hosts.value = (result.data.hosts || []).map(host => ({
      ...host,
      testing: false
    }))
    pagination.total = result.data.total || 0
  } catch (error) {
    console.log('API调用失败，使用模拟数据:', error.message)
    // 使用模拟数据进行测试
    hosts.value = [
      {
        id: 1,
        name: '测试服务器1',
        ip: '*************',
        port: 22,
        username: 'root',
        environment: 'test',
        auth_type: 'password',
        status: 'active',
        description: '测试环境服务器'
      },
      {
        id: 2,
        name: '生产服务器',
        ip: '*********',
        port: 22,
        username: 'deploy',
        environment: 'prod',
        auth_type: 'private_key',
        status: 'active',
        description: '生产环境服务器'
      }
    ]
    pagination.total = 2
    showMessage('加载主机列表失败，显示模拟数据: ' + error.message, 'error')
  } finally {
    loading.value = false
  }
}

// 显示创建弹窗
const showCreateDialog = () => {
  currentHost.value = null
  isEdit.value = false
  dialogVisible.value = true
}

// 显示编辑弹窗
const showEditDialog = (host) => {
  currentHost.value = { ...host }
  isEdit.value = true
  dialogVisible.value = true
}

// 处理主机保存
const handleHostSave = async (hostData) => {
  try {
    if (isEdit.value) {
      await updateHost(currentHost.value.id, hostData)
      showMessage('主机更新成功！', 'success')
    } else {
      await createHost(hostData)
      showMessage('主机创建成功！', 'success')
    }
    
    dialogVisible.value = false
    loadHosts()
  } catch (error) {
    console.error('保存主机失败:', error)
    showMessage('保存主机失败: ' + error.message, 'error')
  }
}

// 处理弹窗取消
const handleDialogCancel = () => {
  dialogVisible.value = false
  currentHost.value = null
}

// 测试连接
const testConnection = async (host) => {
  try {
    // 设置测试状态
    host.testing = true
    showMessage(`正在测试连接到 ${host.name}...`, 'info')

    // 调用真实的连接测试API
    const result = await hostAPI.testHostConnection(host.id)

    if (result.success) {
      const latency = result.data?.latency || 0
      const message = result.data?.message || '连接成功'
      showMessage(`连接测试成功！${message}${latency > 0 ? ` 延迟: ${latency}ms` : ''}`, 'success')
    } else {
      const errorMsg = result.message || '连接测试失败'
      showMessage(`连接测试失败: ${errorMsg}`, 'error')
    }
  } catch (error) {
    console.error('测试连接失败:', error)
    showMessage('连接测试失败: ' + error.message, 'error')
  } finally {
    // 清除测试状态
    host.testing = false
  }
}

// 删除主机
const deleteHost = async (host) => {
  try {
    // 🎭 使用精美确认对话框
    await confirm.confirmDelete(host.name, '主机')

    await deleteHostApi(host.id)
    showMessage('主机删除成功！', 'success')
    loadHosts()
  } catch (error) {
    // 用户取消删除时不显示错误
    if (error !== false) {
      console.error('删除主机失败:', error)
      showMessage('删除主机失败: ' + error.message, 'error')
    }
  }
}

// 环境筛选方法
const filterEnvironment = (value, row) => {
  return row.environment === value
}

// 刷新列表
const refreshList = () => {
  loadHosts()
}

// 处理排序变化
const handleSortChange = ({ column, prop, order }) => {
  // 这里可以实现服务端排序
  console.log('排序变化:', { column, prop, order })
}

// 处理页面大小变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadHosts()
}

// 设置筛选条件
const setFilter = (filter) => {
  activeFilter.value = filter
  pagination.page = 1 // 重置到第一页
  loadHosts() // 重新加载数据
}



// 处理当前页变化
const handleCurrentChange = (page) => {
  pagination.page = page
  loadHosts()
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 分页函数
const getPageNumbers = () => {
  const total = Math.ceil(pagination.total / pagination.size)
  const current = pagination.page
  const pages = []

  // 显示当前页前后2页
  const start = Math.max(1, current - 2)
  const end = Math.min(total, current + 2)

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
}
</script>

<style scoped>
/* 🎯 浪漫风格主机管理样式 */

/* 渐变文字效果 */
.text-romantic-gradient {
  background: linear-gradient(135deg, #ec4899, #8b5cf6, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 浪漫按钮样式 */
.btn-romantic {
  background: linear-gradient(to right, #f472b6, #a855f7);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.btn-romantic:hover {
  background: linear-gradient(to right, #ec4899, #9333ea);
  transform: scale(1.05);
  box-shadow: 0 4px 6px -1px rgba(236, 72, 153, 0.1), 0 2px 4px -1px rgba(236, 72, 153, 0.06);
}

/* 卡片悬浮效果 */
.host-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.host-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 表格优化 */
.table-container {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 加载动画 */
@keyframes romantic-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-romantic-spin {
  animation: romantic-spin 1s linear infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
  }
}

/* 按钮组样式优化 */
.btn-group .btn {
  border-color: #e5e7eb;
}

.btn-group .btn:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-group .btn:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left-width: 0;
}

.btn-group .btn:not(:first-child):not(:last-child) {
  border-radius: 0;
  border-left-width: 0;
}

/* 统计卡片样式 */
:deep(.stat-card) {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.stat-card:hover) {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}
</style>
