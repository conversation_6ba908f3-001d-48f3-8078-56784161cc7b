<template>
  <!-- 🎯 浪漫风格仪表板 -->
  <div class="dashboard-container">
    <!-- 欢迎区域 -->
    <div class="mb-8">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h1 class="text-3xl font-bold text-romantic-gradient mb-2">
            欢迎回来！
          </h1>
          <p class="text-gray-600">
            今天是个美好的一天，让我们开始当牛马吧 ✨
          </p>
        </div>
        <div class="hidden lg:block">
          <div class="text-right">
            <div class="text-sm text-gray-500">{{ currentDate }}</div>
            <div class="text-lg font-semibold text-gray-800">{{ currentTime }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <StatCard
        :value="stats.totalProjects"
        label="总项目数"
        icon="list"
        variant="romantic"
        :change="5.2"
      />
      <StatCard
        :value="stats.activeHosts"
        label="活跃主机"
        icon="setting"
        variant="dreamy"
        :change="2.1"
      />
      <StatCard
        :value="stats.todayDeployments"
        label="今日部署"
        icon="upload"
        variant="success"
        :change="12.5"
      />
      <StatCard
        :value="stats.successRate"
        label="成功率"
        icon="check"
        variant="gradient"
        :change="1.8"
      />
    </div>

    <!-- 功能模块卡片 -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold text-gray-800 mb-6">功能模块</h2>
      <div class="dashboard-grid">
        <DashboardCard
          v-for="module in modules"
          :key="module.id"
          :title="module.title"
          :description="module.description"
          :icon="module.icon"
          :stats="module.stats"
          :badge="module.badge"
          :variant="module.variant"
          :quick-actions="module.quickActions"
          :status="module.status"
          @click="navigateToModule(module.path)"
          @action="navigateToModule(module.path)"
          @quick-action="handleQuickAction"
        />
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 最近部署 -->
      <div class="card-romantic p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">最近部署</h3>
        <div class="space-y-3">
          <div 
            v-for="deployment in recentDeployments" 
            :key="deployment.id"
            class="flex items-center justify-between p-3 rounded-xl bg-gradient-to-r from-romantic-50 to-dreamy-50"
          >
            <div class="flex items-center space-x-3">
              <div :class="getStatusIcon(deployment.status)">
                <Icon :name="getStatusIconName(deployment.status)" :size="16" class="text-white" />
              </div>
              <div>
                <div class="font-medium text-gray-800">{{ deployment.project }}</div>
                <div class="text-sm text-gray-500">{{ deployment.time }}</div>
              </div>
            </div>
            <div :class="getStatusBadge(deployment.status)">
              {{ deployment.status }}
            </div>
          </div>
        </div>
        <div class="mt-4 text-center">
          <button class="btn btn-ghost btn-sm text-romantic-600">
            查看全部
          </button>
        </div>
      </div>

      <!-- 系统状态 -->
      <div class="card-romantic p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">系统状态</h3>
        <div class="space-y-4">
          <div 
            v-for="status in systemStatus" 
            :key="status.name"
            class="flex items-center justify-between"
          >
            <div class="flex items-center space-x-3">
              <div :class="getSystemStatusIcon(status.status)"></div>
              <span class="text-gray-700">{{ status.name }}</span>
            </div>
            <div class="text-sm text-gray-500">{{ status.value }}</div>
          </div>
        </div>
        <div class="mt-4">
          <div class="bg-gradient-to-r from-romantic-50 to-dreamy-50 rounded-xl p-3">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">系统健康度</span>
              <span class="text-lg font-bold text-romantic-600">98%</span>
            </div>
            <div class="mt-2">
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-gradient-to-r from-romantic-400 to-dreamy-400 h-2 rounded-full" style="width: 98%"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import DashboardCard from '../components/DashboardCard.vue'
import StatCard from '../components/StatCard.vue'
import Icon from '../components/Icon.vue'

const router = useRouter()

// 当前时间
const currentDate = ref('')
const currentTime = ref('')

// 统计数据
const stats = ref({
  totalProjects: 24,
  activeHosts: 8,
  todayDeployments: 15,
  successRate: '99.2%'
})

// 功能模块
const modules = ref([
  {
    id: 1,
    title: '项目管理',
    description: '管理您的所有项目和部署配置',
    icon: 'list',
    path: '/projects',
    variant: 'default',
    badge: '24个项目',
    stats: { value: 24, label: '总项目', icon: 'list' },
    quickActions: [
      { name: '新建项目', icon: 'plus' },
      { name: '导入项目', icon: 'upload' }
    ],
    status: 'online'
  },
  {
    id: 2,
    title: '主机管理',
    description: '管理部署服务器和主机配置',
    icon: 'setting',
    path: '/hosts',
    variant: 'default',
    badge: '8台在线',
    stats: { value: 8, label: '活跃主机', icon: 'setting' },
    quickActions: [
      { name: '添加主机', icon: 'plus' },
      { name: '批量检测', icon: 'refresh' }
    ],
    status: 'online'
  },
  {
    id: 3,
    title: '部署中心',
    description: '一键部署和构建管理',
    icon: 'upload',
    path: '/deploy',
    variant: 'default',
    badge: '15次今日',
    stats: { value: 15, label: '今日部署', icon: 'upload' },
    quickActions: [
      { name: '快速部署', icon: 'upload' },
      { name: '批量构建', icon: 'tools' }
    ],
    status: 'online'
  },
  {
    id: 4,
    title: '监控中心',
    description: '实时监控和日志查看',
    icon: 'monitor',
    path: '/monitor',
    variant: 'default',
    badge: '实时监控',
    stats: { value: '99.2%', label: '可用性', icon: 'check' },
    quickActions: [
      { name: '查看日志', icon: 'document' },
      { name: '性能分析', icon: 'chart' }
    ],
    status: 'online'
  }
])

// 最近部署
const recentDeployments = ref([
  { id: 1, project: 'API服务', status: '成功', time: '2分钟前' },
  { id: 2, project: '前端应用', status: '进行中', time: '5分钟前' },
  { id: 3, project: '数据服务', status: '成功', time: '10分钟前' },
  { id: 4, project: '用户中心', status: '失败', time: '15分钟前' }
])

// 系统状态
const systemStatus = ref([
  { name: 'CPU使用率', status: 'good', value: '45%' },
  { name: '内存使用率', status: 'good', value: '62%' },
  { name: '磁盘空间', status: 'warning', value: '78%' },
  { name: '网络延迟', status: 'good', value: '12ms' }
])

// 更新时间
const updateTime = () => {
  const now = new Date()
  currentDate.value = now.toLocaleDateString('zh-CN', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric',
    weekday: 'long'
  })
  currentTime.value = now.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit'
  })
}

let timeInterval = null

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})

// 导航到模块
const navigateToModule = (path) => {
  router.push(path)
}

// 处理快速操作
const handleQuickAction = (action) => {
  console.log('Quick action:', action)
  // 这里可以添加具体的快速操作逻辑
}

// 获取状态图标
const getStatusIcon = (status) => {
  const icons = {
    '成功': 'w-6 h-6 bg-green-500 rounded-full flex items-center justify-center',
    '进行中': 'w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center',
    '失败': 'w-6 h-6 bg-red-500 rounded-full flex items-center justify-center'
  }
  return icons[status] || icons['成功']
}

const getStatusIconName = (status) => {
  const icons = {
    '成功': 'check',
    '进行中': 'clock',
    '失败': 'close'
  }
  return icons[status] || 'check'
}

const getStatusBadge = (status) => {
  const badges = {
    '成功': 'badge badge-success badge-sm',
    '进行中': 'badge badge-info badge-sm',
    '失败': 'badge badge-error badge-sm'
  }
  return badges[status] || badges['成功']
}

const getSystemStatusIcon = (status) => {
  const icons = {
    'good': 'w-3 h-3 bg-green-400 rounded-full',
    'warning': 'w-3 h-3 bg-yellow-400 rounded-full',
    'error': 'w-3 h-3 bg-red-400 rounded-full'
  }
  return icons[status] || icons['good']
}
</script>

<style scoped>
/* 仪表板特定样式 */
.dashboard-container {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 统计卡片动画 */
.grid > * {
  animation: slideUp 0.6s ease-out;
  animation-fill-mode: both;
}

.grid > *:nth-child(1) { animation-delay: 0.1s; }
.grid > *:nth-child(2) { animation-delay: 0.2s; }
.grid > *:nth-child(3) { animation-delay: 0.3s; }
.grid > *:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
