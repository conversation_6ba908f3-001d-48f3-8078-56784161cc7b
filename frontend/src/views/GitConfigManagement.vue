<template>
  <div class="dashboard-container">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h1 class="text-3xl font-bold text-romantic-gradient mb-2">
            Git认证管理
          </h1>
          <p class="text-gray-600">
            管理您的Git服务认证信息，一个认证配置可用于多个仓库 🔐
          </p>
        </div>
        <div class="hidden lg:block">
          <button class="btn btn-romantic btn-lg shadow-lg" @click="showCreateDialog">
            <Icon name="plus" :size="16" class="mr-2" />
            新增认证
          </button>
        </div>
      </div>
      <!-- 移动端新建按钮 -->
      <div class="lg:hidden mb-4">
        <button class="btn btn-romantic btn-block" @click="showCreateDialog">
          <span class="mr-2">➕</span>
          新增认证
        </button>
      </div>
    </div>

    <!-- 快捷筛选按钮 -->
    <div class="mb-6">
      <div class="flex flex-wrap gap-3">
        <button
          class="btn btn-sm"
          :class="statusFilter === '' ? 'btn-romantic' : 'btn-outline'"
          @click="setStatusFilter('')"
        >
          <span class="mr-1">💖</span>
          全部认证
        </button>
        <button
          class="btn btn-sm"
          :class="statusFilter === 'active' ? 'btn-romantic' : 'btn-outline'"
          @click="setStatusFilter('active')"
        >
          <span class="mr-1">🟢</span>
          活跃认证
        </button>
        <button
          class="btn btn-sm"
          :class="statusFilter === 'inactive' ? 'btn-romantic' : 'btn-outline'"
          @click="setStatusFilter('inactive')"
        >
          <span class="mr-1">⚪</span>
          停用认证
        </button>
        <button
          class="btn btn-sm btn-outline"
          @click="showBulkActions"
        >
          <span class="mr-1">⚠️</span>
          批量操作
        </button>
        <button
          class="btn btn-sm btn-outline"
          @click="refreshConfigs"
        >
          <span class="mr-1">🔄</span>
          刷新列表
        </button>
      </div>
    </div>

    <!-- 搜索和视图控制区域 -->
    <div class="mb-6">
      <div class="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
        <!-- 搜索框 -->
        <div class="flex-1 max-w-md">
          <div class="relative">
            <input
              v-model="searchQuery"
              @input="handleSearch"
              placeholder="搜索认证名称、描述..."
              class="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-romantic-pink focus:border-transparent"
            />
            <span class="absolute left-3 top-2.5 text-gray-400">🔍</span>
          </div>
        </div>
        <!-- 视图控制 -->
        <div class="flex gap-2 items-center">
          <button class="btn btn-sm btn-romantic">📋 网格</button>
          <button class="btn btn-sm btn-outline">📊 列表</button>
          <select class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
            <option>按时间</option>
            <option>按名称</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 配置列表标题 -->
    <div class="mb-4">
      <h2 class="text-xl font-semibold text-gray-800">Git认证列表</h2>
    </div>

    <!-- 配置卡片列表 -->
    <div class="configs-grid">
      <!-- 加载状态 -->
      <div v-if="loading" class="col-span-full flex flex-col items-center justify-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-romantic-pink mb-4"></div>
        <p class="text-gray-600">加载中...</p>
      </div>

      <!-- 空状态 -->
      <div v-else-if="configs.length === 0" class="col-span-full flex flex-col items-center justify-center py-12">
        <div class="text-6xl mb-4">📦</div>
        <h3 class="text-xl font-semibold text-gray-800 mb-2">暂无Git认证</h3>
        <p class="text-gray-600 mb-6">点击"新增认证"按钮创建您的第一个Git认证配置</p>
        <button class="btn btn-romantic" @click="showCreateDialog">
          <span class="mr-2">➕</span>
          新增认证
        </button>
      </div>

      <!-- 配置卡片网格 -->
      <template v-else>
        <div v-for="config in configs" :key="config.id" class="project-card">
          <!-- 卡片头部 -->
          <div class="card-header">
            <div class="project-avatar">
              <span class="avatar-text">{{ config.name.charAt(0).toUpperCase() }}</span>
            </div>
            <div class="project-info">
              <h3 class="project-name">{{ config.name }}</h3>
              <p class="project-description">{{ config.description || 'Git认证配置' }}</p>
            </div>
            <div class="project-menu">
              <button class="menu-btn">⋮</button>
            </div>
          </div>



          <!-- 描述信息 -->
          <div class="git-info" v-if="config.description">
            <div class="git-info-item">
              <span class="info-label">描述:</span>
              <span class="info-value">{{ config.description }}</span>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="card-actions">
            <div class="action-buttons">
              <button @click="editConfig(config)" class="action-btn secondary">
                <Icon name="edit" :size="14" class="mr-1" />
                编辑
              </button>
            </div>
          </div>
        </div>
      </template>
    </div>

    <!-- 分页组件 -->
    <Pagination
      :current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      @page-change="changePage"
    />

    <!-- Git配置对话框 -->
    <div v-if="showDialog" class="modal modal-open">
      <div class="modal-box modal-scroll-container w-11/12 max-w-3xl max-h-[90vh] overflow-y-auto">
        <!-- 标题 -->
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-xl font-bold text-gray-800">
            {{ isEditing ? '编辑Git认证' : '新建Git认证' }}
          </h3>
          <button
            type="button"
            class="btn btn-ghost btn-sm btn-circle"
            @click="closeDialog"
          >
            ✕
          </button>
        </div>

        <form @submit.prevent="saveConfig" class="space-y-6">
          <!-- 基本信息卡片 -->
          <div class="card bg-base-100 border border-base-300">
            <div class="card-body p-4">
              <h4 class="card-title text-base mb-4 text-gray-700">基本信息</h4>

              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">认证名称 <span class="text-error">*</span></span>
                </label>
                <input
                  v-model="formData.name"
                  type="text"
                  placeholder="请输入认证配置名称"
                  class="input input-bordered input-romantic w-full"
                  required
                />
              </div>





              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">描述</span>
                </label>
                <textarea
                  v-model="formData.description"
                  placeholder="请输入配置描述"
                  class="textarea textarea-bordered textarea-romantic w-full"
                  rows="3"
                ></textarea>
              </div>
            </div>
          </div>

          <!-- 认证配置卡片 -->
          <div class="card bg-base-100 border border-base-300">
            <div class="card-body p-4">
              <h4 class="card-title text-base mb-4 text-gray-700">认证配置</h4>

              <div class="form-control mb-4">
                <label class="label">
                  <span class="label-text font-medium">认证方式</span>
                </label>
                <div class="flex flex-wrap gap-4">
                  <label class="cursor-pointer label">
                    <input
                      v-model="formData.auth_type"
                      type="radio"
                      value="none"
                      class="radio radio-romantic mr-2"
                    />
                    <span class="label-text">无认证</span>
                  </label>
                  <label class="cursor-pointer label">
                    <input
                      v-model="formData.auth_type"
                      type="radio"
                      value="username"
                      class="radio radio-romantic mr-2"
                    />
                    <span class="label-text">用户名密码</span>
                  </label>
                  <label class="cursor-pointer label">
                    <input
                      v-model="formData.auth_type"
                      type="radio"
                      value="token"
                      class="radio radio-romantic mr-2"
                    />
                    <span class="label-text">Token</span>
                  </label>
                  <label class="cursor-pointer label">
                    <input
                      v-model="formData.auth_type"
                      type="radio"
                      value="ssh_key"
                      class="radio radio-romantic mr-2"
                    />
                    <span class="label-text">SSH密钥</span>
                  </label>
                </div>
              </div>

              <!-- 用户名密码认证 -->
              <div v-if="formData.auth_type === 'username'" class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-medium">用户名 <span class="text-error">*</span></span>
                  </label>
                  <input
                    v-model="formData.username"
                    type="text"
                    placeholder="请输入用户名"
                    class="input input-bordered input-romantic w-full"
                    required
                  />
                </div>
                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-medium">密码 <span class="text-error">*</span></span>
                  </label>
                  <input
                    v-model="formData.password"
                    type="password"
                    placeholder="请输入密码"
                    class="input input-bordered input-romantic w-full"
                    required
                  />
                </div>
              </div>

              <!-- Token认证 -->
              <div v-if="formData.auth_type === 'token'" class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-medium">用户名</span>
                  </label>
                  <input
                    v-model="formData.username"
                    type="text"
                    placeholder="请输入用户名"
                    class="input input-bordered input-romantic w-full"
                  />
                </div>
                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-medium">Token <span class="text-error">*</span></span>
                  </label>
                  <input
                    v-model="formData.password"
                    type="password"
                    placeholder="请输入访问Token"
                    class="input input-bordered input-romantic w-full"
                    required
                  />
                </div>
              </div>

              <!-- SSH密钥认证 -->
              <div v-if="formData.auth_type === 'ssh_key'">
                <div class="form-control mb-4">
                  <label class="label">
                    <span class="label-text font-medium">用户名</span>
                  </label>
                  <input
                    v-model="formData.username"
                    type="text"
                    placeholder="git"
                    class="input input-bordered input-romantic w-full"
                  />
                </div>
                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-medium">SSH私钥 <span class="text-error">*</span></span>
                  </label>
                  <textarea
                    v-model="formData.ssh_key"
                    placeholder="请粘贴SSH私钥内容"
                    class="textarea textarea-bordered textarea-romantic w-full"
                    rows="6"
                    required
                  ></textarea>
                </div>
              </div>
            </div>
          </div>



          <!-- 操作按钮 -->
          <div class="flex justify-end gap-3 pt-4 border-t border-base-300">
            <button
              type="button"
              class="btn btn-ghost"
              @click="closeDialog"
            >
              取消
            </button>

            <button
              type="submit"
              class="btn btn-romantic"
              :disabled="saving"
            >
              <span v-if="saving" class="loading loading-spinner loading-sm"></span>
              {{ saving ? '保存中...' : '保存' }}
            </button>
          </div>
        </form>
      </div>

      <!-- 点击背景关闭 -->
      <div class="modal-backdrop" @click="closeDialog"></div>
    </div>
  </div>
</template>

<script>
import Pagination from '@/components/common/Pagination.vue'
import Icon from '@/components/Icon.vue'
import { gitConfigApi } from '@/api/gitConfig.js'

export default {
  name: 'GitConfigManagement',
  components: {
    Pagination,
    Icon
  },
  data() {
    return {
      loading: false,
      configs: [],
      searchQuery: '',
      statusFilter: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      showDialog: false,
      isEditing: false,
      formData: {
        id: null,
        name: '',
        repository_url: '',
        auth_type: 'none',
        username: '',
        password: '',
        ssh_key: '',
        description: '',
        status: 'active'
      },
      saving: false
    }
  },
  mounted() {
    this.loadConfigs()
  },
  methods: {
    async loadConfigs() {
      this.loading = true
      try {
        const response = await gitConfigApi.getList({
          page: this.currentPage,
          size: this.pageSize,
          search: this.searchQuery,
          status: this.statusFilter
        })

        if (response.success) {
          this.configs = response.data.items || []
          this.total = response.data.total || 0
        } else {
          console.error('加载Git配置失败:', response.message)
          this.configs = []
          this.total = 0
        }
      } catch (error) {
        console.error('加载Git配置出错:', error)
        this.configs = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },
    showCreateDialog() {
      this.isEditing = false
      this.resetFormData()
      this.showDialog = true
    },
    showEditDialog(config) {
      this.isEditing = true
      this.formData = {
        id: config.id,
        name: config.name,
        auth_type: config.auth_type,
        username: config.username,
        password: config.password || '',
        ssh_key: config.ssh_key || '',
        description: config.description || '',
        status: config.status
      }
      this.showDialog = true
    },
    resetFormData() {
      this.formData = {
        id: null,
        name: '',
        auth_type: 'none',
        username: '',
        password: '',
        ssh_key: '',
        description: '',
        status: 'active'
      }
    },
    closeDialog() {
      this.showDialog = false
      this.resetFormData()
    },
    setStatusFilter(filter) {
      this.statusFilter = filter
    },
    handleSearch() {
      // 搜索逻辑
    },
    showBulkActions() {
      // 批量操作逻辑
    },
    refreshConfigs() {
      this.loadConfigs()
    },
    editConfig(config) {
      this.showEditDialog(config)
    },
    changePage(page) {
      this.currentPage = page
      this.loadConfigs()
    },
    viewConfig(config) {
      console.log('查看配置:', config)
    },


    viewLogs(config) {
      console.log('查看日志:', config)
    },
    truncateUrl(url) {
      return url.length > 50 ? url.substring(0, 50) + '...' : url
    },
    getAuthTypeLabel(type) {
      const labels = {
        'none': '无认证',
        'username': '用户名密码',
        'token': 'Token',
        'ssh_key': 'SSH密钥'
      }
      return labels[type] || type
    },
    getTestStatusLabel(result) {
      if (!result) return '⚪ 未测试'
      return result === 'success' ? '✅ 连接正常' : '❌ 连接失败'
    },


    async saveConfig() {
      // 基本验证
      if (!this.formData.name.trim()) {
        alert('请输入认证名称')
        return
      }

      // 根据认证方式验证必填字段
      if (this.formData.auth_type === 'username' && !this.formData.password.trim()) {
        alert('请输入密码')
        return
      }
      if (this.formData.auth_type === 'token' && !this.formData.password.trim()) {
        alert('请输入Token')
        return
      }
      if (this.formData.auth_type === 'ssh_key' && !this.formData.ssh_key.trim()) {
        alert('请输入SSH私钥')
        return
      }

      this.saving = true

      try {
        if (this.isEditing) {
          await gitConfigApi.update(this.formData.id, this.formData)
          console.log('Git配置更新成功')
        } else {
          await gitConfigApi.create(this.formData)
          console.log('Git配置创建成功')
        }

        this.closeDialog()
        this.loadConfigs()
      } catch (error) {
        console.error('保存Git配置失败:', error)
        alert('保存失败: ' + error.message)
      } finally {
        this.saving = false
      }
    },

  }
}
</script>

<style scoped>
/* 配置网格布局 */
.configs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

/* 项目卡片样式 */
.project-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  border: 1px solid #f1f5f9;
  transition: all 0.2s ease;
}

.project-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.project-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(to right, #f472b6, #ec4899);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-text {
  color: white;
  font-weight: 600;
  font-size: 1.2rem;
}

.project-info {
  flex: 1;
  min-width: 0;
}

.project-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.project-description {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
  word-break: break-all;
}

.project-menu {
  flex-shrink: 0;
}

.menu-btn {
  background: none;
  border: none;
  color: #9ca3af;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.menu-btn:hover {
  background: #f3f4f6;
  color: #6b7280;
}



/* Git信息区域 */
.git-info {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.git-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.git-info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
}

.info-value {
  font-size: 0.75rem;
  color: #1f2937;
  font-weight: 500;
}



/* 项目描述文本 */
.project-description-text {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
}

/* 操作按钮 */
.card-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.action-btn.primary {
  width: 100%;
  background: linear-gradient(to right, #f472b6, #ec4899);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.primary:hover {
  background: linear-gradient(to right, #ec4899, #db2777);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(236, 72, 153, 0.3);
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.action-btn.secondary {
  flex: 1;
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.secondary:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #475569;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  grid-column: 1 / -1; /* 占满整个网格 */
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.6;
}

.empty-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.empty-description {
  font-size: 16px;
  color: #666;
  margin-bottom: 32px;
  max-width: 400px;
  line-height: 1.5;
}

.empty-action-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.empty-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* 输入框样式增强 */
.input-romantic {
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background-color: transparent;
  backdrop-filter: blur(2px);
  height: 3rem;
  padding: 0.75rem 1rem;
}

.input-romantic:focus {
  border-color: rgba(0, 0, 0, 0.2);
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
  background-color: rgba(255, 255, 255, 0.1);
}

.input-romantic:hover {
  border-color: rgba(0, 0, 0, 0.15);
  background-color: rgba(255, 255, 255, 0.05);
}

.input-romantic::placeholder {
  color: #9ca3af;
  font-style: italic;
}

/* 文本域样式增强 */
.textarea-romantic {
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background-color: transparent;
  backdrop-filter: blur(2px);
  resize: vertical;
  min-height: 5rem;
  padding: 0.75rem 1rem;
}

.textarea-romantic:focus {
  border-color: rgba(0, 0, 0, 0.2);
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
  background-color: rgba(255, 255, 255, 0.1);
}

.textarea-romantic:hover {
  border-color: rgba(0, 0, 0, 0.15);
  background-color: rgba(255, 255, 255, 0.05);
}

.textarea-romantic::placeholder {
  color: #9ca3af;
  font-style: italic;
}

/* Radio按钮样式增强 */
.radio-romantic {
  border-color: #f472b6;
  background-color: white;
}

.radio-romantic:checked {
  background-color: #f472b6;
  border-color: #f472b6;
  background-image: radial-gradient(circle, white 40%, transparent 50%);
}

.radio-romantic:focus {
  box-shadow: 0 0 0 3px rgba(244, 114, 182, 0.2);
}

/* 浪漫按钮样式 */
.btn-romantic {
  background: linear-gradient(to right, #f472b6, #a855f7);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.btn-romantic:hover {
  background: linear-gradient(to right, #ec4899, #9333ea);
  transform: scale(1.02);
  box-shadow: 0 4px 6px -1px rgba(244, 114, 182, 0.3);
}

/* 模态框背景优化 */
.modal-box {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(253, 242, 248, 0.95) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(244, 114, 182, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .configs-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-btn.secondary {
    flex: none;
  }

  .empty-state {
    padding: 60px 20px;
  }

  .empty-icon {
    font-size: 48px;
  }

  .empty-title {
    font-size: 20px;
  }

  .empty-description {
    font-size: 14px;
  }
}
</style>
