<template>
  <div class="scroll-test">
    <div class="header">
      <h1>滚动测试页面</h1>
      <p>这个页面用于测试垂直滚动是否正常工作</p>
    </div>
    
    <div class="content">
      <div v-for="i in 50" :key="i" class="test-item">
        <h3>测试项目 {{ i }}</h3>
        <p>
          这是第 {{ i }} 个测试项目。如果页面滚动正常，您应该能够使用鼠标滚轮或滚动条
          来查看所有50个测试项目。每个项目都有足够的内容来测试滚动功能是否正常工作。
        </p>
        <div class="item-details">
          <span class="tag">项目编号: {{ i }}</span>
          <span class="tag">状态: 正常</span>
          <span class="tag">类型: 测试</span>
        </div>
      </div>
    </div>
    
    <div class="footer">
      <p>如果您能看到这个底部内容，说明滚动功能正常！</p>
    </div>
  </div>
</template>

<script setup>
// 无需特殊逻辑，纯测试页面
</script>

<style scoped>
.scroll-test {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 200vh; /* 确保页面足够高以产生滚动 */
}

.header {
  text-align: center;
  color: white;
  margin-bottom: 40px;
}

.header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.content {
  max-width: 800px;
  margin: 0 auto;
}

.test-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.test-item h3 {
  color: #2d3748;
  margin-bottom: 10px;
  font-size: 1.3rem;
}

.test-item p {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 15px;
}

.item-details {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.tag {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.footer {
  text-align: center;
  color: white;
  margin-top: 40px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.footer p {
  font-size: 1.2rem;
  font-weight: 600;
}
</style>
