<template>
  <div class="workflow-project-design">
    <!-- 项目信息头部 -->
    <div class="project-header">
      <div class="project-info">
        <div class="project-title">
          <h1>{{ project?.name || '工作流设计' }}</h1>
          <div class="project-meta" v-if="project">
            <span class="tag" :class="`tag-${project.language}`">
              {{ getLanguageIcon(project.language) }} {{ getLanguageLabel(project.language) }}
            </span>
            <span class="tag" :class="`tag-${project.environment}`">
              {{ getEnvironmentIcon(project.environment) }} {{ getEnvironmentLabel(project.environment) }}
            </span>
            <span class="tag" :class="`tag-${project.project_type}`">
              {{ getProjectTypeIcon(project.project_type) }} {{ getProjectTypeLabel(project.project_type) }}
            </span>
          </div>
        </div>
        <p class="project-description" v-if="project?.description">
          {{ project.description }}
        </p>
      </div>
      
      <div class="header-actions">
        <button class="btn-secondary" @click="goBack">
          <span class="icon">←</span>
          返回项目
        </button>
        <button class="btn-primary" @click="saveWorkflow" :disabled="saving">
          <span v-if="saving" class="spinner"></span>
          <span class="icon" v-else>💾</span>
          保存工作流
        </button>
      </div>
    </div>

    <!-- 工作流设计器 -->
    <div class="workflow-designer">
      <VueFlowWorkflow 
        ref="workflowRef"
        :project-context="project"
        @save="handleSaveWorkflow"
        @change="handleWorkflowChange"
      />
    </div>

    <!-- 保存状态提示 -->
    <div v-if="saveStatus" class="save-status" :class="saveStatus.type">
      <span class="status-icon">
        {{ saveStatus.type === 'success' ? '✅' : '❌' }}
      </span>
      {{ saveStatus.message }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import VueFlowWorkflow from '@/components/workflow/vueflow/VueFlowWorkflow.vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const project = ref(null)
const workflowRef = ref(null)
const saving = ref(false)
const saveStatus = ref(null)
const hasUnsavedChanges = ref(false)

// 方法
const loadProject = async () => {
  const projectId = route.params.id
  try {
    // TODO: 调用API获取项目详情
    // 模拟数据
    project.value = {
      id: projectId,
      name: 'Go API 服务',
      language: 'go',
      project_type: 'backend',
      environment: 'production',
      description: '用户管理API服务，提供用户注册、登录、权限管理等功能'
    }

    // 设置工作流项目上下文
    if (workflowRef.value) {
      workflowRef.value.setProjectContext(project.value)
    }

    // TODO: 加载现有的工作流配置
    await loadWorkflowConfig()
  } catch (error) {
    console.error('加载项目失败:', error)
    showSaveStatus('error', '加载项目失败')
  }
}

const loadWorkflowConfig = async () => {
  try {
    // TODO: 调用API获取工作流配置
    // 模拟已有的工作流配置
    const workflowConfig = {
      nodes: [
        {
          id: '1',
          type: 'start',
          position: { x: 100, y: 100 },
          data: { label: '开始' }
        },
        {
          id: '2',
          type: 'git_clone',
          position: { x: 300, y: 100 },
          data: { 
            label: 'Git克隆',
            config: {
              repository: 'https://github.com/user/repo.git',
              branch: 'main'
            }
          }
        },
        {
          id: '3',
          type: 'go_build',
          position: { x: 500, y: 100 },
          data: { 
            label: 'Go构建',
            config: {
              output_name: 'app',
              target_os: 'linux',
              target_arch: 'amd64'
            }
          }
        }
      ],
      edges: [
        {
          id: 'e1-2',
          source: '1',
          target: '2'
        },
        {
          id: 'e2-3',
          source: '2',
          target: '3'
        }
      ]
    }

    if (workflowRef.value) {
      workflowRef.value.loadWorkflow(workflowConfig)
    }
  } catch (error) {
    console.error('加载工作流配置失败:', error)
  }
}

const handleWorkflowChange = () => {
  hasUnsavedChanges.value = true
}

const handleSaveWorkflow = async (workflowConfig) => {
  await saveWorkflow(workflowConfig)
}

const saveWorkflow = async (workflowConfig = null) => {
  if (!workflowConfig && workflowRef.value) {
    workflowConfig = workflowRef.value.getWorkflowConfig()
  }

  if (!workflowConfig) {
    showSaveStatus('error', '无法获取工作流配置')
    return
  }

  saving.value = true
  try {
    // TODO: 调用API保存工作流配置
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用

    console.log('保存工作流配置:', workflowConfig)
    
    hasUnsavedChanges.value = false
    showSaveStatus('success', '工作流保存成功')
  } catch (error) {
    console.error('保存工作流失败:', error)
    showSaveStatus('error', '保存工作流失败')
  } finally {
    saving.value = false
  }
}

const showSaveStatus = (type, message) => {
  saveStatus.value = { type, message }
  setTimeout(() => {
    saveStatus.value = null
  }, 3000)
}

const goBack = () => {
  if (hasUnsavedChanges.value) {
    if (confirm('有未保存的更改，确定要离开吗？')) {
      router.push('/workflow-projects')
    }
  } else {
    router.push('/workflow-projects')
  }
}

// 页面离开前确认
const handleBeforeUnload = (event) => {
  if (hasUnsavedChanges.value) {
    event.preventDefault()
    event.returnValue = '有未保存的更改，确定要离开吗？'
  }
}

// 辅助函数
const getLanguageIcon = (language) => {
  const icons = {
    go: '🔨', python: '🐍', vue: '⚡', java: '☕', nodejs: '📦', react: '⚛️'
  }
  return icons[language] || '💻'
}

const getLanguageLabel = (language) => {
  const labels = {
    go: 'Go', python: 'Python', vue: 'Vue.js', java: 'Java', nodejs: 'Node.js', react: 'React'
  }
  return labels[language] || language
}

const getEnvironmentIcon = (environment) => {
  const icons = {
    development: '🔧', test: '🧪', production: '🚀'
  }
  return icons[environment] || '🌍'
}

const getEnvironmentLabel = (environment) => {
  const labels = {
    development: '开发环境', test: '测试环境', production: '生产环境'
  }
  return labels[environment] || environment
}

const getProjectTypeIcon = (projectType) => {
  const icons = {
    frontend: '🎨', backend: '⚙️', fullstack: '🔄'
  }
  return icons[projectType] || '📦'
}

const getProjectTypeLabel = (projectType) => {
  const labels = {
    frontend: '前端项目', backend: '后端项目', fullstack: '全栈项目'
  }
  return labels[projectType] || projectType
}

// 生命周期
onMounted(() => {
  loadProject()
  window.addEventListener('beforeunload', handleBeforeUnload)
})

onBeforeUnmount(() => {
  window.removeEventListener('beforeunload', handleBeforeUnload)
})
</script>

<style scoped>
.workflow-project-design {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
}

.project-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-shrink: 0;
}

.project-info {
  flex: 1;
}

.project-title {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}

.project-title h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.project-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.tag-go { background: #dbeafe; color: #1e40af; }
.tag-python { background: #fef3c7; color: #92400e; }
.tag-vue { background: #d1fae5; color: #065f46; }
.tag-development { background: #f3f4f6; color: #374151; }
.tag-test { background: #fef3c7; color: #92400e; }
.tag-production { background: #fee2e2; color: #991b1b; }
.tag-frontend { background: #ede9fe; color: #5b21b6; }
.tag-backend { background: #dbeafe; color: #1e40af; }
.tag-fullstack { background: #d1fae5; color: #065f46; }

.project-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.workflow-designer {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.save-status {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

.save-status.success {
  background: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.save-status.error {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

.btn-primary, .btn-secondary {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.spinner {
  width: 14px;
  height: 14px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
</style>
