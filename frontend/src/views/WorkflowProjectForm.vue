<template>
  <div class="workflow-project-form">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">{{ isEdit ? '编辑项目' : '创建项目' }}</h1>
        <p class="page-description">{{ isEdit ? '修改项目基础信息' : '创建新的工作流项目' }}</p>
      </div>
      <button class="btn-secondary" @click="goBack">
        <span class="icon">←</span>
        返回
      </button>
    </div>

    <!-- 表单内容 -->
    <div class="form-container">
      <form @submit.prevent="handleSubmit" class="project-form">
        <!-- 项目名称 -->
        <div class="form-group">
          <label for="name" class="form-label">
            项目名称 <span class="required">*</span>
          </label>
          <input
            id="name"
            type="text"
            v-model="form.name"
            class="form-input"
            :class="{ 'error': errors.name }"
            placeholder="请输入项目名称"
            maxlength="255"
          />
          <div v-if="errors.name" class="error-message">{{ errors.name }}</div>
          <div class="form-hint">项目名称将用于标识您的工作流项目</div>
        </div>

        <!-- 编程语言 -->
        <div class="form-group">
          <label for="language" class="form-label">
            编程语言 <span class="required">*</span>
          </label>
          <select
            id="language"
            v-model="form.language"
            class="form-select"
            :class="{ 'error': errors.language }"
            @change="onLanguageChange"
          >
            <option value="">请选择编程语言</option>
            <option value="go">🔨 Go</option>
            <option value="python">🐍 Python</option>
            <option value="vue">⚡ Vue.js</option>
            <option value="java">☕ Java</option>
            <option value="nodejs">📦 Node.js</option>
            <option value="react">⚛️ React</option>
          </select>
          <div v-if="errors.language" class="error-message">{{ errors.language }}</div>
          <div class="form-hint">选择项目使用的主要编程语言，将影响可用的工作流节点</div>
        </div>

        <!-- 项目类型 -->
        <div class="form-group">
          <label for="projectType" class="form-label">
            项目类型 <span class="required">*</span>
          </label>
          <div class="radio-group">
            <label class="radio-option">
              <input
                type="radio"
                name="projectType"
                value="frontend"
                v-model="form.projectType"
              />
              <span class="radio-label">
                <span class="radio-icon">🎨</span>
                <span class="radio-text">
                  <strong>前端项目</strong>
                  <small>用户界面和交互逻辑</small>
                </span>
              </span>
            </label>
            <label class="radio-option">
              <input
                type="radio"
                name="projectType"
                value="backend"
                v-model="form.projectType"
              />
              <span class="radio-label">
                <span class="radio-icon">⚙️</span>
                <span class="radio-text">
                  <strong>后端项目</strong>
                  <small>服务端逻辑和API</small>
                </span>
              </span>
            </label>
            <label class="radio-option">
              <input
                type="radio"
                name="projectType"
                value="fullstack"
                v-model="form.projectType"
              />
              <span class="radio-label">
                <span class="radio-icon">🔄</span>
                <span class="radio-text">
                  <strong>全栈项目</strong>
                  <small>前后端一体化项目</small>
                </span>
              </span>
            </label>
          </div>
          <div v-if="errors.projectType" class="error-message">{{ errors.projectType }}</div>
        </div>

        <!-- 部署环境 -->
        <div class="form-group">
          <label for="environment" class="form-label">
            部署环境 <span class="required">*</span>
          </label>
          <select
            id="environment"
            v-model="form.environment"
            class="form-select"
            :class="{ 'error': errors.environment }"
          >
            <option value="">请选择部署环境</option>
            <option value="development">🔧 开发环境</option>
            <option value="test">🧪 测试环境</option>
            <option value="production">🚀 生产环境</option>
          </select>
          <div v-if="errors.environment" class="error-message">{{ errors.environment }}</div>
          <div class="form-hint">选择项目的主要部署环境</div>
        </div>

        <!-- 项目描述 -->
        <div class="form-group">
          <label for="description" class="form-label">项目描述</label>
          <textarea
            id="description"
            v-model="form.description"
            class="form-textarea"
            placeholder="请输入项目描述（可选）"
            rows="4"
            maxlength="1000"
          ></textarea>
          <div class="form-hint">
            简要描述项目的功能和用途 ({{ form.description.length }}/1000)
          </div>
        </div>

        <!-- 表单操作 -->
        <div class="form-actions">
          <button type="button" class="btn-secondary" @click="goBack">
            取消
          </button>
          <button type="submit" class="btn-primary" :disabled="loading">
            <span v-if="loading" class="spinner"></span>
            <span class="icon" v-else>{{ isEdit ? '💾' : '➕' }}</span>
            {{ isEdit ? '保存修改' : '创建项目' }}
          </button>
        </div>
      </form>

      <!-- 预览卡片 -->
      <div class="preview-section">
        <h3>项目预览</h3>
        <div class="project-preview-card">
          <div class="preview-header">
            <h4>{{ form.name || '项目名称' }}</h4>
          </div>
          <div class="preview-meta">
            <span v-if="form.language" class="preview-tag">
              {{ getLanguageIcon(form.language) }} {{ getLanguageLabel(form.language) }}
            </span>
            <span v-if="form.environment" class="preview-tag">
              {{ getEnvironmentIcon(form.environment) }} {{ getEnvironmentLabel(form.environment) }}
            </span>
            <span v-if="form.projectType" class="preview-tag">
              {{ getProjectTypeIcon(form.projectType) }} {{ getProjectTypeLabel(form.projectType) }}
            </span>
          </div>
          <p class="preview-description">
            {{ form.description || '暂无描述' }}
          </p>
          <div class="preview-actions">
            <button class="btn-preview" disabled>
              <span class="icon">🎨</span>
              流程设计
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const isEdit = computed(() => !!route.params.id)

const form = reactive({
  name: '',
  language: '',
  projectType: '',
  environment: '',
  description: ''
})

const errors = reactive({
  name: '',
  language: '',
  projectType: '',
  environment: ''
})

// 方法
const validateForm = () => {
  // 清空错误
  Object.keys(errors).forEach(key => {
    errors[key] = ''
  })

  let isValid = true

  if (!form.name.trim()) {
    errors.name = '项目名称不能为空'
    isValid = false
  } else if (form.name.length > 255) {
    errors.name = '项目名称长度不能超过255个字符'
    isValid = false
  }

  if (!form.language) {
    errors.language = '请选择编程语言'
    isValid = false
  }

  if (!form.projectType) {
    errors.projectType = '请选择项目类型'
    isValid = false
  }

  if (!form.environment) {
    errors.environment = '请选择部署环境'
    isValid = false
  }

  return isValid
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  loading.value = true
  try {
    // TODO: 调用API创建或更新项目
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用

    if (isEdit.value) {
      console.log('更新项目:', form)
    } else {
      console.log('创建项目:', form)
    }

    // 跳转到项目列表
    router.push('/workflow-projects')
  } catch (error) {
    console.error('保存项目失败:', error)
    alert('保存失败，请重试')
  } finally {
    loading.value = false
  }
}

const onLanguageChange = () => {
  // 根据语言自动推荐项目类型
  const languageTypeMap = {
    'go': 'backend',
    'python': 'backend',
    'vue': 'frontend',
    'react': 'frontend',
    'nodejs': 'fullstack',
    'java': 'backend'
  }

  if (!form.projectType && languageTypeMap[form.language]) {
    form.projectType = languageTypeMap[form.language]
  }
}

const goBack = () => {
  router.push('/workflow-projects')
}

const loadProject = async () => {
  if (!isEdit.value) return

  const projectId = route.params.id
  try {
    // TODO: 调用API获取项目详情
    // 模拟数据
    const project = {
      id: projectId,
      name: 'Go API 服务',
      language: 'go',
      project_type: 'backend',
      environment: 'production',
      description: '用户管理API服务'
    }

    Object.assign(form, {
      name: project.name,
      language: project.language,
      projectType: project.project_type,
      environment: project.environment,
      description: project.description
    })
  } catch (error) {
    console.error('加载项目失败:', error)
    alert('加载项目失败')
    goBack()
  }
}

// 辅助函数
const getLanguageIcon = (language) => {
  const icons = {
    go: '🔨', python: '🐍', vue: '⚡', java: '☕', nodejs: '📦', react: '⚛️'
  }
  return icons[language] || '💻'
}

const getLanguageLabel = (language) => {
  const labels = {
    go: 'Go', python: 'Python', vue: 'Vue.js', java: 'Java', nodejs: 'Node.js', react: 'React'
  }
  return labels[language] || language
}

const getEnvironmentIcon = (environment) => {
  const icons = {
    development: '🔧', test: '🧪', production: '🚀'
  }
  return icons[environment] || '🌍'
}

const getEnvironmentLabel = (environment) => {
  const labels = {
    development: '开发环境', test: '测试环境', production: '生产环境'
  }
  return labels[environment] || environment
}

const getProjectTypeIcon = (projectType) => {
  const icons = {
    frontend: '🎨', backend: '⚙️', fullstack: '🔄'
  }
  return icons[projectType] || '📦'
}

const getProjectTypeLabel = (projectType) => {
  const labels = {
    frontend: '前端项目', backend: '后端项目', fullstack: '全栈项目'
  }
  return labels[projectType] || projectType
}

// 生命周期
onMounted(() => {
  if (isEdit.value) {
    loadProject()
  }
})
</script>

<style scoped>
.workflow-project-form {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
}

.header-content p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

.form-container {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 32px;
}

.project-form {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.required {
  color: #ef4444;
}

.form-input, .form-select, .form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error, .form-select.error {
  border-color: #ef4444;
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.radio-option {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.radio-option:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.radio-option input[type="radio"] {
  margin-top: 2px;
}

.radio-label {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
}

.radio-icon {
  font-size: 20px;
}

.radio-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.radio-text strong {
  font-size: 14px;
  color: #1f2937;
}

.radio-text small {
  font-size: 12px;
  color: #6b7280;
}

.error-message {
  margin-top: 4px;
  font-size: 12px;
  color: #ef4444;
}

.form-hint {
  margin-top: 4px;
  font-size: 12px;
  color: #6b7280;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.preview-section {
  position: sticky;
  top: 24px;
}

.preview-section h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.project-preview-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
}

.preview-header h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.preview-meta {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
}

.preview-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background: #f3f4f6;
  color: #374151;
  width: fit-content;
}

.preview-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.btn-primary, .btn-secondary, .btn-preview {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-preview {
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #d1d5db;
  cursor: not-allowed;
  opacity: 0.6;
}

.spinner {
  width: 14px;
  height: 14px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .form-container {
    grid-template-columns: 1fr;
  }
  
  .preview-section {
    position: static;
  }
}
</style>
