@import "tailwindcss";

/* 🌸 浪漫主题配置 */
@plugin "daisyui" {
  themes: romantic --default, light, dark;
  root: ":root";
  logs: true;
}

/* 🚫 防止水平溢出的全局规则 */
* {
  box-sizing: border-box;
}

*,
*::before,
*::after {
  max-width: 100%;
}

/* 🎨 自定义浪漫主题 */
@plugin "daisyui/theme" {
  name: "romantic";
  color-scheme: light;

  /* 主色调 - 温暖粉紫渐变 */
  --color-primary: #ec4899;
  --color-primary-content: #ffffff;
  --color-secondary: #a855f7;
  --color-secondary-content: #ffffff;
  --color-accent: #3b82f6;
  --color-accent-content: #ffffff;

  /* 中性色 */
  --color-neutral: #6b7280;
  --color-neutral-content: #ffffff;
  --color-base-100: #ffffff;
  --color-base-200: #fdf2f8;
  --color-base-300: #fce7f3;
  --color-base-content: #374151;

  /* 状态色 */
  --color-info: #06b6d4;
  --color-info-content: #ffffff;
  --color-success: #10b981;
  --color-success-content: #ffffff;
  --color-warning: #f59e0b;
  --color-warning-content: #ffffff;
  --color-error: #ef4444;
  --color-error-content: #ffffff;
}

/* 🌈 自定义设计Token */
@theme {
  /* 浪漫色彩扩展 */
  --color-romantic-50: #fdf2f8;
  --color-romantic-100: #fce7f3;
  --color-romantic-200: #fbcfe8;
  --color-romantic-300: #f9a8d4;
  --color-romantic-400: #f472b6;
  --color-romantic-500: #ec4899;
  --color-romantic-600: #db2777;
  --color-romantic-700: #be185d;
  --color-romantic-800: #9d174d;
  --color-romantic-900: #831843;

  /* 梦幻紫色 */
  --color-dreamy-50: #faf5ff;
  --color-dreamy-100: #f3e8ff;
  --color-dreamy-200: #e9d5ff;
  --color-dreamy-300: #d8b4fe;
  --color-dreamy-400: #c084fc;
  --color-dreamy-500: #a855f7;
  --color-dreamy-600: #9333ea;
  --color-dreamy-700: #7c3aed;
  --color-dreamy-800: #6b21a8;
  --color-dreamy-900: #581c87;

  /* 天空蓝色 */
  --color-sky-50: #f0f9ff;
  --color-sky-100: #e0f2fe;
  --color-sky-200: #bae6fd;
  --color-sky-300: #7dd3fc;
  --color-sky-400: #38bdf8;
  --color-sky-500: #0ea5e9;
  --color-sky-600: #0284c7;
  --color-sky-700: #0369a1;
  --color-sky-800: #075985;
  --color-sky-900: #0c4a6e;

  /* 渐变定义 */
  --gradient-romantic: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  --gradient-dreamy: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  --gradient-sunset: linear-gradient(135deg, #ffecd2 0%, #fcb69f 25%, #ff9a9e 50%, #fecfef 75%, #fecfef 100%);
  --gradient-sky: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  /* 阴影系统 */
  --shadow-romantic: 0 4px 6px -1px rgba(236, 72, 153, 0.1), 0 2px 4px -1px rgba(236, 72, 153, 0.06);
  --shadow-dreamy: 0 10px 15px -3px rgba(168, 85, 247, 0.1), 0 4px 6px -2px rgba(168, 85, 247, 0.05);
  --shadow-soft: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* 字体系统 */
  --font-romantic: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-display: "Poppins", "Inter", sans-serif;
}

/* 🌸 浪漫风格全局样式 */
html {
  overflow-x: hidden;
  overflow-y: auto;
  width: 100%;
  max-width: 100%;
}

body {
  margin: 0;
  font-family: var(--font-romantic);
  background: linear-gradient(to bottom right, #fdf2f8, #f3e8ff, #dbeafe);
  color: #374151;
  /* 移除 min-height: 100vh，让内容自然决定高度 */
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  box-sizing: border-box;
}

#app {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
  text-align: left;
  /* 移除 min-height: 100vh，让内容自然决定高度 */
  background: linear-gradient(to bottom right, #fdf2f8, #f3e8ff, #dbeafe);
  overflow-x: hidden;
  overflow-y: auto;
  box-sizing: border-box;
}

/* 自定义样式 */

/* 🌸 主要内容区域 - 浪漫风格 */
.main-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.5rem;
  /* 移除 min-height 限制，让内容自然决定高度 */
}

.action-stats {
  display: flex;
  align-items: stretch;
  gap: 1.25rem;
  width: 100%;
  margin-bottom: 1.5rem;
}

.project-stats {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: var(--shadow-romantic);
  display: flex;
  gap: 3rem;
  flex: 1;
  align-items: center;
  transition: all 0.3s ease;
}

.project-stats:hover {
  box-shadow: var(--shadow-dreamy);
  transform: translateY(-2px);
}

.stat-item {
  flex: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1f2937;
  line-height: 1;
}

/* 表格样式 */
.table-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-sidebar {
    display: none;
  }

  .main-container {
    margin-left: 0;
  }
}

/* 加载动画 */
.page-loading {
  background: linear-gradient(135deg, #803AFF 0%, #5D25FF 100%);
  color: #fff;
}

.loading-text {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 500;
}

.loading-text:after {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #fff;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 表格容器 */
.table-container {
  display: flex;
  gap: 24px;
  flex: 1;
  min-height: 0;
}

.table-wrapper {
  flex: 1;
  min-width: 0;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.04);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  /* 移除固定高度限制，让内容自然决定高度 */
  position: relative;
}

.table-content {
  flex: 1;
  overflow: auto;
  height: calc(100% - 56px);
}

.pagination-container {
  height: 56px;
  padding: 12px 24px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  gap: 8px;
  box-sizing: border-box;
}

/* 日志面板 */
.log-panel {
  width: 400px;
  background: #fff;
  box-shadow: 0 2px 12px rgba(0,0,0,0.04);
  border-radius: 8px;
  display: none;
  flex-direction: column;
  /* 移除固定高度限制，让内容自然决定高度 */
}

.log-panel.show {
  display: flex;
}

.log-panel-header {
  padding: 16px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  flex-shrink: 0;
  border-radius: 8px 8px 0 0;
}

.log-panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.log-panel-content {
  flex: 1;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.deploy-logs {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #1e1e1e;
  color: #fff;
  font-family: 'Courier New', Courier, monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
  border-radius: 0 0 8px 8px;
}

.empty-logs {
  text-align: center;
  color: #666;
  padding: 20px;
}

.log-item {
  margin-bottom: 8px;
  white-space: pre-wrap;
  word-break: break-all;
  width: 100%;
}

/* 侧边栏控制 */
.collapse-btn {
  position: absolute;
  bottom: 30px;
  left: 0;
  width: 100%;
  text-align: center;
  cursor: pointer;
  color: #fff;
  font-size: 20px;
  transition: color 0.2s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}
.collapse-btn:hover {
  color: #FFD700;
}

/* 动画效果 */
.sidebar-menu-fade-enter-active,
.sidebar-menu-fade-leave-active {
  transition: opacity 0.2s;
}
.sidebar-menu-fade-enter-from,
.sidebar-menu-fade-leave-to {
  opacity: 0;
}

.menu-text {
  display: inline-block;
  max-width: 120px;
  opacity: 1;
  transition: max-width 0.2s, opacity 0.2s;
  vertical-align: middle;
  white-space: nowrap;
}
.menu-text.collapse {
  max-width: 0;
  opacity: 0;
  overflow: hidden;
}

/* 链接样式 */
.project-name-link {
  color: #3b82f6;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s;
}
.project-name-link:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

/* 🎨 浪漫风格工具类 */

/* 渐变背景 */
.bg-romantic-gradient {
  background: var(--gradient-romantic);
}

.bg-dreamy-gradient {
  background: var(--gradient-dreamy);
}

.bg-sunset-gradient {
  background: var(--gradient-sunset);
}

.bg-sky-gradient {
  background: var(--gradient-sky);
}

/* 玻璃态效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.glass-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 磨砂玻璃背景 - 用于弹窗背景 */
.glass-backdrop {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  animation: fadeIn 0.3s ease-out;
}

/* DaisyUI Modal 磨砂玻璃背景覆盖 */
.modal {
  background: rgba(255, 255, 255, 0.15) !important;
  backdrop-filter: blur(16px) !important;
  -webkit-backdrop-filter: blur(16px) !important;
}

/* 防止滚动穿透 */
.modal-no-scroll {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  height: 100% !important;
}

/* 弹窗滚动容器 */
.modal-scroll-container {
  overflow-y: auto;
  overscroll-behavior: contain;
  -webkit-overflow-scrolling: touch;
}

/* 确保弹窗内容的滚动行为 */
.modal-box, .project-modal-box {
  overscroll-behavior: contain;
  -webkit-overflow-scrolling: touch;
}

/* 防止背景滚动 */
.glass-backdrop {
  overscroll-behavior: contain;
}

/* 浪漫阴影 */
.shadow-romantic {
  box-shadow: var(--shadow-romantic);
}

.shadow-dreamy {
  box-shadow: var(--shadow-dreamy);
}

.shadow-soft {
  box-shadow: var(--shadow-soft);
}

.shadow-medium {
  box-shadow: var(--shadow-medium);
}

.shadow-large {
  box-shadow: var(--shadow-large);
}

/* 卡片样式 */
.card-romantic {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(236, 72, 153, 0.1);
  border-radius: 1rem;
  box-shadow: var(--shadow-romantic);
  transition: all 0.3s ease;
}

.card-romantic:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-dreamy);
}

.card-dreamy {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(168, 85, 247, 0.1);
  border-radius: 1rem;
  box-shadow: var(--shadow-dreamy);
  transition: all 0.3s ease;
}

.card-dreamy:hover {
  transform: scale(1.02);
}

/* 按钮样式增强 */
.btn-romantic {
  background: linear-gradient(to right, #f472b6, #ec4899);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.btn-romantic:hover {
  background: linear-gradient(to right, #ec4899, #db2777);
  transform: scale(1.05);
  box-shadow: var(--shadow-romantic);
}

.btn-dreamy {
  background: linear-gradient(to right, #c084fc, #a855f7);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.btn-dreamy:hover {
  background: linear-gradient(to right, #a855f7, #9333ea);
  transform: scale(1.05);
  box-shadow: var(--shadow-dreamy);
}

/* 文本渐变 */
.text-romantic-gradient {
  background: linear-gradient(to right, #ec4899, #9333ea);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.text-dreamy-gradient {
  background: linear-gradient(to right, #a855f7, #0ea5e9);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* 动画类 */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  from { box-shadow: 0 0 5px rgba(236, 72, 153, 0.2); }
  to { box-shadow: 0 0 20px rgba(236, 72, 153, 0.4); }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
    -webkit-backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
  }
}

/* 响应式网格 */
.grid-romantic {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-dashboard {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

/* 🌸 导航和布局样式 */
.navbar-romantic {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid rgba(236, 72, 153, 0.1);
  box-shadow: var(--shadow-soft);
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.navbar-logo {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 1rem;
  background: linear-gradient(to right, #f472b6, #c084fc);
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  font-size: 1.25rem;
  font-weight: 700;
  background: linear-gradient(to right, #ec4899, #9333ea);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.breadcrumb-romantic {
  font-size: 0.875rem;
}

.breadcrumb-romantic a {
  color: #9333ea;
  transition: color 0.2s ease;
}

.breadcrumb-romantic a:hover {
  color: #ec4899;
}

/* 🎯 仪表板卡片布局 */
.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.dashboard-card {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(236, 72, 153, 0.1);
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-romantic);
}

.dashboard-card:hover {
  box-shadow: var(--shadow-dreamy);
  transform: translateY(-2px);
}

.dashboard-card-icon {
  padding: 1rem;
  border-radius: 1rem;
  width: fit-content;
  margin-bottom: 1rem;
  transition: transform 0.3s ease;
}

.dashboard-card:hover .dashboard-card-icon {
  transform: scale(1.1);
}

.dashboard-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.dashboard-card-description {
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.dashboard-card-stats {
  background: linear-gradient(to right, #fdf2f8, #f3e8ff);
  border-radius: 0.75rem;
  padding: 0.75rem;
  margin-bottom: 1rem;
}

.dashboard-card-actions {
  display: flex;
  justify-content: flex-end;
}

/* 💎 表单和输入组件样式 */
.input-romantic {
  border-color: #fbcfe8;
  transition: all 0.3s ease;
}

.input-romantic:focus {
  border-color: #f472b6;
  outline: none;
  box-shadow: 0 0 0 3px rgba(236, 72, 153, 0.1);
}



.textarea-romantic {
  border-color: #fbcfe8;
  transition: all 0.3s ease;
}

.textarea-romantic:focus {
  border-color: #f472b6;
  outline: none;
  box-shadow: 0 0 0 3px rgba(236, 72, 153, 0.1);
}

.checkbox-romantic {
  accent-color: #ec4899;
}

.radio-romantic {
  accent-color: #ec4899;
}

/* 🌟 模态框和对话框样式 */
.modal-romantic {
  position: fixed;
  inset: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-box-romantic {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(236, 72, 153, 0.1);
  border-radius: 1rem;
  padding: 2rem;
  max-width: 32rem;
  width: 90%;
  box-shadow: var(--shadow-large);
}

.modal-title-romantic {
  font-size: 1.25rem;
  font-weight: 700;
  color: #374151;
  margin-bottom: 1.5rem;
}

.modal-section {
  background-color: #f9fafb;
  border: 1px solid rgba(236, 72, 153, 0.1);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.modal-section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
}

/* 📊 统计卡片样式 */
.stat-card {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  border: none;
  border-radius: 1rem;
  padding: 1rem;
  box-shadow: var(--shadow-romantic);
  transition: all 0.3s ease;
}

.stat-card-gradient {
  background: linear-gradient(135deg, #f472b6, #a855f7);
  color: white;
  box-shadow: var(--shadow-large);
}

.stat-card-icon {
  width: 2rem;
  height: 2rem;
  opacity: 0.8;
}

.stat-card-value {
  font-size: 1.5rem;
  font-weight: 700;
}

.stat-card-label {
  font-size: 0.875rem;
  opacity: 0.9;
}

/* DaisyUI 默认按钮样式 */

/* 素雅的卡片样式 */
.project-stats {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* 图标样式 */
.btn-icon {
  font-size: 20px;
  margin-right: 8px;
  vertical-align: middle;
  color: rgb(192, 192, 192);
}


