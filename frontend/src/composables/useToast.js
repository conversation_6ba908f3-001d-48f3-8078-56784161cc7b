import { ref, nextTick } from 'vue'

// 🌸 全局Toast状态管理
const toasts = ref([])
let toastId = 0

// 🎨 精美浪漫的Toast类型配置
const toastConfig = {
  success: {
    alertClass: 'alert-success',
    emoji: '🌟',
    title: '成功',
    gradient: 'from-emerald-400 to-teal-400',
    shadowColor: 'shadow-emerald-200/50',
    duration: 3000
  },
  error: {
    alertClass: 'alert-error',
    emoji: '💫',
    title: '错误',
    gradient: 'from-rose-400 to-pink-400',
    shadowColor: 'shadow-rose-200/50',
    duration: 5000
  },
  warning: {
    alertClass: 'alert-warning',
    emoji: '✨',
    title: '警告',
    gradient: 'from-amber-400 to-orange-400',
    shadowColor: 'shadow-amber-200/50',
    duration: 4000
  },
  info: {
    alertClass: 'alert-info',
    emoji: '💎',
    title: '提示',
    gradient: 'from-blue-400 to-indigo-400',
    shadowColor: 'shadow-blue-200/50',
    duration: 3000
  }
}

// 🎭 创建精美Toast
const createToast = (type, message, options = {}) => {
  const id = ++toastId
  const config = toastConfig[type] || toastConfig.info

  const toast = {
    id,
    type,
    message,
    title: options.title || config.title,
    emoji: config.emoji,
    alertClass: config.alertClass,
    gradient: config.gradient,
    shadowColor: config.shadowColor,
    duration: options.duration || config.duration,
    closable: options.closable !== false,
    visible: false,
    removing: false
  }

  toasts.value.push(toast)

  // 🎬 下一帧显示动画
  nextTick(() => {
    toast.visible = true
  })

  // ⏰ 自动移除
  if (toast.duration > 0) {
    setTimeout(() => {
      removeToast(id)
    }, toast.duration)
  }

  return id
}

// 🎭 移除Toast动画
const removeToast = (id) => {
  const index = toasts.value.findIndex(toast => toast.id === id)
  if (index > -1) {
    const toast = toasts.value[index]
    toast.removing = true
    toast.visible = false

    // 🎬 等待动画完成后移除
    setTimeout(() => {
      const currentIndex = toasts.value.findIndex(t => t.id === id)
      if (currentIndex > -1) {
        toasts.value.splice(currentIndex, 1)
      }
    }, 400)
  }
}

// 🧹 清除所有Toast
const clearAllToasts = () => {
  toasts.value.forEach(toast => {
    toast.removing = true
    toast.visible = false
  })

  setTimeout(() => {
    toasts.value.length = 0
  }, 400)
}

// Toast方法
export const useToast = () => {
  return {
    toasts,
    
    // 成功提示
    success: (message, options = {}) => {
      return createToast('success', message, options)
    },
    
    // 错误提示
    error: (message, options = {}) => {
      return createToast('error', message, options)
    },
    
    // 警告提示
    warning: (message, options = {}) => {
      return createToast('warning', message, options)
    },
    
    // 信息提示
    info: (message, options = {}) => {
      return createToast('info', message, options)
    },
    
    // 移除指定Toast
    remove: removeToast,
    
    // 清除所有Toast
    clear: clearAllToasts
  }
}

// 全局Toast实例
export const toast = useToast()
