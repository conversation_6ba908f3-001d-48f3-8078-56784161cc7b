import { onMounted, onUnmounted, watch } from 'vue'

/**
 * 防止弹窗滚动穿透的组合函数
 * @param {Ref<boolean>} isVisible - 弹窗是否可见的响应式引用
 * @returns {Object} 包含滚动锁定相关方法的对象
 */
export function useScrollLock(isVisible) {
  let originalBodyStyle = ''
  let originalBodyPosition = ''
  let scrollTop = 0

  // 锁定页面滚动
  const lockScroll = () => {
    // 记录当前滚动位置
    scrollTop = window.pageYOffset || document.documentElement.scrollTop

    // 保存原始样式
    originalBodyStyle = document.body.style.overflow || ''
    originalBodyPosition = document.body.style.position || ''

    // 应用锁定样式
    document.body.style.overflow = 'hidden'
    document.body.style.position = 'fixed'
    document.body.style.top = `-${scrollTop}px`
    document.body.style.width = '100%'
    
    // 添加CSS类
    document.body.classList.add('modal-no-scroll')
  }

  // 解锁页面滚动
  const unlockScroll = () => {
    // 移除CSS类
    document.body.classList.remove('modal-no-scroll')
    
    // 恢复原始样式
    document.body.style.overflow = originalBodyStyle
    document.body.style.position = originalBodyPosition
    document.body.style.top = ''
    document.body.style.width = ''

    // 恢复滚动位置
    window.scrollTo(0, scrollTop)
  }

  // 处理弹窗内滚动事件，防止穿透
  const handleModalScroll = (event) => {
    // 检查是否在弹窗内
    const target = event.target
    const modalBackdrop = target.closest('.glass-backdrop, .modal')

    if (!modalBackdrop) {
      return // 不在弹窗内，正常处理
    }

    // 查找最近的滚动容器
    const scrollContainer = target.closest('.modal-scroll-container, .modal-box, .project-modal-box')

    if (!scrollContainer) {
      // 如果不在滚动容器内，阻止默认行为
      event.preventDefault()
      return false
    }

    // 检查是否到达滚动边界
    const { scrollTop, scrollHeight, clientHeight } = scrollContainer
    const isAtTop = scrollTop === 0
    const isAtBottom = Math.abs(scrollTop + clientHeight - scrollHeight) < 1

    // 获取滚动方向
    const deltaY = event.deltaY || (event.touches && event.touches[0] ?
      event.touches[0].clientY - (event.touches[0].previousY || event.touches[0].clientY) : 0)

    // 如果在顶部向上滚动或在底部向下滚动，阻止事件穿透
    if ((isAtTop && deltaY < 0) || (isAtBottom && deltaY > 0)) {
      event.preventDefault()
      return false
    }
  }

  // 监听弹窗可见性变化
  watch(isVisible, (visible) => {
    if (visible) {
      lockScroll()
      // 添加滚轮事件监听
      document.addEventListener('wheel', handleModalScroll, { passive: false })
      document.addEventListener('touchmove', handleModalScroll, { passive: false })
    } else {
      unlockScroll()
      // 移除滚轮事件监听
      document.removeEventListener('wheel', handleModalScroll)
      document.removeEventListener('touchmove', handleModalScroll)
    }
  }, { immediate: true })

  // 组件卸载时清理
  onUnmounted(() => {
    if (isVisible.value) {
      unlockScroll()
      document.removeEventListener('wheel', handleModalScroll)
      document.removeEventListener('touchmove', handleModalScroll)
    }
  })

  return {
    lockScroll,
    unlockScroll,
    handleModalScroll
  }
}
