import { ref, nextTick } from 'vue'

// 🌸 全局确认对话框状态
const confirmState = ref({
  visible: false,
  title: '',
  message: '',
  warning: '',
  confirmText: '确认',
  cancelText: '取消',
  resolve: null,
  reject: null
})

// 🎭 显示确认对话框
const showConfirm = (options) => {
  return new Promise((resolve, reject) => {
    confirmState.value = {
      visible: true,
      title: options.title || '确认操作',
      message: options.message || '您确定要执行此操作吗？',
      warning: options.warning || '',
      confirmText: options.confirmText || '确认',
      cancelText: options.cancelText || '取消',
      resolve,
      reject
    }
  })
}

// ✅ 确认操作
const handleConfirm = async () => {
  if (confirmState.value.resolve) {
    confirmState.value.resolve(true)
  }
  hideConfirm()
}

// ❌ 取消操作
const handleCancel = () => {
  if (confirmState.value.reject) {
    confirmState.value.reject(false)
  }
  hideConfirm()
}

// 🫥 隐藏对话框
const hideConfirm = () => {
  confirmState.value.visible = false
  // 清理状态
  setTimeout(() => {
    confirmState.value = {
      visible: false,
      title: '',
      message: '',
      warning: '',
      confirmText: '确认',
      cancelText: '取消',
      resolve: null,
      reject: null
    }
  }, 300)
}

// 🎯 预设的确认对话框类型
const confirmDelete = (itemName, itemType = '项目') => {
  return showConfirm({
    title: `删除${itemType}`,
    message: `确定要删除${itemType} "${itemName}" 吗？`,
    warning: '此操作不可恢复，请谨慎操作！',
    confirmText: '删除',
    cancelText: '取消'
  })
}

const confirmClear = (description = '所有数据') => {
  return showConfirm({
    title: '清空确认',
    message: `确定要清空${description}吗？`,
    warning: '清空后数据将无法恢复！',
    confirmText: '清空',
    cancelText: '取消'
  })
}

const confirmLogout = () => {
  return showConfirm({
    title: '退出登录',
    message: '确定要退出当前账户吗？',
    warning: '退出后需要重新登录才能继续使用',
    confirmText: '退出',
    cancelText: '取消'
  })
}

const confirmReset = (target = '设置') => {
  return showConfirm({
    title: `重置${target}`,
    message: `确定要重置${target}到默认状态吗？`,
    warning: '重置后当前配置将丢失！',
    confirmText: '重置',
    cancelText: '取消'
  })
}

// 🚀 导出组合式函数
export const useConfirm = () => {
  return {
    // 状态
    confirmState,
    
    // 基础方法
    showConfirm,
    handleConfirm,
    handleCancel,
    hideConfirm,
    
    // 预设方法
    confirmDelete,
    confirmClear,
    confirmLogout,
    confirmReset
  }
}

// 🌍 全局实例
export const confirm = useConfirm()
