import request from './request'

export const getArtifactList = (project_id) =>
  request.get(`/api/artifact/list?project_id=${project_id}`)

export const deployArtifact = (id) =>
  request.post(`/api/artifact/${id}/deploy`)

export const deleteArtifact = (id) =>
  request.delete(`/api/artifact/${id}`)

export const toggleArtifactStar = (id) =>
  request.post(`/api/artifact/${id}/star`)

export const getStarStatus = (projectId) =>
  request.get('/api/artifact/star-status', { params: { project_id: projectId } })