// 主机管理相关API
import { request } from './request.js'

// 获取主机列表
export const getHosts = async (page = 1, size = 10, environment = '', sortBy = 'environment', sortOrder = 'asc') => {
  const params = new URLSearchParams({
    page: page.toString(),
    size: size.toString(),
    sort_by: sortBy,
    sort_order: sortOrder
  })

  if (environment) {
    params.append('environment', environment)
  }

  return request(`/api/hosts?${params.toString()}`)
}

// 获取主机详情
export const getHost = async (id) => {
  return request(`/api/hosts/${id}`)
}

// 创建主机
export const createHost = async (hostData) => {
  return request('/api/hosts', {
    method: 'POST',
    body: JSON.stringify(hostData)
  })
}

// 更新主机
export const updateHost = async (id, hostData) => {
  return request(`/api/hosts/${id}`, {
    method: 'PUT',
    body: JSON.stringify(hostData)
  })
}

// 删除主机
export const deleteHost = async (id) => {
  return request(`/api/hosts/${id}`, {
    method: 'DELETE'
  })
}

// 测试主机连接
export const testHostConnection = async (id) => {
  return request(`/api/hosts/${id}/test`, {
    method: 'POST'
  })
}

// 临时测试主机连接（不保存主机信息）
export const testHostConnectionTemp = async (hostData) => {
  return request('/api/hosts/test-temp', {
    method: 'POST',
    body: JSON.stringify(hostData)
  })
}

// 根据环境获取主机列表
export const getHostsByEnvironment = async (environment) => {
  return request(`/api/hosts/environment/${environment}`)
}

// 验证私钥格式
export const validatePrivateKey = (privateKey) => {
  if (!privateKey) {
    return { valid: false, message: '私钥不能为空' }
  }

  // 检查私钥格式
  if (!privateKey.includes('BEGIN') || !privateKey.includes('PRIVATE KEY')) {
    return { valid: false, message: '私钥格式不正确，请确保包含完整的私钥内容' }
  }

  // 检查常见的私钥类型
  const validHeaders = [
    '-----BEGIN RSA PRIVATE KEY-----',
    '-----BEGIN OPENSSH PRIVATE KEY-----',
    '-----BEGIN EC PRIVATE KEY-----',
    '-----BEGIN DSA PRIVATE KEY-----',
    '-----BEGIN PRIVATE KEY-----'
  ]

  const hasValidHeader = validHeaders.some(header => privateKey.includes(header))
  if (!hasValidHeader) {
    return { valid: false, message: '不支持的私钥类型，请使用RSA、ECDSA、DSA或OpenSSH格式的私钥' }
  }

  return { valid: true, message: '私钥格式验证通过' }
}

// 格式化主机状态
export const formatHostStatus = (status) => {
  const statusMap = {
    'active': { text: '活跃', type: 'success' },
    'inactive': { text: '停用', type: 'danger' }
  }
  return statusMap[status] || { text: '未知', type: 'info' }
}

// 格式化认证类型
export const formatAuthType = (authType) => {
  const authTypeMap = {
    'password': { text: '密码认证', type: 'primary' },
    'private_key': { text: '私钥认证', type: 'warning' }
  }
  return authTypeMap[authType] || { text: '未知', type: 'info' }
}

// 格式化环境类型
export const formatEnvironment = (environment) => {
  const envMap = {
    'test': { text: '测试环境', type: 'success' },
    'prod': { text: '生产环境', type: 'danger' }
  }
  return envMap[environment] || { text: '未知', type: 'info' }
}

// 格式化连接测试结果
export const formatTestResult = (result) => {
  const resultMap = {
    'success': { text: '成功', type: 'success' },
    'failed': { text: '失败', type: 'danger' }
  }
  return resultMap[result] || { text: '未测试', type: 'info' }
}
