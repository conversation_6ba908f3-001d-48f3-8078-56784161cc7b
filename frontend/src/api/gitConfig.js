import request from './request'

// Git配置管理API
export const gitConfigApi = {
  // 获取Git配置列表
  getList(params = {}) {
    const searchParams = new URLSearchParams({
      page: (params.page || 1).toString(),
      size: (params.size || 10).toString(),
      search: params.search || '',
      sort_by: params.sortBy || 'name',
      sort_order: params.sortOrder || 'asc',
      status: params.status || ''
    })

    return request.get(`/api/git-configs?${searchParams.toString()}`)
  },

  // 获取单个Git配置详情
  getById(id) {
    return request.get(`/api/git-configs/${id}`)
  },

  // 创建Git配置
  create(data) {
    return request.post('/api/git-configs', {
      name: data.name,
      repository_url: data.repository_url,
      auth_type: data.auth_type,
      username: data.username || '',
      password: data.password || '',
      ssh_key: data.ssh_key || '',
      default_branch: data.default_branch || 'main',
      description: data.description || '',
      status: data.status || 'active'
    })
  },

  // 更新Git配置
  update(id, data) {
    return request.put(`/api/git-configs/${id}`, {
      name: data.name,
      repository_url: data.repository_url,
      auth_type: data.auth_type,
      username: data.username || '',
      password: data.password || '',
      ssh_key: data.ssh_key || '',
      default_branch: data.default_branch || 'main',
      description: data.description || '',
      status: data.status || 'active'
    })
  },

  // 删除Git配置
  delete(id) {
    return request.delete(`/api/git-configs/${id}`)
  },

  // 测试Git连接
  testConnection(data) {
    return request.post('/api/git-configs/test', {
      id: data.id || 0,
      repository_url: data.repository_url,
      auth_type: data.auth_type,
      username: data.username || '',
      password: data.password || '',
      ssh_key: data.ssh_key || ''
    })
  },

  // 获取活跃的Git配置列表（用于下拉选择）
  getActiveConfigs() {
    return request.get('/api/git-configs/active')
  },

  // 获取Git仓库分支列表
  getBranches(id) {
    return request.get(`/api/git-configs/${id}/branches`)
  },

  // 获取Git仓库标签列表
  getTags(id) {
    return request.get(`/api/git-configs/${id}/tags`)
  }
}

export default gitConfigApi
