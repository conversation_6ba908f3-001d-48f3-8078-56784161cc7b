import request from './request'

export const getProjects = (page = 1, size = 10, filters = {}) => {
  const params = new URLSearchParams({
    page: page.toString(),
    size: size.toString()
  })

  if (filters.environment) {
    params.append('environment', filters.environment)
  }

  if (filters.project_type) {
    params.append('project_type', filters.project_type)
  }

  if (filters.name_filter) {
    params.append('name_filter', filters.name_filter)
  }

  return request.get(`/api/projects?${params.toString()}`)
}

export const createProject = (data) =>
  request.post('/api/project', data)

export const updateProject = (data) =>
  request.put('/api/project', data)

export const deleteProject = (id) =>
  request.delete(`/api/project/${id}`)

export const deployProject = (id) =>
  request.post(`/api/project/${id}/deploy`)

export const getGitBranches = (git_url, git_username, git_password) =>
  request.post('/api/git/branches', { git_url, git_username, git_password })

export const getGitTags = (git_url, git_username, git_password) =>
  request.post('/api/git/tags', { git_url, git_username, git_password })

export const buildProject = (id) =>
  request.post(`/api/project/${id}/build`)

// 一键部署（先构建再部署）
export const buildAndDeployProject = (id) =>
  request.post(`/api/project/${id}/build-and-deploy`) 