// 统一请求封装
const baseConfig = {
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  },
  credentials: 'same-origin'
}

async function request(url, options = {}) {
  try {
    // 设置默认超时时间为5秒
    const timeout = options.timeout || 5000

    const config = {
      ...baseConfig,
      ...options,
      headers: {
        ...baseConfig.headers,
        ...(options.headers || {})
      }
    }

    if (config.body && typeof config.body === 'object') {
      config.body = JSON.stringify(config.body)
    }

    // 创建超时控制器
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    config.signal = controller.signal

    const response = await fetch(url, config)
    clearTimeout(timeoutId)

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(errorText || `请求失败: ${response.status}`)
    }

    const result = await response.json()
    if (result.success === false) {
      throw new Error(result.message || '操作失败')
    }
    return result
  } catch (error) {
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接')
    }
    throw error
  }
}

// 导出request函数和便捷方法
export { request }

export default {
  get: (url, options) => request(url, { ...options, method: 'GET' }),
  post: (url, body, options) => request(url, { ...options, method: 'POST', body }),
  put: (url, body, options) => request(url, { ...options, method: 'PUT', body }),
  delete: (url, options) => request(url, { ...options, method: 'DELETE' })
}