<script setup>
import Icon from './components/Icon.vue'
import ToastContainer from './components/ToastContainer.vue'
import ConfirmDialog from './components/ConfirmDialog.vue'
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useConfirm } from './composables/useConfirm.js'

const router = useRouter()
const route = useRoute()

// 🌸 全局确认对话框
const { confirmState, handleConfirm, handleCancel } = useConfirm()

// 移动端菜单控制
const isMobileMenuOpen = ref(false)

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

// 获取页面信息
const getPageInfo = () => {
  const routes = {
    '/': { title: '仪表板', breadcrumb: ['仪表板'] },
    '/dashboard': { title: '仪表板', breadcrumb: ['仪表板'] },
    '/projects': { title: '项目管理', breadcrumb: ['仪表板', '项目管理'] },
    '/hosts': { title: '主机管理', breadcrumb: ['仪表板', '主机管理'] },
    '/git-configs': { title: 'Git配置管理', breadcrumb: ['仪表板', 'Git配置管理'] },
    '/workflow': { title: '工作流设计器', breadcrumb: ['仪表板', '工作流设计器'] },
    '/workflow-projects': { title: '工作流项目管理', breadcrumb: ['仪表板', '工作流项目管理'] },
    '/workflow-projects/create': { title: '创建工作流项目', breadcrumb: ['仪表板', '工作流项目管理', '创建项目'] },
  }

  // 处理动态路由
  if (route.path.startsWith('/workflow-projects/') && route.path.includes('/edit')) {
    return { title: '编辑工作流项目', breadcrumb: ['仪表板', '工作流项目管理', '编辑项目'] }
  }
  if (route.path.startsWith('/workflow-projects/') && route.path.includes('/design')) {
    return { title: '工作流设计', breadcrumb: ['仪表板', '工作流项目管理', '工作流设计'] }
  }

  return routes[route.path] || { title: 'GoShip', breadcrumb: ['仪表板'] }
}

// 导航菜单项
const menuItems = [
  { path: '/dashboard', name: '仪表板', icon: 'dashboard' },
  { path: '/projects', name: '项目管理', icon: 'list' },
  { path: '/workflow-projects', name: '工作流项目', icon: 'workflow' },
  { path: '/hosts', name: '主机管理', icon: 'setting' },
  { path: '/git-configs', name: 'Git配置', icon: 'git' },
  { path: '/workflow', name: '工作流设计器', icon: 'workflow' }
]

// 检查当前路由是否为仪表板页面
const isDashboardPage = computed(() => {
  return route.path === '/dashboard' || route.path === '/'
})

// 检查路由是否激活
const isActiveRoute = (path) => {
  // 特殊处理仪表板路由
  if (path === '/dashboard') {
    return route.path === '/dashboard' || route.path === '/'
  }
  // 特殊处理工作流项目路由
  if (path === '/workflow-projects') {
    return route.path.startsWith('/workflow-projects')
  }
  return route.path === path
}
</script>

<template>
  <!-- 🌸 浪漫风格主布局 - 固定视窗高度 -->
  <div class="h-screen overflow-hidden bg-gradient-to-br from-romantic-50 via-dreamy-50 to-sky-50 flex flex-col">
    <!-- 🎯 顶部导航栏 -->
    <header class="navbar-romantic sticky top-0 z-50">
      <div class="container mx-auto px-6">
        <div class="navbar">
          <!-- 左侧：Logo和品牌 -->
          <div class="navbar-start">
            <div class="navbar-brand">
              <div class="navbar-logo">
                <span class="text-white font-bold text-lg">G</span>
              </div>
              <span class="navbar-title">GoShip</span>
            </div>
          </div>

          <!-- 中间：面包屑导航 -->
          <div class="navbar-center hidden lg:flex">
            <div class="breadcrumb-romantic">
              <div class="breadcrumbs">
                <ul>
                  <li v-for="(crumb, index) in getPageInfo().breadcrumb" :key="index">
                    <router-link
                      v-if="crumb === '仪表板' && index < getPageInfo().breadcrumb.length - 1"
                      to="/dashboard"
                      :class="'text-gray-500 text-base hover:text-romantic-500 cursor-pointer'"
                    >
                      {{ crumb }}
                    </router-link>
                    <span
                      v-else
                      :class="index === getPageInfo().breadcrumb.length - 1 ? 'text-romantic-600 text-base' : 'text-gray-500 text-base'"
                    >
                      {{ crumb }}
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <!-- 右侧：导航菜单和用户操作 -->
          <div class="navbar-end">
            <!-- 桌面端导航菜单 -->
            <div class="hidden lg:flex items-center space-x-1 mr-6">
              <router-link
                v-for="item in menuItems"
                :key="item.path"
                :to="item.path"
                class="btn btn-ghost btn-sm"
                :class="isActiveRoute(item.path) ? 'bg-romantic-100 text-romantic-700' : 'text-gray-600'"
              >
                <Icon :name="item.icon" :size="16" />
                {{ item.name }}
              </router-link>
            </div>

            <!-- 用户操作 -->
            <div class="flex items-center space-x-3">
              <!-- 搜索按钮 -->
              <button class="btn btn-ghost btn-circle btn-sm">
                <Icon name="search" :size="18" />
              </button>

              <!-- 通知按钮 -->
              <button class="btn btn-ghost btn-circle btn-sm">
                <Icon name="bell" :size="18" />
                <div class="badge badge-romantic badge-xs absolute -top-1 -right-1">3</div>
              </button>

              <!-- 用户头像 -->
              <div class="dropdown dropdown-end">
                <div tabindex="0" role="button" class="btn btn-ghost btn-circle avatar">
                  <div class="user-avatar">
                    <span class="user-avatar-text">U</span>
                  </div>
                </div>
                <ul tabindex="0" class="dropdown-content menu bg-white rounded-box z-[1] w-52 p-2 shadow-lg border border-romantic-100">
                  <li><a class="text-gray-700">个人设置</a></li>
                  <li><a class="text-gray-700">系统设置</a></li>
                  <li><hr class="my-2"></li>
                  <li><a class="text-red-600">退出登录</a></li>
                </ul>
              </div>

              <!-- 移动端菜单按钮 -->
              <button
                class="btn btn-ghost btn-circle btn-sm lg:hidden"
                @click="toggleMobileMenu"
              >
                <Icon name="expand" :size="18" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- 📱 移动端导航菜单 -->
    <div v-if="isMobileMenuOpen" class="lg:hidden fixed inset-0 z-40 glass-backdrop" @click="toggleMobileMenu">
      <div class="fixed top-0 right-0 h-full w-64 bg-white shadow-xl transform transition-transform" @click.stop>
        <div class="p-6">
          <div class="flex items-center justify-between mb-6">
            <span class="text-lg font-semibold text-gray-800">菜单</span>
            <button class="btn btn-ghost btn-circle btn-sm" @click="toggleMobileMenu">
              <Icon name="close" :size="18" />
            </button>
          </div>
          <nav class="space-y-2">
            <router-link
              v-for="item in menuItems"
              :key="item.path"
              :to="item.path"
              class="flex items-center gap-3 px-4 py-3 rounded-lg transition-colors"
              :class="isActiveRoute(item.path) ? 'bg-romantic-100 text-romantic-700' : 'text-gray-600 hover:bg-gray-100'"
              @click="toggleMobileMenu"
            >
              <Icon :name="item.icon" :size="20" />
              {{ item.name }}
            </router-link>
          </nav>
        </div>
      </div>
    </div>

    <!-- 🎨 主要内容区域 - 占用剩余高度 -->
    <main class="flex-1 overflow-y-auto min-h-0">
      <router-view class="h-full" />
    </main>

    <!-- 🌟 页面底部装饰 -->
    <div class="fixed bottom-4 right-4 opacity-20 pointer-events-none">
      <div class="w-32 h-32 bg-gradient-to-br from-romantic-200 to-dreamy-200 rounded-full blur-3xl"></div>
    </div>

    <!-- 🍞 全局Toast通知容器 -->
    <ToastContainer />

    <!-- 🎭 全局确认对话框 -->
    <ConfirmDialog
      v-model:visible="confirmState.visible"
      :title="confirmState.title"
      :message="confirmState.message"
      :warning="confirmState.warning"
      :confirm-text="confirmState.confirmText"
      :cancel-text="confirmState.cancelText"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </div>
</template>

<style scoped>
/* 🌸 浪漫风格布局样式 */

/* 导航栏增强 */
.navbar {
  height: 4rem;
}

.navbar-brand:hover .navbar-logo {
  transform: scale(1.1);
  transition: transform 0.3s ease;
}

.navbar-title {
  background: linear-gradient(135deg, #ec4899, #a855f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 面包屑样式 */
.breadcrumbs ul li a {
  transition: color 0.2s ease;
}

.breadcrumbs ul li a:hover {
  color: #ec4899;
}

/* 移动端菜单动画 */
.mobile-menu-enter-active,
.mobile-menu-leave-active {
  transition: all 0.3s ease;
}

.mobile-menu-enter-from {
  transform: translateX(100%);
}

.mobile-menu-leave-to {
  transform: translateX(100%);
}

/* 用户头像样式 */
.user-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: linear-gradient(to right, #f472b6, #c084fc);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.user-avatar-text {
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 用户头像悬停效果 */
.avatar:hover {
  transform: scale(1.1);
  transition: transform 0.3s ease;
}

/* 通知徽章动画 */
.badge-romantic {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 主内容区域 - 移除高度约束，使用flex布局 */
main {
  /* 移除固定高度约束，使用flex布局自动分配 */
}

/* 底部装饰动画 */
.fixed.bottom-4.right-4 > div {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(-5px) rotate(-1deg); }
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .navbar-center {
    display: none;
  }

  main {
    /* 移动端样式保持简洁 */
  }

  .container {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
}

@media (max-width: 640px) {
  main {
    /* 小屏幕样式保持简洁 */
  }

  .container {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}
</style>
