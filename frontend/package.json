{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "^1.7.4", "@vue-flow/background": "^1.3.2", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.45.0", "@vue-flow/minimap": "^1.5.3", "@vue-flow/node-resizer": "^1.5.0", "@vue-flow/node-toolbar": "^1.1.1", "vue": "^3.5.13", "vue-draggable-plus": "^0.6.0", "vue-router": "^4.4.5"}, "devDependencies": {"@tailwindcss/vite": "^4.0.0", "@vitejs/plugin-vue": "^5.2.3", "daisyui": "^5.0.46", "tailwindcss": "^4.0.0", "vite": "^6.3.5"}}