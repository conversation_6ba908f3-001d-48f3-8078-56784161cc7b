<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>折叠功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 400px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .category {
            margin-bottom: 0;
        }
        .category-header {
            padding: 12px 16px;
            background-color: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .category-header:hover {
            background-color: #f3f4f6;
        }
        .category-title {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            display: flex;
            align-items: center;
        }
        .category-icon {
            margin-right: 8px;
        }
        .category-meta {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .node-count {
            font-size: 12px;
            color: #6b7280;
            background-color: #e5e7eb;
            padding: 2px 8px;
            border-radius: 12px;
        }
        .collapse-icon {
            color: #9ca3af;
            transition: transform 0.2s;
            font-size: 12px;
        }
        .collapse-icon.expanded {
            transform: rotate(180deg);
        }
        .category-content {
            padding: 12px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            transition: all 0.2s;
        }
        .category-content.collapsed {
            display: none;
        }
        .node-item {
            padding: 8px;
            background-color: #f9fafb;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #e5e7eb;
        }
        .node-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        .node-name {
            font-size: 12px;
            font-weight: 500;
            color: #374151;
        }
        .node-desc {
            font-size: 10px;
            color: #6b7280;
            margin-top: 2px;
        }
        .test-collapsed {
            background-color: #fef3c7 !important;
        }
        .test-collapsed .category-header {
            background-color: #fbbf24 !important;
            color: white;
        }
    </style>
</head>
<body>
    <h1>工作流节点分类折叠测试</h1>
    <p>点击分类标题可以折叠/展开节点。测试验证分类默认折叠。</p>
    
    <div class="container">
        <!-- 控制节点 - 默认展开 -->
        <div class="category">
            <div class="category-header" onclick="toggleCategory('control')">
                <div class="category-title">
                    <span class="category-icon">🎮</span>
                    控制节点
                </div>
                <div class="category-meta">
                    <span class="node-count">2</span>
                    <span class="collapse-icon" id="control-icon">▼</span>
                </div>
            </div>
            <div class="category-content" id="control-content">
                <div class="node-item">
                    <div class="node-icon">🚀</div>
                    <div class="node-name">开始</div>
                    <div class="node-desc">工作流开始节点</div>
                </div>
                <div class="node-item">
                    <div class="node-icon">🏁</div>
                    <div class="node-name">结束</div>
                    <div class="node-desc">工作流结束节点</div>
                </div>
            </div>
        </div>

        <!-- 数据源 - 默认展开 -->
        <div class="category">
            <div class="category-header" onclick="toggleCategory('source')">
                <div class="category-title">
                    <span class="category-icon">📥</span>
                    数据源
                </div>
                <div class="category-meta">
                    <span class="node-count">1</span>
                    <span class="collapse-icon" id="source-icon">▼</span>
                </div>
            </div>
            <div class="category-content" id="source-content">
                <div class="node-item">
                    <div class="node-icon">📥</div>
                    <div class="node-name">Git克隆</div>
                    <div class="node-desc">从Git仓库克隆代码</div>
                </div>
            </div>
        </div>

        <!-- 构建部署 - 默认展开 -->
        <div class="category">
            <div class="category-header" onclick="toggleCategory('build')">
                <div class="category-title">
                    <span class="category-icon">🔨</span>
                    构建部署
                </div>
                <div class="category-meta">
                    <span class="node-count">3</span>
                    <span class="collapse-icon" id="build-icon">▼</span>
                </div>
            </div>
            <div class="category-content" id="build-content">
                <div class="node-item">
                    <div class="node-icon">🔨</div>
                    <div class="node-name">Go构建</div>
                    <div class="node-desc">编译Go项目</div>
                </div>
                <div class="node-item">
                    <div class="node-icon">📦</div>
                    <div class="node-name">Go模块</div>
                    <div class="node-desc">管理Go模块依赖</div>
                </div>
                <div class="node-item">
                    <div class="node-icon">🌐</div>
                    <div class="node-name">远程部署</div>
                    <div class="node-desc">部署到远程服务器</div>
                </div>
            </div>
        </div>

        <!-- 测试验证 - 默认折叠 ⭐ -->
        <div class="category test-collapsed">
            <div class="category-header" onclick="toggleCategory('test')">
                <div class="category-title">
                    <span class="category-icon">🧪</span>
                    测试验证
                </div>
                <div class="category-meta">
                    <span class="node-count">4</span>
                    <span class="collapse-icon expanded" id="test-icon">▼</span>
                </div>
            </div>
            <div class="category-content collapsed" id="test-content">
                <div class="node-item">
                    <div class="node-icon">🧪</div>
                    <div class="node-name">Go测试</div>
                    <div class="node-desc">执行Go单元测试</div>
                </div>
                <div class="node-item">
                    <div class="node-icon">❤️</div>
                    <div class="node-name">健康检查</div>
                    <div class="node-desc">检查服务健康状态</div>
                </div>
                <div class="node-item">
                    <div class="node-icon">⬜</div>
                    <div class="node-name">长方形</div>
                    <div class="node-desc">极简测试节点</div>
                </div>
                <div class="node-item">
                    <div class="node-icon">⬛</div>
                    <div class="node-name">空白</div>
                    <div class="node-desc">完全空白节点</div>
                </div>
            </div>
        </div>

        <!-- 逻辑控制 - 默认展开 -->
        <div class="category">
            <div class="category-header" onclick="toggleCategory('logic')">
                <div class="category-title">
                    <span class="category-icon">🧠</span>
                    逻辑控制
                </div>
                <div class="category-meta">
                    <span class="node-count">3</span>
                    <span class="collapse-icon" id="logic-icon">▼</span>
                </div>
            </div>
            <div class="category-content" id="logic-content">
                <div class="node-item">
                    <div class="node-icon">❓</div>
                    <div class="node-name">条件判断</div>
                    <div class="node-desc">根据条件分支执行</div>
                </div>
                <div class="node-item">
                    <div class="node-icon">⚡</div>
                    <div class="node-name">并行执行</div>
                    <div class="node-desc">并行执行多个任务</div>
                </div>
                <div class="node-item">
                    <div class="node-icon">🔄</div>
                    <div class="node-name">循环执行</div>
                    <div class="node-desc">重复执行指定次数</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 折叠状态
        const collapsedState = {
            control: false,
            source: false,
            build: false,
            test: true,    // 测试验证默认折叠
            logic: false
        };

        function toggleCategory(categoryKey) {
            const content = document.getElementById(categoryKey + '-content');
            const icon = document.getElementById(categoryKey + '-icon');
            
            collapsedState[categoryKey] = !collapsedState[categoryKey];
            
            if (collapsedState[categoryKey]) {
                content.classList.add('collapsed');
                icon.classList.add('expanded');
                console.log(`📁 ${categoryKey} 分类折叠`);
            } else {
                content.classList.remove('collapsed');
                icon.classList.remove('expanded');
                console.log(`📁 ${categoryKey} 分类展开`);
            }
        }

        // 页面加载时的状态提示
        window.onload = function() {
            console.log('🎯 折叠功能测试页面已加载');
            console.log('📁 测试验证分类默认折叠状态');
            console.log('💡 点击分类标题可以切换折叠状态');
        };
    </script>
</body>
</html>
