<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>节点配置面板测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            display: flex;
            height: 600px;
        }
        .canvas {
            flex: 1;
            padding: 20px;
            background: #fafafa;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-content: flex-start;
        }
        .node {
            width: 120px;
            height: 80px;
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .node:hover {
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .node.selected {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        .node-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }
        .node-name {
            font-size: 12px;
            font-weight: 500;
            color: #374151;
            text-align: center;
        }
        .config-panel {
            width: 400px;
            background: white;
            border-left: 1px solid #e5e7eb;
            display: flex;
            flex-direction: column;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        .config-panel.visible {
            transform: translateX(0);
        }
        .config-header {
            padding: 16px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .config-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .config-title h3 {
            margin: 0;
            font-size: 18px;
            color: #374151;
        }
        .config-title p {
            margin: 0;
            font-size: 14px;
            color: #6b7280;
        }
        .close-btn {
            padding: 8px;
            background: none;
            border: none;
            cursor: pointer;
            border-radius: 4px;
            color: #6b7280;
        }
        .close-btn:hover {
            background: #e5e7eb;
        }
        .config-content {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
        }
        .config-group {
            margin-bottom: 24px;
        }
        .group-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e5e7eb;
        }
        .field {
            margin-bottom: 16px;
        }
        .field-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 4px;
        }
        .field-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .field-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .field-description {
            font-size: 12px;
            color: #6b7280;
            margin-top: 4px;
        }
        .config-actions {
            padding: 16px;
            border-top: 1px solid #e5e7eb;
            background: #f9fafb;
            display: flex;
            gap: 8px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        .btn-primary {
            background: #3b82f6;
            color: white;
            flex: 1;
        }
        .btn-primary:hover {
            background: #2563eb;
        }
        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }
        .btn-secondary:hover {
            background: #e5e7eb;
        }
        .btn-danger {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
            width: 100%;
            margin-top: 8px;
        }
        .btn-danger:hover {
            background: #fee2e2;
        }
        .instructions {
            margin-bottom: 20px;
            padding: 16px;
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
        }
        .instructions h2 {
            margin: 0 0 8px 0;
            color: #1e40af;
        }
        .instructions p {
            margin: 0;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="instructions">
        <h2>🎯 节点配置面板测试</h2>
        <p>点击下方的节点来测试配置面板功能。每个节点都有不同的配置选项。</p>
    </div>

    <div class="container">
        <div class="canvas">
            <!-- Git克隆节点 -->
            <div class="node" data-type="git_clone" onclick="selectNode(this)">
                <div class="node-icon">📥</div>
                <div class="node-name">Git克隆</div>
            </div>

            <!-- Go构建节点 -->
            <div class="node" data-type="go_build" onclick="selectNode(this)">
                <div class="node-icon">🔨</div>
                <div class="node-name">Go构建</div>
            </div>

            <!-- Go模块节点 -->
            <div class="node" data-type="go_module" onclick="selectNode(this)">
                <div class="node-icon">📦</div>
                <div class="node-name">Go模块</div>
            </div>

            <!-- Go测试节点 -->
            <div class="node" data-type="go_test" onclick="selectNode(this)">
                <div class="node-icon">🧪</div>
                <div class="node-name">Go测试</div>
            </div>

            <!-- 远程部署节点 -->
            <div class="node" data-type="remote_deploy" onclick="selectNode(this)">
                <div class="node-icon">🌐</div>
                <div class="node-name">远程部署</div>
            </div>

            <!-- 启动服务节点 -->
            <div class="node" data-type="start_service" onclick="selectNode(this)">
                <div class="node-icon">▶️</div>
                <div class="node-name">启动服务</div>
            </div>
        </div>

        <!-- 配置面板 -->
        <div class="config-panel" id="configPanel">
            <div class="config-header">
                <div class="config-title">
                    <span id="configIcon">⚙️</span>
                    <div>
                        <h3 id="configName">选择一个节点</h3>
                        <p id="configDesc">点击左侧节点查看配置</p>
                    </div>
                </div>
                <button class="close-btn" onclick="closePanel()">✕</button>
            </div>

            <div class="config-content" id="configContent">
                <p style="text-align: center; color: #6b7280; margin-top: 40px;">
                    👈 点击左侧节点开始配置
                </p>
            </div>

            <div class="config-actions">
                <button class="btn btn-primary">💾 保存配置</button>
                <button class="btn btn-secondary">🔄 重置</button>
                <button class="btn btn-danger">🗑️ 删除节点</button>
            </div>
        </div>
    </div>

    <script>
        // 节点配置数据（简化版）
        const nodeConfigs = {
            git_clone: {
                name: 'Git克隆',
                icon: '📥',
                description: '从Git仓库克隆代码',
                fields: [
                    { label: '仓库地址', type: 'text', placeholder: 'https://github.com/user/repo.git', required: true },
                    { label: '分支/标签', type: 'text', placeholder: 'main', defaultValue: 'main' },
                    { label: '目标目录', type: 'text', placeholder: './src', defaultValue: './src' },
                    { label: '克隆深度', type: 'number', defaultValue: 1, min: 1 },
                    { label: '递归克隆子模块', type: 'checkbox', defaultValue: false }
                ]
            },
            go_build: {
                name: 'Go构建',
                icon: '🔨',
                description: '编译Go项目为二进制文件',
                fields: [
                    { label: '工作目录', type: 'text', placeholder: './src', defaultValue: './src' },
                    { label: '主文件路径', type: 'text', placeholder: './main.go', defaultValue: './main.go' },
                    { label: '输出文件名', type: 'text', placeholder: 'app', defaultValue: 'app' },
                    { label: 'Go版本', type: 'select', options: ['1.19', '1.20', '1.21', '1.22'], defaultValue: '1.21' },
                    { label: '启用CGO', type: 'checkbox', defaultValue: false },
                    { label: '构建标签', type: 'text', placeholder: 'prod,mysql,redis' }
                ]
            },
            go_module: {
                name: 'Go模块',
                icon: '📦',
                description: '管理Go模块依赖',
                fields: [
                    { label: '工作目录', type: 'text', placeholder: './src', defaultValue: './src' },
                    { label: '执行命令', type: 'select', options: ['download', 'tidy', 'vendor', 'verify'], defaultValue: 'download' },
                    { label: '模块代理', type: 'text', placeholder: 'https://goproxy.cn,direct', defaultValue: 'https://goproxy.cn,direct' },
                    { label: '私有模块', type: 'text', placeholder: 'github.com/company/*' },
                    { label: '使用模块缓存', type: 'checkbox', defaultValue: true }
                ]
            },
            go_test: {
                name: 'Go测试',
                icon: '🧪',
                description: '执行Go单元测试',
                fields: [
                    { label: '工作目录', type: 'text', placeholder: './src', defaultValue: './src' },
                    { label: '测试包路径', type: 'text', placeholder: './...', defaultValue: './...' },
                    { label: '详细输出', type: 'checkbox', defaultValue: true },
                    { label: '竞态检测', type: 'checkbox', defaultValue: false },
                    { label: '生成覆盖率报告', type: 'checkbox', defaultValue: true },
                    { label: '超时时间(秒)', type: 'number', defaultValue: 300, min: 1 }
                ]
            },
            remote_deploy: {
                name: '远程部署',
                icon: '🌐',
                description: '部署到远程服务器',
                fields: [
                    { label: '服务器地址', type: 'text', placeholder: '*************', required: true },
                    { label: 'SSH端口', type: 'number', defaultValue: 22, min: 1, max: 65535 },
                    { label: '用户名', type: 'text', placeholder: 'root', defaultValue: 'root', required: true },
                    { label: '认证方式', type: 'select', options: ['SSH密钥', '密码认证'], defaultValue: 'SSH密钥' },
                    { label: '远程部署路径', type: 'text', placeholder: '/opt/app', defaultValue: '/opt/app', required: true },
                    { label: '备份旧版本', type: 'checkbox', defaultValue: true }
                ]
            },
            start_service: {
                name: '启动服务',
                icon: '▶️',
                description: '启动应用服务',
                fields: [
                    { label: '服务名称', type: 'text', placeholder: 'myapp', required: true },
                    { label: '可执行文件路径', type: 'text', placeholder: './app', defaultValue: './app', required: true },
                    { label: '工作目录', type: 'text', placeholder: './', defaultValue: './' },
                    { label: '服务端口', type: 'number', defaultValue: 8080, min: 1, max: 65535 },
                    { label: '启动参数', type: 'text', placeholder: '--config=config.yml --env=prod' },
                    { label: '后台运行', type: 'checkbox', defaultValue: true },
                    { label: '健康检查URL', type: 'text', placeholder: 'http://localhost:8080/health' }
                ]
            }
        };

        let selectedNodeType = null;

        function selectNode(element) {
            // 清除之前的选中状态
            document.querySelectorAll('.node').forEach(node => {
                node.classList.remove('selected');
            });

            // 选中当前节点
            element.classList.add('selected');
            selectedNodeType = element.dataset.type;

            // 显示配置面板
            showConfigPanel(selectedNodeType);
        }

        function showConfigPanel(nodeType) {
            const config = nodeConfigs[nodeType];
            if (!config) return;

            // 更新面板标题
            document.getElementById('configIcon').textContent = config.icon;
            document.getElementById('configName').textContent = config.name;
            document.getElementById('configDesc').textContent = config.description;

            // 生成配置表单
            const content = document.getElementById('configContent');
            content.innerHTML = '';

            config.fields.forEach(field => {
                const fieldDiv = document.createElement('div');
                fieldDiv.className = 'field';

                const label = document.createElement('label');
                label.className = 'field-label';
                label.textContent = field.label + (field.required ? ' *' : '');

                let input;
                if (field.type === 'text' || field.type === 'number') {
                    input = document.createElement('input');
                    input.type = field.type;
                    input.className = 'field-input';
                    input.placeholder = field.placeholder || '';
                    if (field.defaultValue !== undefined) {
                        input.value = field.defaultValue;
                    }
                    if (field.min !== undefined) input.min = field.min;
                    if (field.max !== undefined) input.max = field.max;
                } else if (field.type === 'select') {
                    input = document.createElement('select');
                    input.className = 'field-input';
                    field.options.forEach(option => {
                        const optionEl = document.createElement('option');
                        optionEl.value = option;
                        optionEl.textContent = option;
                        if (option === field.defaultValue) {
                            optionEl.selected = true;
                        }
                        input.appendChild(optionEl);
                    });
                } else if (field.type === 'checkbox') {
                    input = document.createElement('input');
                    input.type = 'checkbox';
                    input.checked = field.defaultValue || false;
                    input.style.width = 'auto';
                    input.style.marginRight = '8px';
                }

                fieldDiv.appendChild(label);
                fieldDiv.appendChild(input);

                if (field.description) {
                    const desc = document.createElement('div');
                    desc.className = 'field-description';
                    desc.textContent = field.description;
                    fieldDiv.appendChild(desc);
                }

                content.appendChild(fieldDiv);
            });

            // 显示面板
            document.getElementById('configPanel').classList.add('visible');
        }

        function closePanel() {
            document.getElementById('configPanel').classList.remove('visible');
            document.querySelectorAll('.node').forEach(node => {
                node.classList.remove('selected');
            });
            selectedNodeType = null;
        }

        // 页面加载完成提示
        window.onload = function() {
            console.log('🎯 节点配置面板测试页面已加载');
            console.log('💡 点击节点查看不同的配置选项');
        };
    </script>
</body>
</html>
