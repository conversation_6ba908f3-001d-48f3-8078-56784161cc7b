package routers

import (
	"GoShip/controllers"

	beego "github.com/beego/beego/v2/server/web"
	"github.com/beego/beego/v2/server/web/context"
)

func init() {
	// 设置CORS
	beego.InsertFilter("*", beego.BeforeRouter, func(ctx *context.Context) {
		ctx.Output.Header("Access-Control-Allow-Origin", "*")
		ctx.Output.Header("Access-Control-Allow-Methods", "GET,POST,PUT,DELETE,OPTIONS")
		ctx.Output.Header("Access-Control-Allow-Headers", "Content-Type,Authorization")
		if ctx.Input.Method() == "OPTIONS" {
			ctx.Abort(200, "")
		}
	})

	// 项目相关路由
	beego.Router("/api/project", &controllers.ProjectController{}, "post:CreateProject")
	beego.Router("/api/projects", &controllers.ProjectController{}, "get:GetProjects")
	beego.Router("/api/project", &controllers.ProjectController{}, "put:UpdateProject")
	beego.Router("/api/project/:id", &controllers.ProjectController{}, "get:GetProject;delete:DeleteProject")
	beego.Router("/api/project/:id/build", &controllers.ProjectController{}, "post:BuildProject")
	beego.Router("/api/project/:id/build-and-deploy", &controllers.ProjectController{}, "post:BuildAndDeploy")

	// 主机管理相关路由
	beego.Router("/api/hosts", &controllers.HostController{}, "get:GetHosts;post:CreateHost")
	beego.Router("/api/hosts/test-temp", &controllers.HostController{}, "post:TestHostConnectionTemp")
	beego.Router("/api/hosts/environment/:environment", &controllers.HostController{}, "get:GetHostsByEnvironment")
	beego.Router("/api/hosts/:id", &controllers.HostController{}, "get:GetHost;put:UpdateHost;delete:DeleteHost")
	beego.Router("/api/hosts/:id/test", &controllers.HostController{}, "post:TestHostConnection")

	// Git认证管理相关路由
	beego.Router("/api/git-configs", &controllers.GitAuthController{}, "get:GetList;post:Post")
	beego.Router("/api/git-configs/active", &controllers.GitAuthController{}, "get:GetActiveConfigs")
	beego.Router("/api/git-configs/test", &controllers.GitAuthController{}, "post:TestConnection")
	beego.Router("/api/git-configs/:id", &controllers.GitAuthController{}, "get:Get;put:Put;delete:Delete")

	// WebSocket 路由
	beego.Router("/ws/project/:id/deploy/logs", &controllers.WebSocketController{}, "get:DeployLogs")

	// API 路由组
	ns := beego.NewNamespace("/api",
		// Git 相关 API
		beego.NSNamespace("/git",
			beego.NSRouter("/branches", &controllers.GitController{}, "post:GetBranches"),
			beego.NSRouter("/tags", &controllers.GitController{}, "post:GetTags"),
		),
		// Python 相关 API
		beego.NSNamespace("/python",
			beego.NSRouter("/check-conda-env", &controllers.PythonController{}, "post:CheckCondaEnvironment"),
			beego.NSRouter("/conda-envs", &controllers.PythonController{}, "post:GetCondaEnvironments"),
			beego.NSRouter("/version", &controllers.PythonController{}, "post:GetPythonVersion"),
		),
		// Conda 相关 API
		beego.NSNamespace("/conda",
			beego.NSRouter("/check-environment", &controllers.CondaController{}, "post:CheckEnvironment"),
			beego.NSRouter("/environments", &controllers.CondaController{}, "get:ListEnvironments"),
			beego.NSRouter("/environment/:env_name", &controllers.CondaController{}, "get:GetEnvironmentInfo"),
		),
	)

	// 注册命名空间
	beego.AddNamespace(ns)

	// 静态文件服务
	beego.SetStaticPath("/", "static")

	// /admin 路径重定向到后台首页
	beego.Get("/admin", func(ctx *context.Context) {
		ctx.Redirect(302, "/static/index.html")
	})
	beego.Get("/admin/", func(ctx *context.Context) {
		ctx.Redirect(302, "/static/index.html")
	})

	// 新增产物部署接口路由
	beego.Router("/api/artifact/:id/deploy", &controllers.ArtifactController{}, "post:DeployArtifact")
	beego.Router("/api/artifact/list", &controllers.ArtifactController{}, "get:ListByProject")
	beego.Router("/api/artifact/:id", &controllers.ArtifactController{}, "delete:DeleteArtifact")
	beego.Router("/api/artifact/:id/star", &controllers.ArtifactController{}, "post:ToggleStar")
	beego.Router("/api/artifact/star-status", &controllers.ArtifactController{}, "get:GetStarStatus")
}
