package tests

import (
	"GoShip/controllers"
	"GoShip/models"
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	beego "github.com/beego/beego/v2/server/web"
	"github.com/stretchr/testify/assert"
)

// TestCondaEnvironmentCheck 测试conda环境检测功能
func TestCondaEnvironmentCheck(t *testing.T) {
	// 初始化beego测试环境
	beego.TestBeegoInit("../")

	tests := []struct {
		name           string
		envName        string
		expectedStatus int
		expectedExists bool
	}{
		{
			name:           "检测存在的环境",
			envName:        "base", // base环境通常存在
			expectedStatus: 200,
			expectedExists: true,
		},
		{
			name:           "检测不存在的环境",
			envName:        "non_existent_env_12345",
			expectedStatus: 200,
			expectedExists: false,
		},
		{
			name:           "空环境名称",
			envName:        "",
			expectedStatus: 400,
			expectedExists: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 准备请求数据
			requestData := controllers.EnvironmentCheckRequest{
				EnvName: tt.envName,
			}
			jsonData, _ := json.Marshal(requestData)

			// 创建HTTP请求
			req, _ := http.NewRequest("POST", "/api/conda/check-environment", bytes.NewBuffer(jsonData))
			req.Header.Set("Content-Type", "application/json")

			// 创建响应记录器
			w := httptest.NewRecorder()

			// 创建控制器实例
			controller := &controllers.CondaController{}
			controller.Init(nil, "", "", nil)

			// 模拟beego上下文
			controller.Ctx = &beego.Context{
				Input:  beego.NewInput(),
				Output: beego.NewOutput(),
			}
			controller.Ctx.Input.Context = req
			controller.Ctx.Output.Context = w

			// 设置请求体
			controller.Ctx.Input.RequestBody = jsonData

			// 执行检测
			controller.CheckEnvironment()

			// 验证响应状态码
			assert.Equal(t, tt.expectedStatus, w.Code)

			if w.Code == 200 {
				// 解析响应数据
				var response controllers.EnvironmentCheckResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// 验证环境存在性
				assert.Equal(t, tt.expectedExists, response.Exists)

				// 如果环境存在，验证额外信息
				if response.Exists {
					assert.NotEmpty(t, response.Location)
					// base环境通常有Python版本信息
					if tt.envName == "base" {
						assert.NotEmpty(t, response.PythonVersion)
					}
				}
			}
		})
	}
}

// TestCondaEnvironmentList 测试conda环境列表功能
func TestCondaEnvironmentList(t *testing.T) {
	// 初始化beego测试环境
	beego.TestBeegoInit("../")

	// 创建HTTP请求
	req, _ := http.NewRequest("GET", "/api/conda/environments", nil)

	// 创建响应记录器
	w := httptest.NewRecorder()

	// 创建控制器实例
	controller := &controllers.CondaController{}
	controller.Init(nil, "", "", nil)

	// 模拟beego上下文
	controller.Ctx = &beego.Context{
		Input:  beego.NewInput(),
		Output: beego.NewOutput(),
	}
	controller.Ctx.Input.Context = req
	controller.Ctx.Output.Context = w

	// 执行列表获取
	controller.ListEnvironments()

	// 验证响应状态码
	assert.Equal(t, 200, w.Code)

	// 解析响应数据
	var environments []string
	err := json.Unmarshal(w.Body.Bytes(), &environments)
	assert.NoError(t, err)

	// 验证返回的是字符串数组
	assert.IsType(t, []string{}, environments)
}

// TestPythonConfigValidation 测试Python配置验证逻辑
func TestPythonConfigValidation(t *testing.T) {
	tests := []struct {
		name           string
		config         map[string]interface{}
		envExists      bool
		expectedErrors int
		description    string
	}{
		{
			name: "现有环境配置（无需Python版本）",
			config: map[string]interface{}{
				"package_manager":   "conda",
				"venv_name":         "existing_env",
				"requirements_file": "environment.yml",
				"start_command":     "python app.py",
			},
			envExists:      true,
			expectedErrors: 0,
			description:    "使用现有环境时，Python版本可选",
		},
		{
			name: "新环境配置（需要Python版本）",
			config: map[string]interface{}{
				"package_manager":   "conda",
				"venv_name":         "new_env",
				"python_version":    "3.10",
				"requirements_file": "environment.yml",
				"start_command":     "python app.py",
			},
			envExists:      false,
			expectedErrors: 0,
			description:    "创建新环境时，提供了Python版本",
		},
		{
			name: "新环境配置（缺少Python版本）",
			config: map[string]interface{}{
				"package_manager":   "conda",
				"venv_name":         "new_env",
				"requirements_file": "environment.yml",
				"start_command":     "python app.py",
			},
			envExists:      false,
			expectedErrors: 1,
			description:    "创建新环境时，缺少Python版本",
		},
		{
			name: "非conda环境配置",
			config: map[string]interface{}{
				"package_manager":   "pip",
				"venv_name":         "pip_env",
				"requirements_file": "requirements.txt",
				"start_command":     "python app.py",
			},
			envExists:      false,
			expectedErrors: 0,
			description:    "非conda环境不需要特殊验证",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 模拟验证逻辑
			errors := validatePythonConfig(tt.config, tt.envExists)
			
			assert.Equal(t, tt.expectedErrors, len(errors), 
				"Expected %d errors, got %d. Description: %s", 
				tt.expectedErrors, len(errors), tt.description)
		})
	}
}

// validatePythonConfig 模拟前端验证逻辑
func validatePythonConfig(config map[string]interface{}, envExists bool) []string {
	var errors []string

	packageManager, _ := config["package_manager"].(string)
	venvName, _ := config["venv_name"].(string)
	pythonVersion, _ := config["python_version"].(string)
	requirementsFile, _ := config["requirements_file"].(string)
	startCommand, _ := config["start_command"].(string)

	// Python版本验证（仅对conda且环境不存在的情况）
	if packageManager == "conda" && venvName != "" && !envExists && pythonVersion == "" {
		errors = append(errors, "创建新的 conda 环境时必须指定 Python 版本")
	}

	// 虚拟环境名称验证
	if venvName == "" {
		errors = append(errors, "请输入虚拟环境名称")
	}

	// 依赖文件验证
	if requirementsFile == "" {
		errors = append(errors, "请指定依赖文件路径")
	}

	// 启动脚本验证
	if startCommand == "" {
		errors = append(errors, "请输入启动脚本")
	}

	return errors
}

// TestPythonDeploymentStrategy 测试Python部署策略优化
func TestPythonDeploymentStrategy(t *testing.T) {
	// 创建测试项目
	project := &models.Project{
		ID:   1,
		Name: "test-python-project",
		BuildConfig: models.BuildConfig{
			Language: "python",
			Config: map[string]interface{}{
				"python_version":    "3.10",
				"package_manager":   "conda",
				"venv_type":         "conda",
				"venv_name":         "test_optimization_env",
				"requirements_file": "environment.yml",
				"start_command":     "python app.py",
			},
		},
	}

	// 测试场景：环境不存在，需要创建
	t.Run("创建新环境", func(t *testing.T) {
		// 这里应该测试实际的部署逻辑
		// 由于涉及到实际的conda命令执行，这里只做逻辑验证
		
		config := project.BuildConfig.Config
		pythonVersion, _ := config["python_version"].(string)
		venvName, _ := config["venv_name"].(string)
		
		// 验证配置完整性
		assert.NotEmpty(t, pythonVersion, "创建新环境时应该有Python版本")
		assert.NotEmpty(t, venvName, "应该有环境名称")
	})

	// 测试场景：环境已存在，直接使用
	t.Run("使用现有环境", func(t *testing.T) {
		// 模拟环境已存在的情况
		// 在这种情况下，即使没有指定Python版本也应该能够继续
		
		configWithoutPython := map[string]interface{}{
			"package_manager":   "conda",
			"venv_type":         "conda",
			"venv_name":         "existing_env",
			"requirements_file": "environment.yml",
			"start_command":     "python app.py",
		}
		
		// 验证在环境存在时的配置有效性
		errors := validatePythonConfig(configWithoutPython, true)
		assert.Equal(t, 0, len(errors), "使用现有环境时不应该有验证错误")
	})
}
