package tests

import (
	"GoShip/models"
	"GoShip/services"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"
)

// TestPythonDeployStrategy 测试Python部署策略
func TestPythonDeployStrategy(t *testing.T) {
	// 跳过集成测试，除非明确指定
	if os.Getenv("RUN_INTEGRATION_TESTS") != "1" {
		t.<PERSON><PERSON>("跳过集成测试，设置 RUN_INTEGRATION_TESTS=1 环境变量以运行")
	}

	// 创建临时工作目录
	workDir, err := os.MkdirTemp("", "python-deploy-test")
	if err != nil {
		t.Fatalf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(workDir)

	// 创建测试项目
	project := createTestPythonProject(t)

	// 创建部署策略
	strategy := &services.PythonBuildStrategy{
		Project: project,
	}

	// 测试构建
	t.Log("开始测试Python项目构建...")
	err = strategy.Build(workDir, 1)
	if err != nil {
		t.Fatalf("Python项目构建失败: %v", err)
	}
	t.Log("Python项目构建成功")

	// 测试部署
	t.Log("开始测试Python项目部署...")
	err = strategy.Deploy(workDir, 1)
	if err != nil {
		t.Fatalf("Python项目部署失败: %v", err)
	}
	t.Log("Python项目部署成功")
}

// createTestPythonProject 创建测试Python项目
func createTestPythonProject(t *testing.T) *models.Project {
	// 创建部署目录
	deployPath, err := os.MkdirTemp("", "python-deploy-path")
	if err != nil {
		t.Fatalf("创建部署目录失败: %v", err)
	}

	// 构建配置
	buildConfig := models.BuildConfig{
		Language: "python",
		Config: map[string]interface{}{
			"python_version":    "3.10",
			"package_manager":   "conda",
			"venv_type":         "conda",
			"venv_name":         "test_env",
			"requirements_file": "environment.yml",
			"start_command":     "python app.py",
			"conda_channels":    "conda-forge,defaults",
		},
	}

	// 序列化配置
	buildConfigJSON, err := json.Marshal(buildConfig)
	if err != nil {
		t.Fatalf("序列化构建配置失败: %v", err)
	}

	// 创建测试项目
	project := &models.Project{
		ID:             1,
		Name:           "python-test-project",
		Description:    "Python测试项目",
		GitURL:         "https://github.com/example/python-flask-example.git",
		GitVersion:     "branch:main",
		DeployType:     "local",
		Environment:    "test",
		ProjectType:    "backend",
		BuildConfig:    buildConfig,
		DeployPath:     deployPath,
		PostDeployShell: "echo 'Python项目部署完成'",
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// 创建示例Python应用
	createExamplePythonApp(t, deployPath)

	return project
}

// createExamplePythonApp 创建示例Python应用
func createExamplePythonApp(t *testing.T, deployPath string) {
	// 创建app.py
	appPath := filepath.Join(deployPath, "app.py")
	appContent := `
from flask import Flask
app = Flask(__name__)

@app.route('/')
def hello():
    return "Hello from GoShip Python Deployment!"

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
`
	err := os.WriteFile(appPath, []byte(appContent), 0644)
	if err != nil {
		t.Fatalf("创建app.py失败: %v", err)
	}

	// 创建environment.yml
	envPath := filepath.Join(deployPath, "environment.yml")
	envContent := `
name: test_env
channels:
  - conda-forge
  - defaults
dependencies:
  - python=3.10
  - flask=2.0.1
  - pip
  - pip:
    - gunicorn==20.1.0
`
	err = os.WriteFile(envPath, []byte(envContent), 0644)
	if err != nil {
		t.Fatalf("创建environment.yml失败: %v", err)
	}

	// 创建requirements.txt (备用)
	reqPath := filepath.Join(deployPath, "requirements.txt")
	reqContent := `
flask==2.0.1
gunicorn==20.1.0
`
	err = os.WriteFile(reqPath, []byte(reqContent), 0644)
	if err != nil {
		t.Fatalf("创建requirements.txt失败: %v", err)
	}

	// 创建.git目录以模拟Git仓库
	gitDir := filepath.Join(deployPath, ".git")
	err = os.MkdirAll(gitDir, 0755)
	if err != nil {
		t.Fatalf("创建.git目录失败: %v", err)
	}

	fmt.Println("示例Python应用创建成功:", deployPath)
}
