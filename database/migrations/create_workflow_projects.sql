-- 工作流项目表
-- 简化的项目基础信息，Git管理通过节点实现
CREATE TABLE workflow_projects (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL COMMENT '项目名称',
    environment ENUM('development', 'test', 'production') NOT NULL COMMENT '部署环境',
    language ENUM('go', 'python', 'vue', 'java', 'nodejs', 'react') NOT NULL COMMENT '编程语言',
    project_type ENUM('frontend', 'backend', 'fullstack') NOT NULL COMMENT '项目类型',
    description TEXT COMMENT '项目描述',
    workflow_config JSON COMMENT '工作流配置（存储节点和连接信息）',
    status ENUM('active', 'inactive', 'archived') DEFAULT 'active' COMMENT '项目状态',
    created_by BIGINT COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引优化
    INDEX idx_language (language),
    INDEX idx_environment (environment),
    INDEX idx_project_type (project_type),
    INDEX idx_status (status),
    INDEX idx_created_by (created_by),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工作流项目表';

-- 工作流项目权限表
CREATE TABLE workflow_project_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    role ENUM('owner', 'editor', 'viewer') NOT NULL COMMENT '权限角色',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 唯一约束：一个用户在一个项目中只能有一个角色
    UNIQUE KEY uk_user_project (user_id, project_id),
    
    -- 外键约束
    FOREIGN KEY (project_id) REFERENCES workflow_projects(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_project_id (project_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工作流项目权限表';

-- 工作流执行历史表（可选，用于记录执行历史）
CREATE TABLE workflow_executions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    project_id BIGINT NOT NULL COMMENT '项目ID',
    executor_id BIGINT NOT NULL COMMENT '执行者ID',
    status ENUM('pending', 'running', 'success', 'failed', 'cancelled') NOT NULL COMMENT '执行状态',
    start_time TIMESTAMP NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    duration INT COMMENT '执行时长（秒）',
    logs TEXT COMMENT '执行日志',
    error_message TEXT COMMENT '错误信息',
    workflow_snapshot JSON COMMENT '执行时的工作流快照',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (project_id) REFERENCES workflow_projects(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_project_id (project_id),
    INDEX idx_executor_id (executor_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工作流执行历史表';
