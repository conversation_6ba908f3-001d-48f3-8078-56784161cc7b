# 历史产物管理功能需求文档

## 1. 背景与目标

为提升项目交付的可追溯性、自动化和团队协作效率，系统需实现项目构建历史产物的自动管理、可视化与智能清理，支持灵活的保存策略和个性化产物保护。

---

## 2. 主要需求点

### 2.1 产物目录可配置
- 支持在配置文件（conf/app.conf）中通过 `artifact_path` 字段自定义产物保存根目录（如 `./artifact`）。
- 后端启动时自动读取该配置，所有产物操作均以此为根目录。

### 2.2 产物自动命名与存储
- 每次构建完成后，自动将产物保存到 `artifact_path/项目名/` 目录下。
- 产物命名规则：`项目名_日期_序号`（如 `小程序_2025_06_13_01`），同一天多次构建自动递增序号，确保唯一性和可追溯性。

### 2.3 产物元数据入库
- 每个产物生成后，自动将其元数据（项目ID、项目名、产物名、路径、生成时间、大小、星标、备注、所属分支/标签等）写入数据库 `artifacts` 表。
- 新增字段 `git_ref`，用于记录产物所属的分支或标签（如 branch:master 或 tag:v1.0.0）。
- 支持后续高效查询、分页、星标、备注、删除等操作。

### 2.4 数据库自动建表
- 系统启动时自动检测并创建 `artifacts` 表，无需手动干预，保证产物元数据存储的健壮性。

### 2.5 产物管理接口与前端展示
- 后端需提供产物的增、查、删、星标等接口，支持前端展示历史产物列表、下载、星标、备注等功能。
- 前端可按项目、时间、名称等多条件筛选和操作历史产物。

### 2.6 产物保存策略（项目级配置）
- 支持配置"最多保留天数"（按周期保存）和"最多保留产物数"（按最大数量保存），两者可单独或同时生效。
- 策略仅针对**非星标产物**，星标产物不受自动清理影响。

### 2.7 星标产物特殊规则
- 用户可在历史构建弹窗中对产物进行"星标/取消星标"操作。
- 星标产物不会被自动清理，无论是按天数还是数量。
- 星标产物不占用最大数量名额。例如最大数量为3，星标了n个产物，则非星标产物最多只保留3个，星标产物数量不受影响。

### 2.8 自动清理机制
- 系统定时任务或构建后触发清理逻辑。
- 清理时只考虑非星标产物，优先删除最早的非星标产物，直到满足天数和数量限制。
- 星标产物始终保留，且不计入最大数量。

---

## 3. 关键技术实现要点

- 配置、数据库、业务逻辑三层解耦，保证灵活性和可维护性。
- 产物命名、保存、入库、查询等流程自动化，减少人工干预。
- 产物目录、表结构、接口等均支持后续扩展（如多类型产物、构建来源、构建状态等）。

---

## 4. 目标效果

- 每个项目的历史构建产物都能被系统自动归档、命名、存储、入库。
- 团队成员可在前端便捷地浏览、检索、下载、管理所有历史产物。
- 重要产物可通过星标永久保留，普通产物则自动轮换，保证空间可控。
- 系统具备良好的可扩展性和可维护性，适应未来更多产物管理需求。

---

## 5. 后续可扩展方向

- 支持多类型产物（如二进制、压缩包、镜像等）
- 产物与构建流水线、CI/CD深度集成
- 产物多级归档、分布式存储、云端备份等

---
