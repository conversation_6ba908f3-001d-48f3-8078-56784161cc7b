---
description: 
globs: 
alwaysApply: true
---
# 项目架构说明

## 一、后端架构（Go）

- **主框架**：Beego v2
- **主要技术栈**：MySQL、gorilla/websocket、pkg/sftp、golang.org/x/crypto 等
- **入口文件**：[main.go](mdc:main.go)
- **分层结构**：
  - **controllers/**：控制器层，处理 HTTP 请求，调用服务层，返回响应。
  - **models/**：数据模型层，定义数据库结构与操作。
  - **services/**：服务层，封装业务逻辑，如构建、部署、Git 操作、WebSocket 通信等。
  - **utils/**：工具类，存放通用辅助函数。
  - **routers/**：路由配置，定义 URL 路径与控制器的映射。
  - **conf/**：配置文件目录，集中管理应用配置。
  - **artifact/**：构建产物目录，存放自动化构建输出。
  - **workspace/**：项目源码工作区，存放拉取的项目代码。
  - **static/**：静态资源目录。
  - **docs/**：文档目录。

- **架构特点**：
  - 分层清晰，职责单一，易于维护和扩展。
  - 控制器-服务-模型三层解耦，便于单元测试和功能复用。
  - 支持自动化构建与部署，artifact、workspace 目录实现产物和源码管理。
  - 支持 WebSocket 实时通信。
  - 配置集中管理，便于多环境切换。

## 二、前端架构（frontend）

- **主框架**：Vite
- **主要技术栈**：npm、ES6+、模块化开发
- **主要目录**：
  - **src/**：前端源码（页面、组件、服务等）
  - **public/**：公共静态资源
  - **dist/**：构建产物
  - **vite.config.js**：Vite 配置文件
  - **package.json**：依赖与脚本定义

- **架构特点**：
  - 前后端分离，前端独立开发、构建、部署。
  - 现代化工程化，支持模块化开发和高效构建。
  - 支持热更新，提升开发体验。
  - 易于集成后端 API。

## 三、整体架构优势

- 遵循主流的前后端分离、分层解耦、配置集中、自动化运维等最佳实践。
- 便于团队协作、功能扩展和后期维护。

如需详细了解某一模块，请参考对应目录下的代码文件。
