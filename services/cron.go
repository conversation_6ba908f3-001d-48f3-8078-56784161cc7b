package services

import (
	"GoShip/models"
	"fmt"
	"os"
	"time"

	"github.com/robfig/cron/v3"
)

// CronService 定时任务服务
type CronService struct {
	cron *cron.Cron
}

// NewCronService 创建定时任务服务
func NewCronService() *CronService {
	return &CronService{
		cron: cron.New(cron.WithSeconds()),
	}
}

// Start 启动定时任务
func (c *CronService) Start() {
	// 每天凌晨2点执行产物清理
	_, err := c.cron.AddFunc("0 0 2 * * *", c.CleanArtifacts)
	if err != nil {
		fmt.Printf("添加产物清理定时任务失败: %v\n", err)
		return
	}

	// 启动定时任务
	c.cron.Start()
	fmt.Println("定时任务服务已启动")
}

// Stop 停止定时任务
func (c *CronService) Stop() {
	if c.cron != nil {
		c.cron.Stop()
		fmt.Println("定时任务服务已停止")
	}
}

// CleanArtifacts 清理产物的定时任务
func (c *CronService) CleanArtifacts() {
	fmt.Println("开始执行定时产物清理任务...")

	// 获取所有项目
	projects, err := models.GetAllProjectsForCleanup()
	if err != nil {
		fmt.Printf("获取项目列表失败: %v\n", err)
		return
	}

	totalDeleted := 0
	for _, project := range projects {
		deleted := c.cleanProjectArtifacts(project)
		totalDeleted += deleted
	}

	fmt.Printf("定时产物清理任务完成，共删除 %d 个产物\n", totalDeleted)
}

// cleanProjectArtifacts 清理单个项目的产物
func (c *CronService) cleanProjectArtifacts(project *models.Project) int {
	maxDays := project.MaxArtifactDays
	maxCount := project.MaxArtifactCount

	// 如果没有设置清理规则，跳过
	if maxDays <= 0 && maxCount <= 0 {
		return 0
	}

	fmt.Printf("开始清理项目 [%s] 的产物...\n", project.Name)

	// 获取非星标产物
	artifacts, err := models.GetNonStarredArtifacts(project.ID)
	if err != nil {
		fmt.Printf("获取项目 [%s] 产物列表失败: %v\n", project.Name, err)
		return 0
	}

	if len(artifacts) == 0 {
		return 0
	}

	deletedCount := 0
	now := time.Now()

	// 1. 按天数清理
	if maxDays > 0 {
		cutoff := now.AddDate(0, 0, -maxDays)
		for _, artifact := range artifacts {
			if artifact.CreatedAt.Before(cutoff) {
				if c.deleteArtifact(artifact) {
					deletedCount++
					fmt.Printf("删除过期产物: %s (项目: %s)\n", artifact.ArtifactName, project.Name)
				}
			}
		}
	}

	// 2. 按数量清理
	if maxCount > 0 {
		// 重新获取未被天数清理后的非星标产物
		remainingArtifacts, err := models.GetNonStarredArtifacts(project.ID)
		if err == nil && len(remainingArtifacts) > maxCount {
			// 删除最旧的产物
			deleteCount := len(remainingArtifacts) - maxCount
			for i := 0; i < deleteCount && i < len(remainingArtifacts); i++ {
				if c.deleteArtifact(remainingArtifacts[i]) {
					deletedCount++
					fmt.Printf("删除旧产物: %s (项目: %s)\n", remainingArtifacts[i].ArtifactName, project.Name)
				}
			}
		}
	}

	if deletedCount > 0 {
		fmt.Printf("项目 [%s] 清理完成，删除了 %d 个产物\n", project.Name, deletedCount)
	}

	return deletedCount
}

// deleteArtifact 删除单个产物（文件和数据库记录）
func (c *CronService) deleteArtifact(artifact *models.Artifact) bool {
	// 删除物理文件
	if artifact.ArtifactPath != "" {
		if err := os.Remove(artifact.ArtifactPath); err != nil {
			fmt.Printf("删除产物文件失败 [%s]: %v\n", artifact.ArtifactPath, err)
			// 文件删除失败也继续删除数据库记录
		}
	}

	// 删除数据库记录
	if err := models.DeleteArtifactByID(artifact.ID); err != nil {
		fmt.Printf("删除产物数据库记录失败 [%s]: %v\n", artifact.ArtifactName, err)
		return false
	}

	return true
}
