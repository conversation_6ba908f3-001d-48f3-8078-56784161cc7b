package services

import (
	"GoShip/models"
	"bufio"
	"bytes"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"
)

// PostDeploy 通用部署后脚本执行
func PostDeploy(project *models.Project, artifactName string) error {
	if project.PostDeployShell == "" {
		return nil
	}
	if project.DeployType == "remote" {
		// 获取项目关联的主机配置
		host, err := project.GetProjectHost()
		if err != nil {
			SendDeployLog(project.ID, "post_deploy", "error", fmt.Sprintf("获取主机配置失败: %v", err))
			return err
		}

		SendDeployLog(project.ID, "post_deploy", "info", fmt.Sprintf("通过SSH连接远程服务器执行部署后脚本... (主机: %s)", host.Name))

		// 使用主机配置创建SSH客户端
		client, err := CreateSSHClientWithTimeout(host, 30*time.Second)
		if err != nil {
			SendDeployLog(project.ID, "post_deploy", "error", fmt.Sprintf("SSH连接失败: %v", err))
			return err
		}
		defer client.Close()
		SendDeployLog(project.ID, "post_deploy", "success", "SSH连接成功")
		session, err := client.NewSession()
		if err != nil {
			SendDeployLog(project.ID, "post_deploy", "error", fmt.Sprintf("创建SSH会话失败: %v", err))
			return err
		}
		defer session.Close()
		stdout, _ := session.StdoutPipe()
		stderr, _ := session.StderrPipe()
		logChan := make(chan logMsg, 100)
		go func() {
			for log := range logChan {
				SendDeployLog(project.ID, "post_deploy", log.Level, log.Msg)
			}
		}()
		go func() {
			scanner := bufio.NewScanner(stdout)
			for scanner.Scan() {
				logChan <- logMsg{Level: "info", Msg: scanner.Text()}
			}
		}()
		go func() {
			scanner := bufio.NewScanner(stderr)
			for scanner.Scan() {
				logChan <- logMsg{Level: "error", Msg: scanner.Text()}
			}
		}()
		// 添加重试机制处理 "Text file busy" 错误
		cmd := fmt.Sprintf("cd %s && export BUILD_ARTIFACT='%s' && %s", project.DeployPath, artifactName, project.PostDeployShell)

		// 重试执行部署后脚本，处理 "Text file busy" 错误
		maxRetries := 3
		retryDelay := 2 * time.Second

		for attempt := 1; attempt <= maxRetries; attempt++ {
			if attempt > 1 {
				logChan <- logMsg{Level: "info", Msg: fmt.Sprintf("第 %d 次重试执行部署后脚本...", attempt)}
				time.Sleep(retryDelay)
			}

			// 创建新的session用于重试
			if attempt > 1 {
				session.Close()
				session, err = client.NewSession()
				if err != nil {
					logChan <- logMsg{Level: "error", Msg: fmt.Sprintf("创建SSH会话失败: %v", err)}
					close(logChan)
					return err
				}
				// 重新设置stdout和stderr管道
				stdout, _ = session.StdoutPipe()
				stderr, _ = session.StderrPipe()
				go func() {
					scanner := bufio.NewScanner(stdout)
					for scanner.Scan() {
						logChan <- logMsg{Level: "info", Msg: scanner.Text()}
					}
				}()
				go func() {
					scanner := bufio.NewScanner(stderr)
					for scanner.Scan() {
						logChan <- logMsg{Level: "error", Msg: scanner.Text()}
					}
				}()
			}

			err := session.Run(cmd)
			if err == nil {
				// 成功执行
				break
			}

			// 检查是否是 "Text file busy" 错误
			errStr := err.Error()
			if strings.Contains(errStr, "Text file busy") || strings.Contains(errStr, "text file busy") {
				if attempt < maxRetries {
					logChan <- logMsg{Level: "warning", Msg: fmt.Sprintf("检测到 'Text file busy' 错误，%d秒后重试 (第%d/%d次)", int(retryDelay.Seconds()), attempt, maxRetries)}
					continue
				} else {
					logChan <- logMsg{Level: "error", Msg: "多次重试后仍然出现 'Text file busy' 错误，建议手动检查进程状态"}
				}
			}

			// 其他错误或最后一次重试失败
			if attempt == maxRetries {
				logChan <- logMsg{Level: "error", Msg: fmt.Sprintf("远程部署后脚本执行失败: %v", err)}
				close(logChan)
				return err
			}
		}
		logChan <- logMsg{Level: "success", Msg: "远程部署后脚本执行完成"}
		close(logChan)
		return nil
	}
	// 本地部署后脚本执行
	SendDeployLog(project.ID, "post_deploy", "start", "执行部署后脚本...")
	cmd := exec.Command("sh", "-c", project.PostDeployShell)
	cmd.Dir = project.DeployPath
	cmd.Env = append(os.Environ(), "BUILD_ARTIFACT="+artifactName)
	var output bytes.Buffer
	cmd.Stdout = &output
	cmd.Stderr = &output
	err := cmd.Run()
	if output.Len() > 0 {
		SendDeployLog(project.ID, "post_deploy", "info", output.String())
	}
	if err != nil {
		SendDeployLog(project.ID, "post_deploy", "error", fmt.Sprintf("部署后脚本执行失败: %v", err))
		return err
	}
	SendDeployLog(project.ID, "post_deploy", "success", "部署后脚本执行完成")
	return nil
}

type logMsg struct {
	Level string
	Msg   string
}
