package services

import (
	"GoShip/models"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"golang.org/x/crypto/ssh"
)

// PythonBuildStrategy Python项目部署策略
type PythonBuildStrategy struct {
	Project *models.Project
}

// setupLocalRepository 设置本地仓库
func (p *PythonBuildStrategy) setupLocalRepository() error {
	SendDeployLog(p.Project.ID, "deploy", "info", "检查本地部署目录...")

	// 检查部署目录是否存在
	if _, err := os.Stat(p.Project.DeployPath); os.IsNotExist(err) {
		// 目录不存在，创建并克隆
		SendDeployLog(p.Project.ID, "deploy", "info", "本地部署目录不存在，开始克隆项目...")

		// 创建父目录
		if err := os.MkdirAll(filepath.Dir(p.Project.DeployPath), 0755); err != nil {
			SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("创建本地目录失败: %v", err))
			return err
		}

		// 克隆项目
		gitSvc := NewGitService(p.Project.GitURL, p.Project.GitUsername, p.Project.GitPassword)
		gitSvc.GitVersion = p.Project.GitVersion
		if err := gitSvc.CloneOrPull(p.Project.DeployPath); err != nil {
			SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("克隆项目失败: %v", err))
			return err
		}

		SendDeployLog(p.Project.ID, "deploy", "success", "项目克隆完成")
	} else {
		// 目录存在，检查是否是Git仓库
		gitDir := filepath.Join(p.Project.DeployPath, ".git")
		if _, err := os.Stat(gitDir); os.IsNotExist(err) {
			SendDeployLog(p.Project.ID, "deploy", "error", "本地部署目录存在但不是Git仓库")
			return fmt.Errorf("本地部署目录存在但不是Git仓库")
		}
		SendDeployLog(p.Project.ID, "deploy", "info", "本地Git项目已存在")
	}

	return nil
}

// pullLatestCodeLocal 拉取最新代码到本地
func (p *PythonBuildStrategy) pullLatestCodeLocal() error {
	SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("开始在本地拉取最新代码，目标目录: %s", p.Project.DeployPath))

	gitSvc := NewGitService(p.Project.GitURL, p.Project.GitUsername, p.Project.GitPassword)
	gitSvc.GitVersion = p.Project.GitVersion
	if err := gitSvc.CloneOrPull(p.Project.DeployPath); err != nil {
		SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("本地代码拉取失败: %v", err))
		return err
	}

	SendDeployLog(p.Project.ID, "deploy", "success", fmt.Sprintf("本地代码拉取完成，目录: %s", p.Project.DeployPath))
	return nil
}

// prepareLocalEnvironment 准备本地Python环境
func (p *PythonBuildStrategy) prepareLocalEnvironment() error {
	SendDeployLog(p.Project.ID, "deploy", "info", "准备本地Python环境...")

	// 检查Python环境
	cmd := exec.Command("python3", "--version")
	if output, err := cmd.Output(); err != nil {
		SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("本地Python环境检查失败: %v", err))
		return err
	} else {
		SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("本地Python版本: %s", strings.TrimSpace(string(output))))
	}

	return nil
}

// installLocalDependencies 安装本地依赖
func (p *PythonBuildStrategy) installLocalDependencies() error {
	SendDeployLog(p.Project.ID, "deploy", "info", "安装本地依赖...")

	// 固定使用 requirements.txt 文件
	requirementsFile := "requirements.txt"

	// 检查依赖文件是否存在
	fullFilePath := filepath.Join(p.Project.DeployPath, requirementsFile)
	SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("检查本地依赖文件，完整路径: %s", fullFilePath))
	if _, err := os.Stat(fullFilePath); os.IsNotExist(err) {
		SendDeployLog(p.Project.ID, "deploy", "warning", fmt.Sprintf("本地依赖文件不存在，完整路径: %s", fullFilePath))
		return nil
	}
	SendDeployLog(p.Project.ID, "deploy", "success", fmt.Sprintf("本地依赖文件存在，路径: %s", fullFilePath))

	// 使用pip安装依赖
	return p.installLocalPipDependencies(requirementsFile)
}

// installLocalCondaDependencies 安装本地Conda依赖
func (p *PythonBuildStrategy) installLocalCondaDependencies(requirementsFile string) error {
	// 使用配置的conda环境名
	config := p.Project.BuildConfig.Config
	envName, _ := config["conda_env_name"].(string)

	if envName == "" {
		SendDeployLog(p.Project.ID, "deploy", "error", "未配置Conda环境名")
		return fmt.Errorf("未配置Conda环境名")
	}

	SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("使用Conda环境: %s", envName))

	// 检查环境是否存在
	var installCmd string
	checkEnvCmd := exec.Command("conda", "env", "list")
	if output, err := checkEnvCmd.Output(); err == nil && strings.Contains(string(output), envName) {
		// 环境存在，使用update
		installCmd = fmt.Sprintf("cd %s && conda env update -f %s --prune", p.Project.DeployPath, requirementsFile)
		SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("更新现有Conda环境(含清理): %s", envName))
	} else {
		// 环境不存在，使用create
		installCmd = fmt.Sprintf("cd %s && conda env create -f %s", p.Project.DeployPath, requirementsFile)
		SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("创建新的Conda环境: %s", envName))
	}

	cmd := exec.Command("sh", "-c", installCmd)
	var output bytes.Buffer
	cmd.Stdout = &output
	cmd.Stderr = &output

	if err := cmd.Run(); err != nil {
		SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("安装依赖失败: %v\n%s", err, output.String()))
		return err
	}

	SendDeployLog(p.Project.ID, "deploy", "success", "本地依赖安装完成")
	return nil
}

// installLocalPipDependencies 安装本地Pip依赖
func (p *PythonBuildStrategy) installLocalPipDependencies(requirementsFile string) error {
	installCmd := fmt.Sprintf("cd %s && pip3 install -r %s", p.Project.DeployPath, requirementsFile)
	SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("使用pip安装依赖: %s", requirementsFile))

	cmd := exec.Command("sh", "-c", installCmd)
	var output bytes.Buffer
	cmd.Stdout = &output
	cmd.Stderr = &output

	if err := cmd.Run(); err != nil {
		SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("安装依赖失败: %v\n%s", err, output.String()))
		return err
	}

	SendDeployLog(p.Project.ID, "deploy", "success", "本地依赖安装完成")
	return nil
}

// executeLocalStartCommand 执行本地启动命令
func (p *PythonBuildStrategy) executeLocalStartCommand() error {
	config := p.Project.BuildConfig.Config
	startCommand, _ := config["start_command"].(string)

	if startCommand == "" {
		SendDeployLog(p.Project.ID, "deploy", "info", "跳过启动脚本执行（未配置）")
		return nil
	}

	SendDeployLog(p.Project.ID, "deploy", "info", "开始执行本地启动脚本...")

	// 构建完整的启动命令
	condaEnvName, _ := config["conda_env_name"].(string)

	var fullCmd string
	if condaEnvName != "" {
		fullCmd = fmt.Sprintf("cd %s && conda activate %s && %s", p.Project.DeployPath, condaEnvName, startCommand)
		SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("使用Conda环境: %s", condaEnvName))
	} else {
		fullCmd = fmt.Sprintf("cd %s && %s", p.Project.DeployPath, startCommand)
		SendDeployLog(p.Project.ID, "deploy", "info", "使用系统默认Python环境")
	}

	// 执行启动脚本并获取输出
	SendDeployLog(p.Project.ID, "deploy", "info", "执行启动脚本...")

	// 检查脚本是否包含后台执行逻辑
	if strings.Contains(startCommand, "nohup") || strings.Contains(startCommand, "&") {
		// 脚本本身包含后台执行，直接执行
		cmd := exec.Command("sh", "-c", fullCmd)
		cmd.Dir = p.Project.DeployPath
		output, err := cmd.CombinedOutput()
		if err != nil {
			SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("启动脚本执行失败: %v", err))
			if len(output) > 0 {
				SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("脚本错误输出: %s", string(output)))
			}
			return err
		}

		// 显示脚本执行的输出
		if len(output) > 0 {
			lines := strings.Split(strings.TrimSpace(string(output)), "\n")
			for _, line := range lines {
				if strings.TrimSpace(line) != "" {
					SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("脚本输出: %s", strings.TrimSpace(line)))
				}
			}
		}
	} else {
		// 脚本不包含后台执行，我们添加nohup后台执行
		backgroundCmd := fmt.Sprintf("nohup %s > app.log 2>&1 &", fullCmd)
		SendDeployLog(p.Project.ID, "deploy", "info", "脚本不包含后台执行，添加nohup后台执行...")
		cmd := exec.Command("sh", "-c", backgroundCmd)
		cmd.Dir = p.Project.DeployPath

		if err := cmd.Start(); err != nil {
			SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("启动脚本执行失败: %v", err))
			return err
		}
	}

	// 等待一下，然后检查进程是否启动
	SendDeployLog(p.Project.ID, "deploy", "info", "等待3秒后检查应用启动状态...")
	time.Sleep(3 * time.Second)

	// 检查应用是否成功启动
	checkCmd := exec.Command("sh", "-c", fmt.Sprintf("cd %s && ps aux | grep -v grep | grep python || echo '未找到Python进程'", p.Project.DeployPath))
	if checkOutput, err := checkCmd.Output(); err == nil {
		SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("进程检查结果: %s", strings.TrimSpace(string(checkOutput))))
	}

	SendDeployLog(p.Project.ID, "deploy", "success", "本地启动脚本执行完成")
	return nil
}

// checkCondaEnvExists 检查Conda环境是否存在
func (p *PythonBuildStrategy) checkCondaEnvExists(envName string) bool {
	cmd := exec.Command("conda", "env", "list")
	output, err := cmd.Output()
	if err != nil {
		return false
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.HasPrefix(strings.TrimSpace(line), envName+" ") || strings.HasPrefix(strings.TrimSpace(line), envName+"\t") {
			return true
		}
	}
	return false
}

// extractRepoName 从Git URL中提取仓库名称
func (p *PythonBuildStrategy) extractRepoName(gitURL string) string {
	// 处理各种Git URL格式
	// https://pd.pixmoving.city:9876/pix/ai_voice_bot_server.git -> ai_voice_bot_server
	// **************:user/repo.git -> repo

	// 移除.git后缀
	repoURL := strings.TrimSuffix(gitURL, ".git")

	// 获取最后一个路径段
	parts := strings.Split(repoURL, "/")
	if len(parts) > 0 {
		return parts[len(parts)-1]
	}

	return "unknown"
}

// getActualDeployPath 获取实际的部署路径（基础路径 + 项目名称）
func (p *PythonBuildStrategy) getActualDeployPath() string {
	repoName := p.extractRepoName(p.Project.GitURL)
	return filepath.Join(p.Project.DeployPath, repoName)
}

// Build Python项目构建（Python项目不需要传统的构建阶段）
func (p *PythonBuildStrategy) Build(workDir string, buildNumber int) error {
	SendDeployLog(p.Project.ID, "build", "start", "开始Python项目构建...")

	// Python项目是解释型语言，不需要编译构建
	// 所有操作（代码拉取、环境准备、依赖安装）都在部署阶段的目标路径进行
	SendDeployLog(p.Project.ID, "build", "info", "Python项目无需构建阶段，所有操作将在部署阶段的目标路径执行")
	SendDeployLog(p.Project.ID, "build", "success", "Python项目构建完成（跳过构建阶段）")
	return nil
}

// Deploy Python项目部署
func (p *PythonBuildStrategy) Deploy(workDir string, buildNumber int) error {
	SendDeployLog(p.Project.ID, "deploy", "start", "开始Python项目部署...")

	if p.Project.DeployType == "remote" {
		SendDeployLog(p.Project.ID, "deploy", "info", "执行远程部署模式")
		return p.deployToRemote(buildNumber)
	} else {
		SendDeployLog(p.Project.ID, "deploy", "info", "执行本地部署模式")
		return p.deployToLocal(buildNumber)
	}
}

// checkPythonEnvironment 检查Python环境
func (p *PythonBuildStrategy) checkPythonEnvironment() error {
	config := p.Project.BuildConfig.Config
	pythonVersion, _ := config["python_version"].(string)
	packageManager, _ := config["package_manager"].(string)

	SendDeployLog(p.Project.ID, "build", "info", fmt.Sprintf("检查Python环境 - 版本: %s, 包管理器: %s", pythonVersion, packageManager))

	// 检查Python是否安装
	cmd := exec.Command("python3", "--version")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("Python3未安装或不在PATH中: %v", err)
	}
	SendDeployLog(p.Project.ID, "build", "info", fmt.Sprintf("Python版本: %s", strings.TrimSpace(string(output))))

	// 检查包管理器
	switch packageManager {
	case "conda":
		if err := p.checkConda(); err != nil {
			return err
		}
	case "poetry":
		if err := p.checkPoetry(); err != nil {
			return err
		}
	case "pip":
		if err := p.checkPip(); err != nil {
			return err
		}
	default:
		return fmt.Errorf("不支持的包管理器: %s", packageManager)
	}

	return nil
}

// checkConda 检查conda是否可用
func (p *PythonBuildStrategy) checkConda() error {
	cmd := exec.Command("conda", "--version")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("conda未安装或不在PATH中: %v", err)
	}
	SendDeployLog(p.Project.ID, "build", "info", fmt.Sprintf("Conda版本: %s", strings.TrimSpace(string(output))))
	return nil
}

// checkPoetry 检查poetry是否可用
func (p *PythonBuildStrategy) checkPoetry() error {
	cmd := exec.Command("poetry", "--version")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("poetry未安装或不在PATH中: %v", err)
	}
	SendDeployLog(p.Project.ID, "build", "info", fmt.Sprintf("Poetry版本: %s", strings.TrimSpace(string(output))))
	return nil
}

// checkPip 检查pip是否可用
func (p *PythonBuildStrategy) checkPip() error {
	cmd := exec.Command("pip3", "--version")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("pip3未安装或不在PATH中: %v", err)
	}
	SendDeployLog(p.Project.ID, "build", "info", fmt.Sprintf("Pip版本: %s", strings.TrimSpace(string(output))))
	return nil
}

// createVirtualEnvironment 创建虚拟环境
func (p *PythonBuildStrategy) createVirtualEnvironment(workDir string) error {
	config := p.Project.BuildConfig.Config
	requirementsFile, _ := config["requirements_file"].(string)

	// 如果是environment.yml文件，环境创建将在installDependencies中处理
	if strings.HasSuffix(requirementsFile, ".yml") || strings.HasSuffix(requirementsFile, ".yaml") {
		SendDeployLog(p.Project.ID, "build", "info", "使用environment.yml，环境将在依赖安装阶段创建")
		return nil
	}

	// 对于非yml文件，暂时跳过环境创建，在依赖安装时处理
	SendDeployLog(p.Project.ID, "build", "info", "跳过虚拟环境创建（将在依赖安装阶段处理）")
	return nil
}

// createCondaEnvironment 创建conda环境（智能版本）
func (p *PythonBuildStrategy) createCondaEnvironment(venvName, pythonVersion string) error {
	// 使用更精确的方法检查环境是否存在
	envExists, existingPythonVersion, err := p.checkCondaEnvironmentExists(venvName)
	if err != nil {
		return fmt.Errorf("检查conda环境失败: %v", err)
	}

	if envExists {
		SendDeployLog(p.Project.ID, "build", "info", fmt.Sprintf("Conda环境 %s 已存在 (Python %s)，跳过创建", venvName, existingPythonVersion))

		// 如果指定了Python版本且与现有环境不匹配，给出警告
		if pythonVersion != "" && !strings.HasPrefix(existingPythonVersion, pythonVersion) {
			SendDeployLog(p.Project.ID, "build", "warning",
				fmt.Sprintf("现有环境Python版本 (%s) 与配置版本 (%s) 不匹配，将使用现有环境",
					existingPythonVersion, pythonVersion))
		}
		return nil
	}

	// 环境不存在，需要创建新环境
	if pythonVersion == "" {
		return fmt.Errorf("创建新conda环境时必须指定Python版本")
	}

	SendDeployLog(p.Project.ID, "build", "info", fmt.Sprintf("创建新的Conda环境: %s (Python %s)", venvName, pythonVersion))

	// 创建新环境
	cmd := exec.Command("conda", "create", "-n", venvName, fmt.Sprintf("python=%s", pythonVersion), "-y")
	var cmdOutput bytes.Buffer
	cmd.Stdout = &cmdOutput
	cmd.Stderr = &cmdOutput

	if err := cmd.Run(); err != nil {
		SendDeployLog(p.Project.ID, "build", "error", cmdOutput.String())
		return fmt.Errorf("创建conda环境失败: %v", err)
	}

	SendDeployLog(p.Project.ID, "build", "success", fmt.Sprintf("Conda环境 %s 创建成功", venvName))
	return nil
}

// checkCondaEnvironmentExists 检查conda环境是否存在并返回Python版本
func (p *PythonBuildStrategy) checkCondaEnvironmentExists(venvName string) (bool, string, error) {
	// 使用JSON格式获取环境列表，更准确
	cmd := exec.Command("conda", "env", "list", "--json")
	output, err := cmd.Output()
	if err != nil {
		return false, "", err
	}

	var envList struct {
		Envs []string `json:"envs"`
	}

	if err := json.Unmarshal(output, &envList); err != nil {
		// 如果JSON解析失败，回退到文本解析
		return strings.Contains(string(output), venvName), "", nil
	}

	// 检查环境是否存在
	for _, envPath := range envList.Envs {
		envName := filepath.Base(envPath)
		if envName == venvName {
			// 环境存在，获取Python版本
			pythonVersion, err := p.getCondaEnvironmentPythonVersion(venvName)
			if err != nil {
				// 即使获取版本失败，仍然返回环境存在
				return true, "unknown", nil
			}
			return true, pythonVersion, nil
		}
	}

	return false, "", nil
}

// getCondaEnvironmentPythonVersion 获取conda环境中的Python版本
func (p *PythonBuildStrategy) getCondaEnvironmentPythonVersion(venvName string) (string, error) {
	cmd := exec.Command("conda", "list", "-n", venvName, "python", "--json")
	output, err := cmd.Output()
	if err != nil {
		return "", err
	}

	var packages []struct {
		Name    string `json:"name"`
		Version string `json:"version"`
	}

	if err := json.Unmarshal(output, &packages); err != nil {
		return "", err
	}

	for _, pkg := range packages {
		if pkg.Name == "python" {
			return pkg.Version, nil
		}
	}

	return "", fmt.Errorf("Python package not found in environment")
}

// createVenvEnvironment 创建venv环境
func (p *PythonBuildStrategy) createVenvEnvironment(workDir, venvName string) error {
	venvPath := filepath.Join(workDir, venvName)

	// 检查环境是否已存在
	if _, err := os.Stat(venvPath); err == nil {
		SendDeployLog(p.Project.ID, "build", "info", fmt.Sprintf("Venv环境 %s 已存在，跳过创建", venvName))
		return nil
	}

	cmd := exec.Command("python3", "-m", "venv", venvPath)
	var output bytes.Buffer
	cmd.Stdout = &output
	cmd.Stderr = &output

	if err := cmd.Run(); err != nil {
		SendDeployLog(p.Project.ID, "build", "error", output.String())
		return fmt.Errorf("创建venv环境失败: %v", err)
	}

	SendDeployLog(p.Project.ID, "build", "success", fmt.Sprintf("Venv环境 %s 创建成功", venvName))
	return nil
}

// createVirtualenvEnvironment 创建virtualenv环境
func (p *PythonBuildStrategy) createVirtualenvEnvironment(workDir, venvName string) error {
	venvPath := filepath.Join(workDir, venvName)

	// 检查环境是否已存在
	if _, err := os.Stat(venvPath); err == nil {
		SendDeployLog(p.Project.ID, "build", "info", fmt.Sprintf("Virtualenv环境 %s 已存在，跳过创建", venvName))
		return nil
	}

	cmd := exec.Command("virtualenv", venvPath)
	var output bytes.Buffer
	cmd.Stdout = &output
	cmd.Stderr = &output

	if err := cmd.Run(); err != nil {
		SendDeployLog(p.Project.ID, "build", "error", output.String())
		return fmt.Errorf("创建virtualenv环境失败: %v", err)
	}

	SendDeployLog(p.Project.ID, "build", "success", fmt.Sprintf("Virtualenv环境 %s 创建成功", venvName))
	return nil
}

// installDependencies 安装依赖
func (p *PythonBuildStrategy) installDependencies(workDir string) error {
	// 固定使用 requirements.txt 文件
	requirementsFile := "requirements.txt"

	requirementsPath := filepath.Join(workDir, requirementsFile)
	if _, err := os.Stat(requirementsPath); os.IsNotExist(err) {
		SendDeployLog(p.Project.ID, "build", "warning", fmt.Sprintf("依赖文件不存在: %s", requirementsFile))
		return nil
	}

	SendDeployLog(p.Project.ID, "build", "info", fmt.Sprintf("安装依赖: %s (使用 pip)", requirementsFile))

	// 固定使用pip安装依赖
	return p.installPipDependencies(workDir, requirementsFile)
}

// installCondaDependencies 使用conda安装依赖（仅用于本地部署）
func (p *PythonBuildStrategy) installCondaDependencies(workDir, requirementsFile string) error {
	// 检查依赖文件是否存在
	filePath := filepath.Join(workDir, requirementsFile)
	SendDeployLog(p.Project.ID, "build", "info", fmt.Sprintf("检查本地构建目录依赖文件，完整路径: %s", filePath))
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		SendDeployLog(p.Project.ID, "build", "warning", fmt.Sprintf("本地构建目录依赖文件不存在，完整路径: %s", filePath))
		return nil
	}
	SendDeployLog(p.Project.ID, "build", "success", fmt.Sprintf("本地构建目录依赖文件存在，路径: %s", filePath))

	// 使用配置的conda环境名
	config := p.Project.BuildConfig.Config
	envName, _ := config["conda_env_name"].(string)

	if envName == "" {
		SendDeployLog(p.Project.ID, "build", "error", "未配置Conda环境名")
		return fmt.Errorf("未配置Conda环境名")
	}

	SendDeployLog(p.Project.ID, "build", "info", fmt.Sprintf("使用Conda环境: %s", envName))

	var cmd *exec.Cmd
	if strings.HasSuffix(requirementsFile, ".yml") || strings.HasSuffix(requirementsFile, ".yaml") {

		// 检查环境是否存在
		if p.checkCondaEnvExists(envName) {
			// 环境存在，使用update，默认启用prune
			args := []string{"env", "update", "-f", requirementsFile, "--prune"}
			cmd = exec.Command("conda", args...)
			SendDeployLog(p.Project.ID, "build", "info", fmt.Sprintf("环境已存在，执行更新(含清理): conda %s", strings.Join(args, " ")))
		} else {
			// 环境不存在，使用create
			args := []string{"env", "create", "-f", requirementsFile}
			cmd = exec.Command("conda", args...)
			SendDeployLog(p.Project.ID, "build", "info", fmt.Sprintf("创建新环境: conda %s", strings.Join(args, " ")))
		}
	} else {
		// 使用requirements.txt文件（简化处理，不支持自定义频道）
		// 需要环境名称，从Git仓库名生成
		repoName := p.extractRepoName(p.Project.GitURL)
		envName := fmt.Sprintf("%s_env", repoName)
		cmd = exec.Command("conda", "install", "-n", envName, "--file", requirementsFile, "-y")
		SendDeployLog(p.Project.ID, "build", "info", fmt.Sprintf("使用requirements.txt安装到环境: %s", envName))
	}

	cmd.Dir = workDir
	var output bytes.Buffer
	cmd.Stdout = &output
	cmd.Stderr = &output

	if err := cmd.Run(); err != nil {
		SendDeployLog(p.Project.ID, "build", "error", output.String())
		return fmt.Errorf("conda安装依赖失败: %v", err)
	}

	SendDeployLog(p.Project.ID, "build", "success", "Conda依赖安装完成")
	return nil
}

// installPoetryDependencies 使用poetry安装依赖（仅用于本地部署）
func (p *PythonBuildStrategy) installPoetryDependencies(workDir string) error {
	// 检查pyproject.toml文件是否存在
	filePath := filepath.Join(workDir, "pyproject.toml")
	SendDeployLog(p.Project.ID, "build", "info", fmt.Sprintf("检查本地构建目录Poetry配置文件，完整路径: %s", filePath))
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		SendDeployLog(p.Project.ID, "build", "warning", fmt.Sprintf("本地构建目录Poetry配置文件不存在，完整路径: %s", filePath))
		return nil
	}
	SendDeployLog(p.Project.ID, "build", "success", fmt.Sprintf("本地构建目录Poetry配置文件存在，路径: %s", filePath))

	cmd := exec.Command("poetry", "install")
	cmd.Dir = workDir
	var output bytes.Buffer
	cmd.Stdout = &output
	cmd.Stderr = &output

	if err := cmd.Run(); err != nil {
		SendDeployLog(p.Project.ID, "build", "error", output.String())
		return fmt.Errorf("poetry安装依赖失败: %v", err)
	}

	SendDeployLog(p.Project.ID, "build", "success", "Poetry依赖安装完成")
	return nil
}

// installPipDependencies 使用pip安装依赖（仅用于本地部署）
func (p *PythonBuildStrategy) installPipDependencies(workDir, requirementsFile string) error {
	// 检查依赖文件是否存在
	filePath := filepath.Join(workDir, requirementsFile)
	SendDeployLog(p.Project.ID, "build", "info", fmt.Sprintf("检查本地构建目录依赖文件，完整路径: %s", filePath))
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		SendDeployLog(p.Project.ID, "build", "warning", fmt.Sprintf("本地构建目录依赖文件不存在，完整路径: %s", filePath))
		return nil
	}
	SendDeployLog(p.Project.ID, "build", "success", fmt.Sprintf("本地构建目录依赖文件存在，路径: %s", filePath))

	config := p.Project.BuildConfig.Config
	venvType, _ := config["venv_type"].(string)
	venvName, _ := config["venv_name"].(string)

	var cmd *exec.Cmd
	if venvType == "venv" || venvType == "virtualenv" {
		// 激活虚拟环境并安装
		venvPath := filepath.Join(workDir, venvName)
		pipPath := filepath.Join(venvPath, "bin", "pip")
		if _, err := os.Stat(pipPath); os.IsNotExist(err) {
			// Windows系统
			pipPath = filepath.Join(venvPath, "Scripts", "pip.exe")
		}
		cmd = exec.Command(pipPath, "install", "-r", requirementsFile)
	} else {
		// 使用系统pip
		cmd = exec.Command("pip3", "install", "-r", requirementsFile)
	}

	cmd.Dir = workDir
	var output bytes.Buffer
	cmd.Stdout = &output
	cmd.Stderr = &output

	if err := cmd.Run(); err != nil {
		SendDeployLog(p.Project.ID, "build", "error", output.String())
		return fmt.Errorf("pip安装依赖失败: %v", err)
	}

	SendDeployLog(p.Project.ID, "build", "success", "Pip依赖安装完成")
	return nil
}

// deployToLocal 本地部署
func (p *PythonBuildStrategy) deployToLocal(buildNumber int) error {
	SendDeployLog(p.Project.ID, "deploy", "start", fmt.Sprintf("开始本地部署到目标路径: %s", p.Project.DeployPath))

	// 1. 检查或克隆项目到部署路径
	if err := p.setupLocalRepository(); err != nil {
		return err
	}

	// 2. 拉取最新代码
	if err := p.pullLatestCodeLocal(); err != nil {
		return err
	}

	// 3. 准备本地Python环境
	if err := p.prepareLocalEnvironment(); err != nil {
		return err
	}

	// 4. 安装本地依赖
	if err := p.installLocalDependencies(); err != nil {
		return err
	}

	// 5. 执行本地启动脚本
	if err := p.executeLocalStartCommand(); err != nil {
		return err
	}

	SendDeployLog(p.Project.ID, "deploy", "success", "本地部署完成")
	return nil
}

// deployToRemote 远程部署
func (p *PythonBuildStrategy) deployToRemote(buildNumber int) error {
	// 计算实际部署路径
	actualDeployPath := p.getActualDeployPath()
	SendDeployLog(p.Project.ID, "deploy", "start", fmt.Sprintf("开始远程部署，基础路径: %s", p.Project.DeployPath))
	SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("项目名称: %s", p.extractRepoName(p.Project.GitURL)))
	SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("实际部署路径: %s", actualDeployPath))

	// 获取主机配置
	host, err := p.Project.GetProjectHost()
	if err != nil {
		SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("获取主机配置失败: %v", err))
		return err
	}

	SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("使用主机: %s (%s:%d)", host.Name, host.IP, host.Port))

	// 远程部署的具体实现
	return p.executeRemoteDeployment(host)
}

// executeStartCommand 执行启动命令
func (p *PythonBuildStrategy) executeStartCommand() error {
	config := p.Project.BuildConfig.Config
	startCommand, _ := config["start_command"].(string)
	condaEnvName, _ := config["conda_env_name"].(string)

	if startCommand == "" {
		SendDeployLog(p.Project.ID, "deploy", "info", "跳过启动脚本执行（未配置）")
		return nil
	}

	SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("执行启动命令: %s", startCommand))

	var fullCmd string
	if condaEnvName != "" {
		// 激活Conda环境后执行启动命令
		fullCmd = fmt.Sprintf("conda activate %s && %s", condaEnvName, startCommand)
		SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("在Conda环境 %s 中执行启动命令", condaEnvName))
	} else {
		fullCmd = startCommand
		SendDeployLog(p.Project.ID, "deploy", "info", "使用系统默认Python环境执行启动命令")
	}

	// 在部署目录中执行启动命令
	cmd := exec.Command("sh", "-c", fullCmd)
	cmd.Dir = p.Project.DeployPath

	var output bytes.Buffer
	cmd.Stdout = &output
	cmd.Stderr = &output

	// 启动命令通常是后台运行，所以我们不等待它完成
	if err := cmd.Start(); err != nil {
		SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("启动命令执行失败: %v", err))
		return err
	}

	SendDeployLog(p.Project.ID, "deploy", "success", "启动命令已执行")
	return nil
}

// executeRemoteDeployment 执行远程部署
func (p *PythonBuildStrategy) executeRemoteDeployment(host *models.Host) error {
	// 创建SSH客户端
	client, err := CreateSSHClientWithTimeout(host, 30*time.Second)
	if err != nil {
		SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("SSH连接失败: %v", err))
		return err
	}
	defer client.Close()

	// 1. 检查远程目录是否存在Git项目
	if err := p.checkOrCloneRemoteRepository(client); err != nil {
		return err
	}

	// 2. 拉取最新代码
	if err := p.pullLatestCode(client); err != nil {
		return err
	}

	// 3. 远程环境准备
	if err := p.prepareRemoteEnvironment(client); err != nil {
		return err
	}

	// 4. 安装远程依赖
	if err := p.installRemoteDependencies(client); err != nil {
		return err
	}

	// 5. 执行远程启动脚本
	if err := p.executeRemoteStartCommand(client); err != nil {
		return err
	}

	SendDeployLog(p.Project.ID, "deploy", "success", "远程部署完成")
	return nil
}

// checkOrCloneRemoteRepository 检查或克隆远程仓库
func (p *PythonBuildStrategy) checkOrCloneRemoteRepository(client *ssh.Client) error {
	// 获取实际部署路径
	actualDeployPath := p.getActualDeployPath()
	SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("检查远程Git项目，实际部署路径: %s", actualDeployPath))

	// 检查实际部署目录是否存在
	checkCmd := fmt.Sprintf("test -d %s", actualDeployPath)
	SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("执行远程检查命令: %s", checkCmd))
	_, err := ExecuteSSHCommand(client, checkCmd)

	if err != nil {
		// 目录不存在，创建并克隆
		SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("远程目录不存在，开始克隆项目到: %s", actualDeployPath))

		// 确保基础目录存在
		mkdirCmd := fmt.Sprintf("mkdir -p %s", p.Project.DeployPath)
		SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("确保基础目录存在: %s", p.Project.DeployPath))
		if _, err := ExecuteSSHCommand(client, mkdirCmd); err != nil {
			SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("创建基础目录失败: %v", err))
			return err
		}

		// 克隆项目到基础目录
		cloneCmd := fmt.Sprintf("cd %s && git clone %s", p.Project.DeployPath, p.getAuthGitURL())
		SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("执行克隆命令: %s", cloneCmd))
		if output, err := ExecuteSSHCommand(client, cloneCmd); err != nil {
			SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("克隆项目失败: %v\n%s", err, output))
			return err
		}

		SendDeployLog(p.Project.ID, "deploy", "success", fmt.Sprintf("项目克隆完成，路径: %s", actualDeployPath))
	} else {
		// 目录存在，检查是否是Git仓库
		gitDir := filepath.Join(actualDeployPath, ".git")
		gitCheckCmd := fmt.Sprintf("test -d %s", gitDir)
		SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("检查Git仓库目录: %s", gitDir))
		if _, err := ExecuteSSHCommand(client, gitCheckCmd); err != nil {
			// 目录存在但不是Git仓库，删除并重新克隆
			SendDeployLog(p.Project.ID, "deploy", "warning", fmt.Sprintf("目录存在但不是Git仓库，删除并重新克隆: %s", actualDeployPath))

			// 删除现有目录
			removeCmd := fmt.Sprintf("rm -rf %s", actualDeployPath)
			SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("删除现有目录: %s", removeCmd))
			if _, err := ExecuteSSHCommand(client, removeCmd); err != nil {
				SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("删除目录失败: %v", err))
				return err
			}

			// 确保基础目录存在
			mkdirCmd := fmt.Sprintf("mkdir -p %s", p.Project.DeployPath)
			if _, err := ExecuteSSHCommand(client, mkdirCmd); err != nil {
				SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("创建基础目录失败: %v", err))
				return err
			}

			// 重新克隆项目
			cloneCmd := fmt.Sprintf("cd %s && git clone %s", p.Project.DeployPath, p.getAuthGitURL())
			SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("重新克隆项目: %s", cloneCmd))
			if output, err := ExecuteSSHCommand(client, cloneCmd); err != nil {
				SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("重新克隆项目失败: %v\n%s", err, output))
				return err
			}

			SendDeployLog(p.Project.ID, "deploy", "success", fmt.Sprintf("项目重新克隆完成，路径: %s", actualDeployPath))
		} else {
			SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("远程Git项目已存在，路径: %s", actualDeployPath))
		}
	}

	return nil
}

// pullLatestCode 拉取最新代码
func (p *PythonBuildStrategy) pullLatestCode(client *ssh.Client) error {
	actualDeployPath := p.getActualDeployPath()
	SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("开始在远程主机拉取最新代码，目标目录: %s", actualDeployPath))

	// 解析版本信息
	versionType := "branch"
	versionName := "master"

	if p.Project.GitVersion != "" {
		parts := strings.SplitN(p.Project.GitVersion, ":", 2)
		if len(parts) == 2 {
			versionType = parts[0]
			versionName = parts[1]
		}
	}

	// 丢弃本地更改并拉取最新代码
	commands := []string{
		fmt.Sprintf("cd %s", actualDeployPath),
		"git fetch --all",
		"git reset --hard HEAD",
		"git clean -fd",
	}

	if versionType == "tag" {
		commands = append(commands, fmt.Sprintf("git checkout tags/%s -f", versionName))
	} else {
		commands = append(commands,
			fmt.Sprintf("git checkout %s -f", versionName),
			fmt.Sprintf("git reset --hard origin/%s", versionName),
		)
	}

	fullCmd := strings.Join(commands, " && ")
	if output, err := ExecuteSSHCommand(client, fullCmd); err != nil {
		SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("远程主机代码拉取失败: %v\n%s", err, output))
		return err
	}

	SendDeployLog(p.Project.ID, "deploy", "success", fmt.Sprintf("远程主机代码拉取完成，目录: %s", actualDeployPath))
	return nil
}

// prepareRemoteEnvironment 准备远程环境
func (p *PythonBuildStrategy) prepareRemoteEnvironment(client *ssh.Client) error {
	SendDeployLog(p.Project.ID, "deploy", "info", "准备远程Python环境...")

	config := p.Project.BuildConfig.Config
	condaEnvName, _ := config["conda_env_name"].(string)
	pythonVersion, _ := config["python_version"].(string)

	// 检查Conda环境
	if output, err := ExecuteSSHCommand(client, "conda --version"); err != nil {
		SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("远程conda未安装: %v", err))
		return err
	} else {
		SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("远程Conda版本: %s", strings.TrimSpace(output)))
	}

	// 检查指定的conda环境是否存在
	if condaEnvName != "" {
		checkEnvCmd := fmt.Sprintf("conda env list | grep '^%s '", condaEnvName)
		if _, err := ExecuteSSHCommand(client, checkEnvCmd); err != nil {
			// 环境不存在，创建新环境
			SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("Conda环境 %s 不存在，开始创建...", condaEnvName))

			var createCmd string
			if pythonVersion != "" {
				createCmd = fmt.Sprintf("conda create -n %s python=%s -y", condaEnvName, pythonVersion)
			} else {
				createCmd = fmt.Sprintf("conda create -n %s python -y", condaEnvName)
			}

			if output, err := ExecuteSSHCommand(client, createCmd); err != nil {
				SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("创建Conda环境失败: %v\n%s", err, output))
				return err
			}
			SendDeployLog(p.Project.ID, "deploy", "success", fmt.Sprintf("Conda环境 %s 创建成功", condaEnvName))
		} else {
			SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("Conda环境 %s 已存在", condaEnvName))
		}

		// 检查环境中的Python版本
		pythonCheckCmd := fmt.Sprintf("conda activate %s && python --version", condaEnvName)
		if output, err := ExecuteSSHCommand(client, pythonCheckCmd); err == nil {
			SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("环境 %s 中的Python版本: %s", condaEnvName, strings.TrimSpace(output)))
		}
	} else {
		SendDeployLog(p.Project.ID, "deploy", "warning", "未指定Conda环境名，将使用base环境")
	}

	return nil
}

// getAuthGitURL 获取带认证信息的Git URL
func (p *PythonBuildStrategy) getAuthGitURL() string {
	if p.Project.GitUsername != "" && p.Project.GitPassword != "" {
		// 处理HTTPS URL
		if strings.HasPrefix(p.Project.GitURL, "https://") {
			url := strings.TrimPrefix(p.Project.GitURL, "https://")
			return fmt.Sprintf("https://%s:%s@%s", p.Project.GitUsername, p.Project.GitPassword, url)
		}
	}
	return p.Project.GitURL
}

// createRemoteVirtualEnvironment 创建远程虚拟环境
func (p *PythonBuildStrategy) createRemoteVirtualEnvironment(client *ssh.Client, venvType, venvName string) error {
	SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("创建远程虚拟环境: %s (类型: %s)", venvName, venvType))

	switch venvType {
	case "conda":
		// 检查conda环境是否存在
		checkCmd := "conda env list"
		if output, err := ExecuteSSHCommand(client, checkCmd); err != nil {
			return fmt.Errorf("检查conda环境失败: %v", err)
		} else if strings.Contains(output, venvName) {
			SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("Conda环境 %s 已存在", venvName))
			return nil
		}

		// 创建conda环境
		config := p.Project.BuildConfig.Config
		pythonVersion, _ := config["python_version"].(string)
		var createCmd string
		if pythonVersion != "" {
			createCmd = fmt.Sprintf("conda create -n %s python=%s -y", venvName, pythonVersion)
		} else {
			createCmd = fmt.Sprintf("conda create -n %s python -y", venvName)
		}

		if output, err := ExecuteSSHCommand(client, createCmd); err != nil {
			SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("创建conda环境失败: %v\n%s", err, output))
			return err
		}

	case "venv", "virtualenv":
		venvPath := fmt.Sprintf("%s/%s", p.Project.DeployPath, venvName)
		checkCmd := fmt.Sprintf("test -d %s", venvPath)

		if _, err := ExecuteSSHCommand(client, checkCmd); err == nil {
			SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("虚拟环境 %s 已存在", venvName))
			return nil
		}

		var createCmd string
		if venvType == "venv" {
			createCmd = fmt.Sprintf("cd %s && python3 -m venv %s", p.Project.DeployPath, venvName)
		} else {
			createCmd = fmt.Sprintf("cd %s && virtualenv %s", p.Project.DeployPath, venvName)
		}

		if output, err := ExecuteSSHCommand(client, createCmd); err != nil {
			SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("创建虚拟环境失败: %v\n%s", err, output))
			return err
		}
	}

	SendDeployLog(p.Project.ID, "deploy", "success", fmt.Sprintf("远程虚拟环境 %s 创建完成", venvName))
	return nil
}

// installRemoteDependencies 安装远程依赖
func (p *PythonBuildStrategy) installRemoteDependencies(client *ssh.Client) error {
	SendDeployLog(p.Project.ID, "deploy", "info", "开始安装远程依赖...")

	// 固定使用 requirements.txt 文件
	requirementsFile := "requirements.txt"

	actualDeployPath := p.getActualDeployPath()
	fullFilePath := fmt.Sprintf("%s/%s", actualDeployPath, requirementsFile)

	// 检查远程依赖文件是否存在
	checkFileCmd := fmt.Sprintf("test -f %s", fullFilePath)
	if _, err := ExecuteSSHCommand(client, checkFileCmd); err != nil {
		SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("远程依赖文件不存在: %s", fullFilePath))
		return fmt.Errorf("远程依赖文件不存在: %s", fullFilePath)
	}

	SendDeployLog(p.Project.ID, "deploy", "success", fmt.Sprintf("远程依赖文件存在，路径: %s", fullFilePath))

	// 使用pip安装依赖
	return p.installRemotePipDependencies(client, requirementsFile)
}

// executeRemoteStartCommand 执行远程启动命令
func (p *PythonBuildStrategy) executeRemoteStartCommand(client *ssh.Client) error {
	config := p.Project.BuildConfig.Config
	startCommand, _ := config["start_command"].(string)
	condaEnvName, _ := config["conda_env_name"].(string)

	if startCommand == "" {
		SendDeployLog(p.Project.ID, "deploy", "info", "跳过启动脚本执行（未配置）")
		return nil
	}

	SendDeployLog(p.Project.ID, "deploy", "info", "开始执行远程启动脚本...")

	actualDeployPath := p.getActualDeployPath()
	var fullCmd string

	// 如果配置了conda环境名，使用conda activate
	if condaEnvName != "" {
		fullCmd = fmt.Sprintf("cd %s && conda activate %s && %s", actualDeployPath, condaEnvName, startCommand)
		SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("使用Conda环境: %s", condaEnvName))
	} else {
		// 直接执行
		fullCmd = fmt.Sprintf("cd %s && %s", actualDeployPath, startCommand)
		SendDeployLog(p.Project.ID, "deploy", "info", "使用系统默认Python环境")
	}

	// 执行启动脚本并获取输出
	SendDeployLog(p.Project.ID, "deploy", "info", "执行启动脚本...")

	// 对于您的脚本，我们需要特殊处理：脚本最后的python命令是前台运行的
	// 我们将脚本修改为后台执行，这样可以看到echo输出并且不会阻塞

	// 检查脚本最后一行是否是前台python命令
	lines := strings.Split(strings.TrimSpace(startCommand), "\n")
	lastLine := ""
	if len(lines) > 0 {
		lastLine = strings.TrimSpace(lines[len(lines)-1])
	}

	// 检查最后一行是否是前台python命令（不包含nohup或&）
	isLastLinePython := strings.HasPrefix(lastLine, "python ") &&
		!strings.Contains(lastLine, "nohup") &&
		!strings.Contains(lastLine, "&")

	SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("脚本最后一行: %s", lastLine))
	SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("检测结果: 是否为前台Python命令 = %v", isLastLinePython))

	if isLastLinePython {
		// 脚本包含前台python命令，需要修改为后台执行
		SendDeployLog(p.Project.ID, "deploy", "info", "检测到脚本包含前台Python命令，将修改为后台执行...")

		// 将最后一行的python命令替换为后台执行
		lines[len(lines)-1] = fmt.Sprintf("nohup %s > app.log 2>&1 &", lastLine)
		lines = append(lines, "echo \"Python应用已在后台启动\"")
		modifiedCmd := strings.Join(lines, "\n")

		modifiedFullCmd := ""
		if condaEnvName != "" {
			modifiedFullCmd = fmt.Sprintf("cd %s && conda activate %s && %s", actualDeployPath, condaEnvName, modifiedCmd)
		} else {
			modifiedFullCmd = fmt.Sprintf("cd %s && %s", actualDeployPath, modifiedCmd)
		}

		// 执行修改后的脚本，设置15秒超时（足够看到echo输出）
		output, err := p.executeSSHCommandWithTimeout(client, modifiedFullCmd, 15*time.Second)
		if err != nil {
			SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("启动脚本执行失败: %v", err))
			if output != "" {
				SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("脚本错误输出: %s", output))
			}
			return err
		}

		// 显示脚本执行的输出
		if output != "" {
			lines := strings.Split(strings.TrimSpace(output), "\n")
			for _, line := range lines {
				if strings.TrimSpace(line) != "" {
					SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("脚本输出: %s", strings.TrimSpace(line)))
				}
			}
		}
	} else if strings.Contains(lastLine, "nohup") || strings.Contains(lastLine, "&") {
		// 脚本本身包含后台执行逻辑
		SendDeployLog(p.Project.ID, "deploy", "info", "检测到脚本包含后台执行逻辑，设置30秒超时...")
		output, err := p.executeSSHCommandWithTimeout(client, fullCmd, 30*time.Second)
		if err != nil {
			SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("启动脚本执行失败: %v", err))
			if output != "" {
				SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("脚本错误输出: %s", output))
			}
			return err
		}

		// 显示脚本执行的输出
		if output != "" {
			lines := strings.Split(strings.TrimSpace(output), "\n")
			for _, line := range lines {
				if strings.TrimSpace(line) != "" {
					SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("脚本输出: %s", strings.TrimSpace(line)))
				}
			}
		}
	} else {
		// 其他情况，添加nohup后台执行
		backgroundCmd := fmt.Sprintf("nohup %s > app.log 2>&1 &", fullCmd)
		SendDeployLog(p.Project.ID, "deploy", "info", "脚本不包含后台执行，添加nohup后台执行...")
		output, err := ExecuteSSHCommand(client, backgroundCmd)
		if err != nil {
			SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("启动脚本执行失败: %v", err))
			return err
		}

		if output != "" {
			SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("脚本输出: %s", output))
		}
	}

	// 等待一下，然后检查进程是否启动
	SendDeployLog(p.Project.ID, "deploy", "info", "等待3秒后检查应用启动状态...")
	time.Sleep(3 * time.Second)

	// 检查应用是否成功启动
	checkCmd := fmt.Sprintf("cd %s && ps aux | grep -v grep | grep 'python app.py' || echo '未找到Python应用进程'", actualDeployPath)
	if checkOutput, err := ExecuteSSHCommand(client, checkCmd); err == nil {
		SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("进程检查结果: %s", strings.TrimSpace(checkOutput)))
	}

	SendDeployLog(p.Project.ID, "deploy", "success", "远程启动脚本执行完成")
	return nil
}

// installRemoteCondaDependencies 安装远程Conda依赖
func (p *PythonBuildStrategy) installRemoteCondaDependencies(client *ssh.Client, requirementsFile string) error {
	config := p.Project.BuildConfig.Config
	envName, _ := config["conda_env_name"].(string)

	if envName == "" {
		SendDeployLog(p.Project.ID, "deploy", "error", "未配置Conda环境名")
		return fmt.Errorf("未配置Conda环境名")
	}

	actualDeployPath := p.getActualDeployPath()
	SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("使用Conda环境: %s", envName))

	// 检查环境是否存在
	var installCmd string
	checkEnvCmd := fmt.Sprintf("conda env list | grep %s", envName)
	if _, err := ExecuteSSHCommand(client, checkEnvCmd); err == nil {
		// 环境存在，使用update
		installCmd = fmt.Sprintf("cd %s && conda env update -f %s --prune", actualDeployPath, requirementsFile)
		SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("更新现有Conda环境(含清理): %s", envName))
	} else {
		// 环境不存在，使用create
		installCmd = fmt.Sprintf("cd %s && conda env create -f %s", actualDeployPath, requirementsFile)
		SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("创建新Conda环境: %s", envName))
	}

	SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("执行远程Conda依赖安装命令: %s", installCmd))

	if output, err := ExecuteSSHCommand(client, installCmd); err != nil {
		SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("远程Conda依赖安装失败: %v\n%s", err, output))
		return err
	}

	SendDeployLog(p.Project.ID, "deploy", "success", "远程Conda依赖安装完成")
	return nil
}

// installRemotePipDependencies 安装远程Pip依赖
func (p *PythonBuildStrategy) installRemotePipDependencies(client *ssh.Client, requirementsFile string) error {
	config := p.Project.BuildConfig.Config
	envName, _ := config["conda_env_name"].(string)
	actualDeployPath := p.getActualDeployPath()

	var installCmd string
	if envName != "" {
		// 在指定conda环境中使用pip安装
		installCmd = fmt.Sprintf("cd %s && conda run -n %s pip install -r %s", actualDeployPath, envName, requirementsFile)
		SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("在Conda环境 %s 中使用pip安装依赖", envName))
	} else {
		// 使用系统pip
		installCmd = fmt.Sprintf("cd %s && pip3 install -r %s", actualDeployPath, requirementsFile)
		SendDeployLog(p.Project.ID, "deploy", "info", "使用系统pip安装依赖")
	}

	SendDeployLog(p.Project.ID, "deploy", "info", fmt.Sprintf("执行远程pip依赖安装命令: %s", installCmd))

	if output, err := ExecuteSSHCommand(client, installCmd); err != nil {
		SendDeployLog(p.Project.ID, "deploy", "error", fmt.Sprintf("远程pip依赖安装失败: %v\n%s", err, output))
		return err
	}

	SendDeployLog(p.Project.ID, "deploy", "success", "远程pip依赖安装完成")
	return nil
}

// executeSSHCommandWithTimeout 执行SSH命令并设置超时
func (p *PythonBuildStrategy) executeSSHCommandWithTimeout(client *ssh.Client, command string, timeout time.Duration) (string, error) {
	// 创建一个带超时的context
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 创建一个channel来接收结果
	resultChan := make(chan struct {
		output string
		err    error
	}, 1)

	// 在goroutine中执行SSH命令
	go func() {
		output, err := ExecuteSSHCommand(client, command)
		resultChan <- struct {
			output string
			err    error
		}{output, err}
	}()

	// 等待结果或超时
	select {
	case result := <-resultChan:
		return result.output, result.err
	case <-ctx.Done():
		return "", fmt.Errorf("命令执行超时 (%v)", timeout)
	}
}
