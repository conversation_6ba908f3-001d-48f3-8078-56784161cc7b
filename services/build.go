package services

import (
	"GoShip/models"
	"GoShip/utils"
	"fmt"
)

// BuildStrategy 部署流程策略接口
// 每种语言/类型的构建与部署都实现该接口
type BuildStrategy interface {
	Build(workDir string, buildNumber int) error
	Deploy(workDir string, buildNumber int) error
}

// Vue前端部署策略声明（实现应在vue_build_strategy.go）
type VueBuildStrategy struct {
	Project *models.Project
}

func (v *VueBuildStrategy) Build(workDir string, buildNumber int) error  { return nil }
func (v *VueBuildStrategy) Deploy(workDir string, buildNumber int) error { return nil }

// Python后端部署策略实现在 python_strategy.go 中

// 工厂方法
func NewBuildStrategy(project *models.Project) (BuildStrategy, error) {
	switch project.BuildConfig.Language {
	case "go":
		return &GoBuildStrategy{Project: project}, nil
	case "vue":
		return &VueBuildStrategy{Project: project}, nil
	case "python":
		return &PythonBuildStrategy{Project: project}, nil
	default:
		return nil, fmt.Errorf("不支持的项目语言类型: %s", project.BuildConfig.Language)
	}
}

// 顶层构建方法，统一流程
func BuildProject(project *models.Project, buildNumber int) error {
	// Python项目不需要构建阶段的源码拉取，直接调用构建策略
	if project.BuildConfig.Language == "python" {
		strategy, err := NewBuildStrategy(project)
		if err != nil {
			return err
		}
		return strategy.Build("", buildNumber)
	}

	// 编译型语言需要拉取源码进行构建
	// 1. 准备工作目录
	workDir := prepareWorkDir(project)
	if workDir == "" {
		return fmt.Errorf("工作目录准备失败")
	}
	defer func() { _ = removeWorkDir(workDir) }()

	// 2. 拉取代码
	gitSvc := NewGitService(project.GitURL, project.GitUsername, project.GitPassword)
	gitSvc.GitVersion = project.GitVersion
	if err := gitSvc.CloneOrPull(workDir); err != nil {
		SendDeployLog(project.ID, "git", "error", fmt.Sprintf("拉取代码失败: %v", err))
		return err
	}
	SendDeployLog(project.ID, "git", "success", "代码拉取完成")

	// 3. 选择策略并构建
	strategy, err := NewBuildStrategy(project)
	if err != nil {
		return err
	}
	return strategy.Build(workDir, buildNumber)
}

// 通用工作目录准备
func prepareWorkDir(project *models.Project) string {
	workDir := fmt.Sprintf("/tmp/goship_%d", project.ID)
	if _, err := utils.EnsureDir(workDir); err != nil {
		SendDeployLog(project.ID, "build", "error", fmt.Sprintf("创建工作目录失败: %v", err))
		return ""
	}
	return workDir
}

// 通用目录清理
func removeWorkDir(workDir string) error {
	return utils.RemoveDir(workDir)
}
