package services

import (
	"fmt"
	"net/http"
	"sync"

	"github.com/gorilla/websocket"
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 允许所有来源
	},
}

// ConnWithLock 封装 websocket.Conn 及其写锁
type ConnWithLock struct {
	Conn *websocket.Conn
	WriteLock sync.Mutex
}

// WebSocketManager 管理所有的 WebSocket 连接
type WebSocketManager struct {
	connections sync.Map // projectID -> []*ConnWithLock
	mu          sync.Mutex
}

var WSManager = NewWebSocketManager()

func NewWebSocketManager() *WebSocketManager {
	return &WebSocketManager{}
}

// AddConnection 添加新的 WebSocket 连接
func (m *WebSocketManager) AddConnection(projectID int64, conn *websocket.Conn) {
	m.mu.Lock()
	defer m.mu.Unlock()

	conns, _ := m.connections.LoadOrStore(projectID, make([]*ConnWithLock, 0))
	connList := conns.([]*ConnWithLock)
	connList = append(connList, &ConnWithLock{Conn: conn})
	m.connections.Store(projectID, connList)
}

// RemoveConnection 移除 WebSocket 连接
func (m *WebSocketManager) RemoveConnection(projectID int64, conn *websocket.Conn) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if conns, ok := m.connections.Load(projectID); ok {
		connList := conns.([]*ConnWithLock)
		for i, c := range connList {
			if c.Conn == conn {
				connList = append(connList[:i], connList[i+1:]...)
				break
			}
		}
		if len(connList) == 0 {
			m.connections.Delete(projectID)
		} else {
			m.connections.Store(projectID, connList)
		}
	}
}

// SendMessage 向指定项目的所有连接发送消息
func (m *WebSocketManager) SendMessage(projectID int64, message string) {
	if conns, ok := m.connections.Load(projectID); ok {
		connList := conns.([]*ConnWithLock)
		for _, c := range connList {
			c.WriteLock.Lock()
			err := c.Conn.WriteMessage(websocket.TextMessage, []byte(message))
			c.WriteLock.Unlock()
			if err != nil {
				fmt.Printf("发送消息失败: %v\n", err)
				c.Conn.Close()
				m.RemoveConnection(projectID, c.Conn)
			}
		}
	}
}

// SendLogMessage 发送格式化的日志消息
func (m *WebSocketManager) SendLogMessage(projectID int64, step, status, message string) {
	logMessage := fmt.Sprintf("[%s] %s: %s", step, status, message)
	// 控制台同步打印
	fmt.Printf("[项目ID:%d][%s][%s] %s\n", projectID, step, status, message)
	m.SendMessage(projectID, logMessage)
}
