package services

import (
	"GoShip/models"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"net"
	"strings"
	"time"

	"golang.org/x/crypto/ssh"
)

// TestSSHConnection 测试SSH连接
func TestSSHConnection(host *models.Host) (*models.HostConnectionTest, error) {
	startTime := time.Now()

	// 构建SSH配置
	config := &ssh.ClientConfig{
		User:            host.Username,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // 注意：生产环境应该验证主机密钥
		Timeout:         10 * time.Second,
	}

	// 根据认证方式配置
	switch host.AuthType {
	case "password":
		config.Auth = []ssh.AuthMethod{
			ssh.Password(host.Password),
		}
	case "private_key":
		signer, err := parsePrivateKey(host.PrivateKey)
		if err != nil {
			return &models.HostConnectionTest{
				Success: false,
				Message: fmt.Sprintf("解析私钥失败: %v", err),
				Latency: 0,
			}, nil
		}
		config.Auth = []ssh.AuthMethod{
			ssh.PublicKeys(signer),
		}
	default:
		return &models.HostConnectionTest{
			Success: false,
			Message: "不支持的认证方式: " + host.AuthType,
			Latency: 0,
		}, nil
	}

	// 连接SSH
	addr := fmt.Sprintf("%s:%d", host.IP, host.Port)
	client, err := ssh.Dial("tcp", addr, config)
	if err != nil {
		latency := time.Since(startTime).Milliseconds()
		return &models.HostConnectionTest{
			Success: false,
			Message: fmt.Sprintf("SSH连接失败: %v", err),
			Latency: latency,
		}, nil
	}
	defer client.Close()

	// 测试执行简单命令
	session, err := client.NewSession()
	if err != nil {
		latency := time.Since(startTime).Milliseconds()
		return &models.HostConnectionTest{
			Success: false,
			Message: fmt.Sprintf("创建SSH会话失败: %v", err),
			Latency: latency,
		}, nil
	}
	defer session.Close()

	// 执行简单的测试命令
	output, err := session.Output("echo 'GoShip SSH Test'")
	if err != nil {
		latency := time.Since(startTime).Milliseconds()
		return &models.HostConnectionTest{
			Success: false,
			Message: fmt.Sprintf("执行测试命令失败: %v", err),
			Latency: latency,
		}, nil
	}

	latency := time.Since(startTime).Milliseconds()

	// 验证命令输出
	expectedOutput := "GoShip SSH Test"
	if string(output) != expectedOutput+"\n" {
		return &models.HostConnectionTest{
			Success: false,
			Message: fmt.Sprintf("命令输出不符合预期，期望: %s，实际: %s", expectedOutput, string(output)),
			Latency: latency,
		}, nil
	}

	return &models.HostConnectionTest{
		Success: true,
		Message: "SSH连接测试成功",
		Latency: latency,
	}, nil
}

// parsePrivateKey 解析SSH私钥
func parsePrivateKey(privateKeyData string) (ssh.Signer, error) {
	// 解析PEM格式的私钥
	block, _ := pem.Decode([]byte(privateKeyData))
	if block == nil {
		return nil, fmt.Errorf("无法解析PEM格式的私钥")
	}

	// 尝试不同的私钥格式
	var privateKey interface{}
	var err error

	switch block.Type {
	case "RSA PRIVATE KEY":
		privateKey, err = x509.ParsePKCS1PrivateKey(block.Bytes)
	case "PRIVATE KEY":
		privateKey, err = x509.ParsePKCS8PrivateKey(block.Bytes)
	case "EC PRIVATE KEY":
		privateKey, err = x509.ParseECPrivateKey(block.Bytes)
	case "OPENSSH PRIVATE KEY":
		// OpenSSH格式的私钥
		return ssh.ParsePrivateKey([]byte(privateKeyData))
	default:
		return nil, fmt.Errorf("不支持的私钥类型: %s", block.Type)
	}

	if err != nil {
		return nil, fmt.Errorf("解析私钥失败: %v", err)
	}

	// 转换为SSH签名器
	signer, err := ssh.NewSignerFromKey(privateKey)
	if err != nil {
		return nil, fmt.Errorf("创建SSH签名器失败: %v", err)
	}

	return signer, nil
}

// CreateSSHClient 创建SSH客户端（用于部署）
func CreateSSHClient(host *models.Host) (*ssh.Client, error) {
	return CreateSSHClientWithTimeout(host, 30*time.Second)
}

// CreateSSHClientWithTimeout 创建带超时的SSH客户端
func CreateSSHClientWithTimeout(host *models.Host, timeout time.Duration) (*ssh.Client, error) {
	// 构建SSH配置
	config := &ssh.ClientConfig{
		User:            host.Username,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // 注意：生产环境应该验证主机密钥
		Timeout:         timeout,
	}

	// 根据认证方式配置
	switch host.AuthType {
	case "password":
		config.Auth = []ssh.AuthMethod{
			ssh.Password(host.Password),
		}
	case "private_key":
		signer, err := parsePrivateKey(host.PrivateKey)
		if err != nil {
			return nil, fmt.Errorf("解析私钥失败: %v", err)
		}
		config.Auth = []ssh.AuthMethod{
			ssh.PublicKeys(signer),
		}
	default:
		return nil, fmt.Errorf("不支持的认证方式: %s", host.AuthType)
	}

	// 连接SSH
	addr := fmt.Sprintf("%s:%d", host.IP, host.Port)
	client, err := ssh.Dial("tcp", addr, config)
	if err != nil {
		// 提供详细的SSH连接错误诊断
		errMsg := err.Error()

		if strings.Contains(errMsg, "connection refused") {
			return nil, fmt.Errorf("SSH连接被拒绝: SSH服务可能未启动或端口 %d 未开放", host.Port)
		}

		if strings.Contains(errMsg, "timeout") {
			return nil, fmt.Errorf("SSH连接超时: 网络不通或防火墙阻止了连接")
		}

		if strings.Contains(errMsg, "authentication") || strings.Contains(errMsg, "permission denied") {
			switch host.AuthType {
			case "password":
				return nil, fmt.Errorf("SSH认证失败: 用户名或密码错误 (用户: %s)", host.Username)
			case "private_key":
				return nil, fmt.Errorf("SSH认证失败: 私钥认证失败，请检查私钥格式和权限")
			default:
				return nil, fmt.Errorf("SSH认证失败: 不支持的认证方式 %s", host.AuthType)
			}
		}

		if strings.Contains(errMsg, "host key") {
			return nil, fmt.Errorf("SSH主机密钥验证失败: %v", err)
		}

		return nil, fmt.Errorf("SSH连接失败: %v (目标: %s)", err, addr)
	}

	return client, nil
}

// ExecuteSSHCommand 执行SSH命令
func ExecuteSSHCommand(client *ssh.Client, command string) (string, error) {
	session, err := client.NewSession()
	if err != nil {
		return "", fmt.Errorf("创建SSH会话失败: %v", err)
	}
	defer session.Close()

	output, err := session.CombinedOutput(command)
	if err != nil {
		return string(output), fmt.Errorf("执行命令失败: %v", err)
	}

	return string(output), nil
}

// CheckHostConnectivity 检查主机网络连通性
func CheckHostConnectivity(host *models.Host) error {
	addr := fmt.Sprintf("%s:%d", host.IP, host.Port)

	// 尝试TCP连接
	startTime := time.Now()
	conn, err := net.DialTimeout("tcp", addr, 10*time.Second)
	if err != nil {
		// 提供详细的错误诊断
		if netErr, ok := err.(net.Error); ok {
			if netErr.Timeout() {
				return fmt.Errorf("连接超时: 无法在10秒内连接到 %s，请检查网络连通性和防火墙设置", addr)
			}
		}

		// 检查是否是DNS解析问题
		if _, err := net.LookupHost(host.IP); err != nil {
			return fmt.Errorf("DNS解析失败: 无法解析主机地址 %s", host.IP)
		}

		return fmt.Errorf("网络连接失败: %v (目标: %s)", err, addr)
	}
	defer conn.Close()

	latency := time.Since(startTime)
	if latency > 5*time.Second {
		return fmt.Errorf("网络延迟过高: %v，可能影响文件传输性能", latency)
	}

	return nil
}

// ValidateHostConfiguration 验证主机配置
func ValidateHostConfiguration(host *models.Host) error {
	// 验证IP地址格式
	if net.ParseIP(host.IP) == nil {
		return fmt.Errorf("无效的IP地址: %s", host.IP)
	}

	// 验证端口范围
	if host.Port < 1 || host.Port > 65535 {
		return fmt.Errorf("端口号必须在1-65535之间")
	}

	// 验证认证配置
	switch host.AuthType {
	case "password":
		if host.Password == "" {
			return fmt.Errorf("密码认证方式下密码不能为空")
		}
	case "private_key":
		if host.PrivateKey == "" {
			return fmt.Errorf("私钥认证方式下私钥不能为空")
		}
		if err := models.ValidatePrivateKey(host.PrivateKey); err != nil {
			return fmt.Errorf("私钥验证失败: %v", err)
		}
	default:
		return fmt.Errorf("不支持的认证方式: %s", host.AuthType)
	}

	return nil
}
