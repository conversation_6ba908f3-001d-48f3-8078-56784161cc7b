package services

import (
	"fmt"
	"net/url"
	"os/exec"
	"strings"
	"os"
	"path/filepath"
)

type GitService struct {
	GitURL      string `json:"git_url"`
	GitUsername string `json:"git_username"`
	GitPassword string `json:"git_password"`
	GitVersion  string `json:"git_version"`
}

func NewGitService(gitURL, username, password string) *GitService {
	return &GitService{
		GitURL:      gitURL,
		GitUsername: username,
		GitPassword: password,
	}
}

// 构建带认证的 Git URL
func (s *GitService) getAuthURL() string {
	if s.GitUsername != "" && s.GitPassword != "" {
		parsedURL, err := url.Parse(s.GitURL)
		if err != nil {
			return s.GitURL
		}
		parsedURL.User = url.UserPassword(s.GitUsername, s.GitPassword)
		return parsedURL.String()
	}
	return s.GitURL
}

// GetBranches 获取仓库的分支列表
func (s *GitService) GetBranches() ([]string, error) {
	// 使用 ls-remote 获取远程分支
	cmd := exec.Command("git", "ls-remote", "--heads", s.getAuthURL())
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("获取分支列表失败: %v", err)
	}

	// 处理输出
	branches := []string{}
	for _, line := range strings.Split(string(output), "\n") {
		if line == "" {
			continue
		}
		// 格式: <hash> refs/heads/<branch>
		parts := strings.Split(line, "\t")
		if len(parts) != 2 {
			continue
		}
		// 提取分支名
		branch := strings.TrimPrefix(parts[1], "refs/heads/")
		branches = append(branches, branch)
	}

	return branches, nil
}

// GetTags 获取仓库的标签列表
func (s *GitService) GetTags() ([]string, error) {
	// 使用 ls-remote 获取远程标签
	cmd := exec.Command("git", "ls-remote", "--tags", s.getAuthURL())
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("获取标签列表失败: %v", err)
	}

	// 处理输出
	tags := []string{}
	for _, line := range strings.Split(string(output), "\n") {
		if line == "" {
			continue
		}
		// 格式: <hash> refs/tags/<tag>
		parts := strings.Split(line, "\t")
		if len(parts) != 2 {
			continue
		}
		// 提取标签名，排除 ^{} 结尾的引用
		tagRef := parts[1]
		if strings.HasSuffix(tagRef, "^{}") {
			continue
		}
		tag := strings.TrimPrefix(tagRef, "refs/tags/")
		tags = append(tags, tag)
	}

	return tags, nil
}

// CloneOrPull 克隆或更新仓库代码到指定目录
func (s *GitService) CloneOrPull(dir string) error {
	gitDir := filepath.Join(dir, ".git")
	
	// 检查目录是否已经存在Git仓库
	isRepo := false
	if _, err := os.Stat(gitDir); err == nil {
		isRepo = true
	}
	
	// 解析版本信息
	versionType := "branch"
	versionName := "master" // 默认分支
	
	if s.GitVersion != "" {
		parts := strings.SplitN(s.GitVersion, ":", 2)
		if len(parts) == 2 {
			versionType = parts[0]
			versionName = parts[1]
		}
	}
	
	if isRepo {
		// 已有仓库，执行 pull 操作
		cmd := exec.Command("git", "fetch", "--all")
		cmd.Dir = dir
		if err := cmd.Run(); err != nil {
			return fmt.Errorf("git fetch 失败: %v", err)
		}
		
		// 根据版本类型切换分支或标签
		var checkoutCmd *exec.Cmd
		if versionType == "tag" {
			checkoutCmd = exec.Command("git", "checkout", "tags/"+versionName, "-f")
		} else {
			checkoutCmd = exec.Command("git", "checkout", versionName, "-f")
			checkoutCmd.Dir = dir
			if err := checkoutCmd.Run(); err != nil {
				return fmt.Errorf("git checkout %s 失败: %v", versionName, err)
			}
			// 新增：强制同步本地分支到远程最新
			resetCmd := exec.Command("git", "reset", "--hard", "origin/"+versionName)
			resetCmd.Dir = dir
			if err := resetCmd.Run(); err != nil {
				return fmt.Errorf("git reset --hard 失败: %v", err)
			}
			return nil
		}
		checkoutCmd.Dir = dir
		if err := checkoutCmd.Run(); err != nil {
			return fmt.Errorf("git checkout %s 失败: %v", versionName, err)
		}
		return nil
	} else {
		// 创建目录(如果不存在)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建目录失败: %v", err)
		}
		
		// 没有仓库，执行 clone 操作
		cloneCmd := exec.Command("git", "clone", s.getAuthURL(), dir)
		if err := cloneCmd.Run(); err != nil {
			return fmt.Errorf("git clone 失败: %v", err)
		}
		
		// 根据版本类型切换分支或标签
		if versionType == "tag" {
			checkoutCmd := exec.Command("git", "checkout", "tags/"+versionName)
			checkoutCmd.Dir = dir
			if err := checkoutCmd.Run(); err != nil {
				return fmt.Errorf("git checkout tag %s 失败: %v", versionName, err)
			}
		} else if versionName != "master" {
			checkoutCmd := exec.Command("git", "checkout", versionName)
			checkoutCmd.Dir = dir
			if err := checkoutCmd.Run(); err != nil {
				return fmt.Errorf("git checkout branch %s 失败: %v", versionName, err)
			}
		}
		
		return nil
	}
}
