package services

import (
	"bufio"
	"bytes"
	"fmt"
	"io"
	"os"
	"os/exec"
	"regexp"
	"strconv"
	"strings"
)

// UploadAndReplace 通过scp上传为.new文件，上传后ssh原子替换为正式文件名并chmod +x
func UploadAndReplace(remoteUser, remotePass, remoteHost, remotePort, localFile, remoteDir, remoteFileName string) error {
	// 1. 上传为 .new 文件
	remotePath := fmt.Sprintf("%s/%s.new", remoteDir, remoteFileName)
	cmdStr := fmt.Sprintf("LANG=en_US.UTF-8 sshpass -p '%s' scp -P %s -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o LogLevel=ERROR '%s' %s@%s:'%s'", remotePass, remotePort, localFile, remoteUser, remoteHost, remotePath)
	cmd := exec.Command("sh", "-c", cmdStr)
	var scpOutput bytes.Buffer
	cmd.Stdout = &scpOutput
	cmd.Stderr = &scpOutput
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("scp上传失败: %v, 输出: %s", err, scpOutput.String())
	}
	// 2. 原子替换
	replaceCmd := fmt.Sprintf("mv '%s.new' '%s' && chmod +x '%s'", remoteFileName, remoteFileName, remoteFileName)
	sshCmd := fmt.Sprintf("cd %s && %s", remoteDir, replaceCmd)
	cmdReplace := exec.Command("sshpass", "-p", remotePass, "ssh", "-p", remotePort, "-o", "StrictHostKeyChecking=no", "-o", "UserKnownHostsFile=/dev/null", "-o", "LogLevel=ERROR", fmt.Sprintf("%s@%s", remoteUser, remoteHost), sshCmd)
	var replaceOutput bytes.Buffer
	cmdReplace.Stdout = &replaceOutput
	cmdReplace.Stderr = &replaceOutput
	if err := cmdReplace.Run(); err != nil {
		return fmt.Errorf("原子替换失败: %v, 输出: %s", err, replaceOutput.String())
	}
	return nil
}

// UploadWithProgress 使用rsync上传文件并显示进度
func UploadWithProgress(projectID int64, remoteUser, remotePass, remoteHost, remotePort, localFile, remoteDir, remoteFileName string) error {
	// 获取文件大小用于进度计算
	fileInfo, err := os.Stat(localFile)
	if err != nil {
		return fmt.Errorf("获取文件信息失败: %v", err)
	}
	fileSize := fileInfo.Size()

	WSManager.SendLogMessage(projectID, "upload", "info", fmt.Sprintf("开始上传文件: %s (大小: %.2f MB)", remoteFileName, float64(fileSize)/(1024*1024)))

	// 1. 使用rsync上传为.new文件
	remotePath := fmt.Sprintf("%s@%s:%s/%s.new", remoteUser, remoteHost, remoteDir, remoteFileName)

	// 构建rsync命令，使用ssh作为传输协议
	rsyncCmd := exec.Command("rsync",
		"--progress",
		"--partial",
		"--inplace",
		"-e", fmt.Sprintf("sshpass -p '%s' ssh -p %s -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o LogLevel=ERROR", remotePass, remotePort),
		localFile,
		remotePath,
	)

	// 创建管道来实时读取输出
	stdout, err := rsyncCmd.StdoutPipe()
	if err != nil {
		return fmt.Errorf("创建stdout管道失败: %v", err)
	}

	stderr, err := rsyncCmd.StderrPipe()
	if err != nil {
		return fmt.Errorf("创建stderr管道失败: %v", err)
	}

	// 启动命令
	if err := rsyncCmd.Start(); err != nil {
		return fmt.Errorf("启动rsync命令失败: %v", err)
	}

	// 创建进度解析的正则表达式
	progressRegex := regexp.MustCompile(`(\d+)%`)
	speedRegex := regexp.MustCompile(`(\d+(?:\.\d+)?[KMGT]?B/s)`)

	// 读取输出并解析进度
	go func() {
		scanner := bufio.NewScanner(io.MultiReader(stdout, stderr))
		lastProgress := 0
		for scanner.Scan() {
			line := scanner.Text()

			// 解析进度百分比
			if matches := progressRegex.FindStringSubmatch(line); len(matches) > 1 {
				if progress, err := strconv.Atoi(matches[1]); err == nil && progress != lastProgress {
					lastProgress = progress

					// 解析传输速度
					speed := "计算中..."
					if speedMatches := speedRegex.FindStringSubmatch(line); len(speedMatches) > 1 {
						speed = speedMatches[1]
					}

					WSManager.SendLogMessage(projectID, "upload", "info",
						fmt.Sprintf("上传进度: %d%% (速度: %s)", progress, speed))
				}
			} else if strings.Contains(line, "sent") && strings.Contains(line, "received") {
				// rsync完成时的统计信息
				WSManager.SendLogMessage(projectID, "upload", "info", fmt.Sprintf("传输统计: %s", line))
			}
		}
	}()

	// 等待命令完成
	if err := rsyncCmd.Wait(); err != nil {
		return fmt.Errorf("rsync上传失败: %v", err)
	}

	WSManager.SendLogMessage(projectID, "upload", "success", "文件上传完成")

	// 2. 原子替换
	WSManager.SendLogMessage(projectID, "upload", "info", "正在进行原子替换...")

	replaceCmd := fmt.Sprintf("mv '%s.new' '%s' && chmod +x '%s'", remoteFileName, remoteFileName, remoteFileName)
	sshCmd := fmt.Sprintf("cd %s && %s", remoteDir, replaceCmd)
	cmdReplace := exec.Command("sshpass", "-p", remotePass, "ssh", "-p", remotePort,
		"-o", "StrictHostKeyChecking=no",
		"-o", "UserKnownHostsFile=/dev/null",
		"-o", "LogLevel=ERROR",
		fmt.Sprintf("%s@%s", remoteUser, remoteHost), sshCmd)

	var replaceOutput bytes.Buffer
	cmdReplace.Stdout = &replaceOutput
	cmdReplace.Stderr = &replaceOutput

	if err := cmdReplace.Run(); err != nil {
		return fmt.Errorf("原子替换失败: %v, 输出: %s", err, replaceOutput.String())
	}

	WSManager.SendLogMessage(projectID, "upload", "success", "原子替换完成")
	return nil
}
